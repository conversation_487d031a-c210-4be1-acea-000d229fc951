"""
Point Cloud Dataset for VAE Training
====================================

This module implements dataset classes for loading and preprocessing point clouds
specifically for 3D VAE training. It handles MVTec 3D-AD dataset format and
provides data augmentation capabilities.

Author: Generated for IA-CRF project
"""

import os
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
import tifffile as tiff
from pathlib import Path
from typing import Tuple, List, Optional, Dict, Any
import cv2
import warnings
warnings.filterwarnings('ignore')


class PointCloudNormalizer:
    """
    Point cloud normalization utilities.
    """
    
    @staticmethod
    def normalize_point_cloud(pc: np.ndarray, method: str = 'unit_sphere') -> np.ndarray:
        """
        Normalize point cloud to standard coordinate space.
        
        Args:
            pc: Point cloud array of shape (H, W, 3) or (N, 3)
            method: Normalization method ('unit_sphere', 'unit_cube', 'zero_mean')
            
        Returns:
            Normalized point cloud
        """
        original_shape = pc.shape
        
        # Convert to (N, 3) format
        if len(pc.shape) == 3:
            pc_flat = pc.reshape(-1, 3)
        else:
            pc_flat = pc.copy()
            
        # Remove invalid points (zeros or NaN)
        valid_mask = ~np.any(np.isnan(pc_flat) | (pc_flat == 0).all(axis=1, keepdims=True), axis=1)
        if not np.any(valid_mask):
            # If all points are invalid, return zeros
            return np.zeros_like(pc)
            
        valid_points = pc_flat[valid_mask]
        
        if method == 'unit_sphere':
            # Center and scale to unit sphere
            centroid = np.mean(valid_points, axis=0)
            centered = valid_points - centroid
            max_dist = np.max(np.linalg.norm(centered, axis=1))
            if max_dist > 0:
                normalized_valid = centered / max_dist
            else:
                normalized_valid = centered
                
        elif method == 'unit_cube':
            # Scale to [-1, 1] cube
            min_vals = np.min(valid_points, axis=0)
            max_vals = np.max(valid_points, axis=0)
            ranges = max_vals - min_vals
            ranges[ranges == 0] = 1  # Avoid division by zero
            normalized_valid = 2 * (valid_points - min_vals) / ranges - 1
            
        elif method == 'zero_mean':
            # Zero mean, unit variance
            centroid = np.mean(valid_points, axis=0)
            std = np.std(valid_points, axis=0)
            std[std == 0] = 1  # Avoid division by zero
            normalized_valid = (valid_points - centroid) / std
            
        else:
            raise ValueError(f"Unknown normalization method: {method}")
            
        # Reconstruct full point cloud
        pc_normalized = np.zeros_like(pc_flat)
        pc_normalized[valid_mask] = normalized_valid
        
        # Restore original shape
        return pc_normalized.reshape(original_shape)


class PointCloudAugmentor:
    """
    Point cloud data augmentation utilities.
    """
    
    def __init__(self, 
                 rotation_range: float = 0.2,
                 scale_range: Tuple[float, float] = (0.8, 1.2),
                 jitter_std: float = 0.01,
                 dropout_ratio: float = 0.1):
        """
        Initialize augmentor.
        
        Args:
            rotation_range: Maximum rotation angle in radians
            scale_range: Range for random scaling
            jitter_std: Standard deviation for Gaussian jitter
            dropout_ratio: Ratio of points to randomly drop
        """
        self.rotation_range = rotation_range
        self.scale_range = scale_range
        self.jitter_std = jitter_std
        self.dropout_ratio = dropout_ratio
        
    def random_rotation(self, pc: np.ndarray) -> np.ndarray:
        """Apply random rotation around Z-axis."""
        angle = np.random.uniform(-self.rotation_range, self.rotation_range)
        cos_a, sin_a = np.cos(angle), np.sin(angle)
        rotation_matrix = np.array([
            [cos_a, -sin_a, 0],
            [sin_a, cos_a, 0],
            [0, 0, 1]
        ])
        
        original_shape = pc.shape
        pc_flat = pc.reshape(-1, 3)
        rotated = pc_flat @ rotation_matrix.T
        return rotated.reshape(original_shape)
        
    def random_scale(self, pc: np.ndarray) -> np.ndarray:
        """Apply random uniform scaling."""
        scale = np.random.uniform(self.scale_range[0], self.scale_range[1])
        return pc * scale
        
    def random_jitter(self, pc: np.ndarray) -> np.ndarray:
        """Apply random Gaussian jitter."""
        noise = np.random.normal(0, self.jitter_std, pc.shape)
        return pc + noise
        
    def random_dropout(self, pc: np.ndarray) -> np.ndarray:
        """Randomly drop some points."""
        if len(pc.shape) == 3:
            # For organized point clouds, set some points to zero
            mask = np.random.random(pc.shape[:2]) > self.dropout_ratio
            pc_aug = pc.copy()
            pc_aug[~mask] = 0
            return pc_aug
        else:
            # For unorganized point clouds, remove some points
            num_points = len(pc)
            num_keep = int(num_points * (1 - self.dropout_ratio))
            indices = np.random.choice(num_points, num_keep, replace=False)
            return pc[indices]
            
    def augment(self, pc: np.ndarray) -> np.ndarray:
        """Apply random augmentations."""
        pc_aug = pc.copy()
        
        # Apply augmentations with some probability
        if np.random.random() > 0.5:
            pc_aug = self.random_rotation(pc_aug)
        if np.random.random() > 0.5:
            pc_aug = self.random_scale(pc_aug)
        if np.random.random() > 0.5:
            pc_aug = self.random_jitter(pc_aug)
        if np.random.random() > 0.3:  # Lower probability for dropout
            pc_aug = self.random_dropout(pc_aug)
            
        return pc_aug


class MVTec3DNormalDataset(Dataset):
    """
    Dataset for loading normal samples from MVTec 3D-AD dataset for VAE training.
    Only loads normal (good) samples for learning geometric priors.
    """
    
    def __init__(self,
                 dataset_path: str,
                 class_name: str,
                 num_points: int = 2048,
                 normalize_method: str = 'unit_sphere',
                 augment: bool = True,
                 target_size: Optional[Tuple[int, int]] = None):
        """
        Initialize dataset.
        
        Args:
            dataset_path: Path to MVTec 3D-AD dataset
            class_name: Name of the object class (e.g., 'bagel', 'cable_gland')
            num_points: Number of points to sample from each point cloud
            normalize_method: Point cloud normalization method
            augment: Whether to apply data augmentation
            target_size: Target size for organized point clouds (H, W)
        """
        self.dataset_path = Path(dataset_path)
        self.class_name = class_name
        self.num_points = num_points
        self.normalize_method = normalize_method
        self.augment = augment
        self.target_size = target_size
        
        # Initialize normalizer and augmentor
        self.normalizer = PointCloudNormalizer()
        if augment:
            self.augmentor = PointCloudAugmentor()
            
        # Load file paths
        self.tiff_paths = self._load_normal_samples()
        
        print(f"Loaded {len(self.tiff_paths)} normal samples for class '{class_name}'")
        
    def _load_normal_samples(self) -> List[Path]:
        """Load paths to all normal (good) TIFF files."""
        class_path = self.dataset_path / self.class_name / "train" / "good" / "xyz"
        
        if not class_path.exists():
            raise ValueError(f"Dataset path not found: {class_path}")
            
        tiff_files = list(class_path.glob("*.tiff"))
        if not tiff_files:
            raise ValueError(f"No TIFF files found in {class_path}")
            
        return sorted(tiff_files)
        
    def _load_point_cloud(self, tiff_path: Path) -> np.ndarray:
        """Load point cloud from TIFF file."""
        try:
            pc = tiff.imread(str(tiff_path))
            if pc.ndim != 3 or pc.shape[2] != 3:
                raise ValueError(f"Expected 3D point cloud with shape (H, W, 3), got {pc.shape}")
            return pc
        except Exception as e:
            raise ValueError(f"Could not load point cloud from {tiff_path}: {e}")
            
    def _resize_point_cloud(self, pc: np.ndarray) -> np.ndarray:
        """Resize organized point cloud if target size is specified."""
        if self.target_size is None:
            return pc
            
        # Use torch interpolation for consistency
        torch_pc = torch.tensor(pc).permute(2, 0, 1).unsqueeze(0).float()
        resized = torch.nn.functional.interpolate(
            torch_pc,
            size=self.target_size,
            mode='nearest'
        )
        return resized.squeeze(0).permute(1, 2, 0).numpy()
        
    def _sample_points(self, pc: np.ndarray) -> np.ndarray:
        """Sample fixed number of points from organized point cloud."""
        # Convert to unorganized format
        pc_flat = pc.reshape(-1, 3)
        
        # Remove invalid points
        valid_mask = ~np.any(np.isnan(pc_flat) | (pc_flat == 0).all(axis=1, keepdims=True), axis=1)
        valid_points = pc_flat[valid_mask]
        
        if len(valid_points) == 0:
            # Return zeros if no valid points
            return np.zeros((self.num_points, 3))
            
        if len(valid_points) >= self.num_points:
            # Random sampling
            indices = np.random.choice(len(valid_points), self.num_points, replace=False)
            return valid_points[indices]
        else:
            # Upsample with replacement
            indices = np.random.choice(len(valid_points), self.num_points, replace=True)
            return valid_points[indices]
            
    def __len__(self) -> int:
        return len(self.tiff_paths)
        
    def __getitem__(self, idx: int) -> Dict[str, torch.Tensor]:
        """Get a single sample."""
        tiff_path = self.tiff_paths[idx]
        
        # Load point cloud
        pc = self._load_point_cloud(tiff_path)
        
        # Resize if needed
        pc = self._resize_point_cloud(pc)
        
        # Store original for reconstruction loss
        pc_original = pc.copy()
        
        # Apply augmentation
        if self.augment:
            pc = self.augmentor.augment(pc)
            
        # Normalize
        pc_normalized = self.normalizer.normalize_point_cloud(pc, self.normalize_method)
        pc_original_normalized = self.normalizer.normalize_point_cloud(pc_original, self.normalize_method)
        
        # Sample points
        pc_sampled = self._sample_points(pc_normalized)
        pc_original_sampled = self._sample_points(pc_original_normalized)

        # Check for NaN or infinite values
        if np.isnan(pc_sampled).any() or np.isinf(pc_sampled).any():
            print(f"Warning: NaN/Inf detected in sampled point cloud from {tiff_path}")
            pc_sampled = np.zeros((self.num_points, 3))

        if np.isnan(pc_original_sampled).any() or np.isinf(pc_original_sampled).any():
            print(f"Warning: NaN/Inf detected in original sampled point cloud from {tiff_path}")
            pc_original_sampled = np.zeros((self.num_points, 3))

        return {
            'point_cloud': torch.tensor(pc_sampled, dtype=torch.float32),
            'point_cloud_original': torch.tensor(pc_original_sampled, dtype=torch.float32),
            'file_path': str(tiff_path)
        }


def get_vae_dataloader(dataset_path: str,
                      class_name: str,
                      batch_size: int = 8,
                      num_workers: int = 4,
                      shuffle: bool = True,
                      num_points: int = 2048,
                      normalize_method: str = 'unit_sphere',
                      augment: bool = True,
                      target_size: Optional[Tuple[int, int]] = (224, 224)) -> DataLoader:
    """
    Create DataLoader for VAE training.
    
    Args:
        dataset_path: Path to MVTec 3D-AD dataset
        class_name: Object class name
        batch_size: Batch size for training
        num_workers: Number of worker processes
        shuffle: Whether to shuffle data
        num_points: Number of points per point cloud
        normalize_method: Normalization method
        augment: Whether to apply augmentation
        target_size: Target size for point clouds
        
    Returns:
        DataLoader for VAE training
    """
    dataset = MVTec3DNormalDataset(
        dataset_path=dataset_path,
        class_name=class_name,
        num_points=num_points,
        normalize_method=normalize_method,
        augment=augment,
        target_size=target_size
    )
    
    dataloader = DataLoader(
        dataset=dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=True  # Ensure consistent batch sizes
    )
    
    return dataloader
