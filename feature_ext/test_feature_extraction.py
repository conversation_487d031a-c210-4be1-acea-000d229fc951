#!/usr/bin/env python3
"""
Test Script for 3D Feature Extraction
=====================================

This script tests the feature extraction functionality on a small sample
to ensure everything works correctly before processing entire datasets.

Usage:
    python model/test_feature_extraction.py
"""

import os
import sys
import numpy as np
import tempfile
from pathlib import Path

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from point_cloud_feature_extractor import PointCloudFeatureExtractor
from feature_visualizer import FeatureVisualizer


def create_synthetic_point_cloud(height=100, width=100):
    """
    Create a synthetic point cloud for testing.
    
    Args:
        height (int): Height of the organized point cloud
        width (int): Width of the organized point cloud
        
    Returns:
        np.ndarray: Synthetic point cloud of shape (height, width, 3)
    """
    print("Creating synthetic point cloud for testing...")
    
    # Create a simple curved surface (paraboloid)
    x = np.linspace(-1, 1, width)
    y = np.linspace(-1, 1, height)
    X, Y = np.meshgrid(x, y)
    Z = 0.5 * (X**2 + Y**2)  # Paraboloid surface
    
    # Add some noise
    Z += np.random.normal(0, 0.01, Z.shape)
    
    # Stack to create point cloud
    pc = np.stack([X, Y, Z], axis=-1)
    
    # Set some points to zero to simulate missing data
    mask = np.random.random((height, width)) > 0.1  # 90% valid points
    pc[~mask] = 0
    
    return pc


def test_feature_extraction():
    """Test the feature extraction pipeline."""
    print("=" * 60)
    print("TESTING 3D FEATURE EXTRACTION")
    print("=" * 60)
    
    # Create synthetic data
    pc = create_synthetic_point_cloud(50, 50)  # Small size for quick testing
    print(f"Created synthetic point cloud with shape: {pc.shape}")
    
    # Initialize extractor
    extractor = PointCloudFeatureExtractor(k_neighbors=20)  # Smaller K for testing
    print("Initialized feature extractor")
    
    # Extract features
    print("Extracting features...")
    features = extractor.extract_features(pc)
    print(f"Extracted features with shape: {features.shape}")
    
    # Validate feature dimensions
    assert features.shape == (*pc.shape[:2], 8), f"Expected shape {(*pc.shape[:2], 8)}, got {features.shape}"
    print("✓ Feature dimensions are correct")
    
    # Check that XYZ coordinates match original (for valid points)
    valid_mask = np.any(pc != 0, axis=2)
    original_xyz = pc[valid_mask]
    extracted_xyz = features[valid_mask][:, :3]
    
    if np.allclose(original_xyz, extracted_xyz, atol=1e-6):
        print("✓ XYZ coordinates match original point cloud")
    else:
        print("⚠ XYZ coordinates don't match exactly (this might be expected)")
    
    # Check normal vectors (should be unit vectors)
    normals = features[valid_mask][:, 3:6]
    normal_lengths = np.linalg.norm(normals, axis=1)
    valid_normals = normal_lengths > 0
    
    if valid_normals.any():
        unit_normals = normal_lengths[valid_normals]
        if np.allclose(unit_normals, 1.0, atol=1e-3):
            print("✓ Normal vectors are unit vectors")
        else:
            print(f"⚠ Normal vectors are not unit vectors (mean length: {np.mean(unit_normals):.4f})")
    else:
        print("⚠ No valid normal vectors found")
    
    # Check curvature values (should be finite)
    mean_curvatures = features[valid_mask][:, 6]
    gaussian_curvatures = features[valid_mask][:, 7]
    
    if np.all(np.isfinite(mean_curvatures)) and np.all(np.isfinite(gaussian_curvatures)):
        print("✓ Curvature values are finite")
    else:
        print("⚠ Some curvature values are not finite")
    
    print(f"Mean curvature range: [{np.min(mean_curvatures):.6f}, {np.max(mean_curvatures):.6f}]")
    print(f"Gaussian curvature range: [{np.min(gaussian_curvatures):.6f}, {np.max(gaussian_curvatures):.6f}]")
    
    return features


def test_visualization(features):
    """Test the visualization functionality."""
    print("\n" + "=" * 60)
    print("TESTING FEATURE VISUALIZATION")
    print("=" * 60)
    
    # Initialize visualizer
    visualizer = FeatureVisualizer()
    print("Initialized feature visualizer")
    
    # Generate statistics
    stats = visualizer.generate_feature_statistics(features)
    print("Generated feature statistics:")
    visualizer.print_statistics(stats)
    
    # Test that statistics are reasonable
    if "error" not in stats:
        assert stats["valid_points"] > 0, "No valid points found"
        assert 0 <= stats["valid_ratio"] <= 1, "Invalid valid ratio"
        print("✓ Statistics are reasonable")
    else:
        print(f"⚠ Error in statistics: {stats['error']}")
    
    return stats


def test_file_io():
    """Test file input/output operations."""
    print("\n" + "=" * 60)
    print("TESTING FILE I/O OPERATIONS")
    print("=" * 60)
    
    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create synthetic data and save as TIFF
        pc = create_synthetic_point_cloud(30, 30)
        tiff_path = temp_path / "test_pointcloud.tiff"
        
        # Save as TIFF (simulate the format used in datasets)
        import tifffile as tiff
        tiff.imsave(tiff_path, pc.astype(np.float32))
        print(f"Saved test point cloud to: {tiff_path}")
        
        # Test loading
        extractor = PointCloudFeatureExtractor(k_neighbors=15)
        loaded_pc = extractor.load_point_cloud(tiff_path)
        
        if loaded_pc is not None:
            print("✓ Successfully loaded point cloud from TIFF")
            assert np.allclose(pc, loaded_pc, atol=1e-6), "Loaded point cloud doesn't match original"
            print("✓ Loaded point cloud matches original")
        else:
            print("⚠ Failed to load point cloud from TIFF")
            return False
        
        # Test feature extraction and saving
        features_dir = temp_path / "features"
        success = extractor.process_single_file(tiff_path, features_dir)
        
        if success:
            print("✓ Successfully processed single file")
            
            # Check if feature file was created
            feature_file = features_dir / "test_pointcloud_features.npy"
            if feature_file.exists():
                print("✓ Feature file was created")
                
                # Load and validate features
                features = np.load(feature_file)
                if features.shape == (*pc.shape[:2], 8):
                    print("✓ Saved features have correct shape")
                else:
                    print(f"⚠ Saved features have incorrect shape: {features.shape}")
                    return False
            else:
                print("⚠ Feature file was not created")
                return False
        else:
            print("⚠ Failed to process single file")
            return False
    
    return True


def main():
    """Run all tests."""
    print("Starting 3D Feature Extraction Tests...")
    
    try:
        # Test 1: Basic feature extraction
        features = test_feature_extraction()
        
        # Test 2: Visualization
        test_visualization(features)
        
        # Test 3: File I/O
        io_success = test_file_io()
        
        # Summary
        print("\n" + "=" * 60)
        print("TEST SUMMARY")
        print("=" * 60)
        print("✓ Feature extraction: PASSED")
        print("✓ Visualization: PASSED")
        print(f"{'✓' if io_success else '⚠'} File I/O: {'PASSED' if io_success else 'FAILED'}")
        
        if io_success:
            print("\n🎉 All tests passed! The feature extraction system is working correctly.")
            print("\nYou can now run the full extraction on your datasets:")
            print("  python model/extract_features.py --extract_all")
        else:
            print("\n⚠ Some tests failed. Please check the error messages above.")
            
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
