"""
Point Cloud Feature Extraction Before/After Processing Visualizer
================================================================

This module provides comprehensive visualization functionality for point cloud feature extraction process, including:
1. Original point cloud visualization
2. Processed point cloud (normals, curvature) visualization
3. Before/after feature extraction comparison
4. Interactive 3D visualization
5. Feature statistical analysis

Compatible with FastPointCloudFeatureExtractor output format:
- 7 features: [x, y, z, nx, ny, nz, curvature]

Author: Generated for IA-CRF project
"""

import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import tifffile as tiff
from pathlib import Path
import argparse
import os
import sys
from typing import Tuple, Optional, Dict, Any
import warnings
warnings.filterwarnings('ignore')

# Add current directory to path for importing feature extractor
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from point_cloud_feature_extractor_fast import FastPointCloudFeatureExtractor
except ImportError:
    print("Warning: FastPointCloudFeatureExtractor not found. Some features may not work.")
    FastPointCloudFeatureExtractor = None


class PointCloudProcessingVisualizer:
    """Complete visualizer for point cloud feature extraction before/after processing states"""

    def __init__(self, figsize=(15, 10)):
        """
        Initialize the visualizer

        Args:
            figsize (tuple): Figure size (width, height)
        """
        self.figsize = figsize
        self.feature_extractor = None
        if FastPointCloudFeatureExtractor:
            self.feature_extractor = FastPointCloudFeatureExtractor(k_neighbors=30, batch_size=500)
    
    def load_point_cloud(self, tiff_path: str) -> Optional[np.ndarray]:
        """
        Load point cloud from TIFF file

        Args:
            tiff_path (str): TIFF file path

        Returns:
            np.ndarray: Point cloud array with shape (H, W, 3)
        """
        try:
            pc = tiff.imread(tiff_path)
            if pc.ndim != 3 or pc.shape[2] != 3:
                raise ValueError(f"Expected 3D point cloud shape (H, W, 3), got {pc.shape}")
            return pc
        except Exception as e:
            print(f"Error loading point cloud file {tiff_path}: {e}")
            return None
    
    def extract_valid_points(self, organized_pc: np.ndarray) -> Tuple[np.ndarray, np.ndarray, Tuple[int, int]]:
        """
        Extract valid points from organized point cloud

        Args:
            organized_pc (np.ndarray): Organized point cloud (H, W, 3)

        Returns:
            tuple: (valid_points, valid_indices, original_shape)
        """
        original_shape = organized_pc.shape[:2]
        unorganized_pc = organized_pc.reshape(-1, 3)

        # Find valid points (non-zero points)
        valid_mask = np.any(unorganized_pc != 0, axis=1)
        valid_indices = np.where(valid_mask)[0]
        valid_points = unorganized_pc[valid_mask]

        return valid_points, valid_indices, original_shape
    
    def create_synthetic_point_cloud(self, height: int = 100, width: int = 100,
                                   noise_level: float = 0.01) -> np.ndarray:
        """
        Create synthetic point cloud for testing

        Args:
            height (int): Height
            width (int): Width
            noise_level (float): Noise level

        Returns:
            np.ndarray: Synthetic point cloud (H, W, 3)
        """
        # Create grid
        x = np.linspace(-1, 1, width)
        y = np.linspace(-1, 1, height)
        X, Y = np.meshgrid(x, y)

        # Create wave surface
        Z = 0.3 * np.sin(3 * np.pi * X) * np.cos(3 * np.pi * Y)
        Z += 0.1 * np.sin(10 * np.pi * X) * np.sin(10 * np.pi * Y)

        # Add noise
        Z += noise_level * np.random.randn(height, width)

        # Combine into point cloud
        pc = np.stack([X, Y, Z], axis=2)

        # Randomly remove some points to simulate real data
        mask = np.random.rand(height, width) > 0.1
        pc[~mask] = 0

        return pc
    
    def visualize_original_pointcloud(self, organized_pc: np.ndarray, title: str = "Original Point Cloud",
                                    sample_rate: float = 0.1, ax=None) -> None:
        """
        Visualize original point cloud

        Args:
            organized_pc (np.ndarray): Organized point cloud (H, W, 3)
            title (str): Plot title
            sample_rate (float): Sampling rate
            ax: matplotlib 3D axis object
        """
        valid_points, _, _ = self.extract_valid_points(organized_pc)

        if len(valid_points) == 0:
            print("No valid points found for visualization")
            return

        # Sample points for visualization
        n_sample = int(len(valid_points) * sample_rate)
        if n_sample > 0:
            indices = np.random.choice(len(valid_points), n_sample, replace=False)
            sample_points = valid_points[indices]
        else:
            sample_points = valid_points

        if ax is None:
            fig = plt.figure(figsize=(10, 8))
            ax = fig.add_subplot(111, projection='3d')

        # Plot point cloud
        scatter = ax.scatter(sample_points[:, 0], sample_points[:, 1], sample_points[:, 2],
                           c=sample_points[:, 2], cmap='viridis', s=1, alpha=0.6)

        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_zlabel('Z')
        ax.set_title(title)

        # Add colorbar
        if ax.figure:
            plt.colorbar(scatter, ax=ax, shrink=0.5, aspect=20, label='Z Value')

        print(f"Displayed {len(sample_points)} points (total {len(valid_points)} valid points)")
    
    def visualize_processed_pointcloud(self, features: np.ndarray, title: str = "Processed Point Cloud",
                                     sample_rate: float = 0.1, normal_scale: float = 0.01,
                                     show_normals: bool = True, show_curvature: bool = True, ax=None) -> None:
        """
        Visualize processed point cloud (with normals and curvature)

        Args:
            features (np.ndarray): Feature array (H, W, 7) [x, y, z, nx, ny, nz, curvature]
            title (str): Plot title
            sample_rate (float): Sampling rate
            normal_scale (float): Normal scaling factor
            show_normals (bool): Whether to show normals
            show_curvature (bool): Whether to color by curvature
            ax: matplotlib 3D axis object
        """
        # Extract valid points and features
        features_flat = features.reshape(-1, 7)
        valid_mask = np.any(features_flat[:, :3] != 0, axis=1)
        valid_features = features_flat[valid_mask]

        if len(valid_features) == 0:
            print("No valid features found for visualization")
            return

        points = valid_features[:, :3]
        normals = valid_features[:, 3:6]
        curvatures = valid_features[:, 6]

        # Sample points for visualization
        n_sample = int(len(points) * sample_rate)
        if n_sample > 0:
            indices = np.random.choice(len(points), n_sample, replace=False)
            sample_points = points[indices]
            sample_normals = normals[indices]
            sample_curvatures = curvatures[indices]
        else:
            sample_points = points
            sample_normals = normals
            sample_curvatures = curvatures

        if ax is None:
            fig = plt.figure(figsize=(12, 8))
            ax = fig.add_subplot(111, projection='3d')

        # Choose color mapping
        if show_curvature:
            colors = sample_curvatures
            cmap = 'plasma'
            label = 'Curvature'
        else:
            colors = sample_points[:, 2]
            cmap = 'viridis'
            label = 'Z Value'

        # Plot point cloud
        scatter = ax.scatter(sample_points[:, 0], sample_points[:, 1], sample_points[:, 2],
                           c=colors, cmap=cmap, s=2, alpha=0.7)

        # Plot normals
        if show_normals:
            # Only show some normals to avoid overcrowding
            normal_step = max(1, len(sample_points) // 200)
            for i in range(0, len(sample_points), normal_step):
                start = sample_points[i]
                end = start + sample_normals[i] * normal_scale
                ax.plot([start[0], end[0]], [start[1], end[1]], [start[2], end[2]],
                       'r-', alpha=0.8, linewidth=1)

        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_zlabel('Z')
        ax.set_title(title)

        # Add colorbar
        if ax.figure:
            plt.colorbar(scatter, ax=ax, shrink=0.5, aspect=20, label=label)

        print(f"Displayed {len(sample_points)} points (total {len(points)} valid points)")
        if show_normals:
            print(f"Displayed {len(range(0, len(sample_points), normal_step))} normal vectors")

    def compare_before_after(self, organized_pc: np.ndarray, features: np.ndarray,
                           sample_rate: float = 0.1, normal_scale: float = 0.01) -> None:
        """
        Compare point cloud states before and after processing

        Args:
            organized_pc (np.ndarray): Original point cloud (H, W, 3)
            features (np.ndarray): Processed features (H, W, 7)
            sample_rate (float): Sampling rate
            normal_scale (float): Normal scaling factor
        """
        fig = plt.figure(figsize=(20, 8))

        # Original point cloud
        ax1 = fig.add_subplot(131, projection='3d')
        self.visualize_original_pointcloud(organized_pc, "Original Point Cloud", sample_rate, ax1)

        # Processed point cloud (show curvature)
        ax2 = fig.add_subplot(132, projection='3d')
        self.visualize_processed_pointcloud(features, "Processed Point Cloud (Curvature)",
                                          sample_rate, normal_scale,
                                          show_normals=False, show_curvature=True, ax=ax2)

        # Processed point cloud (show normals)
        ax3 = fig.add_subplot(133, projection='3d')
        self.visualize_processed_pointcloud(features, "Processed Point Cloud (Normals)",
                                          sample_rate, normal_scale,
                                          show_normals=True, show_curvature=False, ax=ax3)

        plt.tight_layout()
        plt.show()

    def analyze_features(self, features: np.ndarray) -> Dict[str, Any]:
        """
        Analyze extracted feature statistics

        Args:
            features (np.ndarray): Feature array (H, W, 7)

        Returns:
            dict: Feature statistics
        """
        # Extract valid features
        features_flat = features.reshape(-1, 7)
        valid_mask = np.any(features_flat[:, :3] != 0, axis=1)
        valid_features = features_flat[valid_mask]

        if len(valid_features) == 0:
            return {"error": "No valid features found"}

        points = valid_features[:, :3]
        normals = valid_features[:, 3:6]
        curvatures = valid_features[:, 6]

        stats = {
            "total_points": len(valid_features),
            "valid_point_ratio": len(valid_features) / len(features_flat),
            "point_cloud_range": {
                "X": (np.min(points[:, 0]), np.max(points[:, 0])),
                "Y": (np.min(points[:, 1]), np.max(points[:, 1])),
                "Z": (np.min(points[:, 2]), np.max(points[:, 2]))
            },
            "curvature_stats": {
                "min": np.min(curvatures),
                "max": np.max(curvatures),
                "mean": np.mean(curvatures),
                "std": np.std(curvatures),
                "median": np.median(curvatures)
            },
            "normal_stats": {
                "length_mean": np.mean(np.linalg.norm(normals, axis=1)),
                "length_std": np.std(np.linalg.norm(normals, axis=1))
            }
        }

        return stats

    def print_feature_analysis(self, stats: Dict[str, Any]) -> None:
        """
        Print feature analysis results

        Args:
            stats (dict): Feature statistics
        """
        if "error" in stats:
            print(f"Error: {stats['error']}")
            return

        print("=" * 60)
        print("Feature Analysis Results")
        print("=" * 60)
        print(f"Total points: {stats['total_points']:,}")
        print(f"Valid point ratio: {stats['valid_point_ratio']:.2%}")

        print("\nPoint cloud spatial range:")
        for axis, (min_val, max_val) in stats["point_cloud_range"].items():
            print(f"  {axis}: [{min_val:.4f}, {max_val:.4f}] (range: {max_val-min_val:.4f})")

        print("\nCurvature statistics:")
        curvature_stats = stats["curvature_stats"]
        for key, value in curvature_stats.items():
            print(f"  {key}: {value:.6f}")

        print("\nNormal statistics:")
        normal_stats = stats["normal_stats"]
        for key, value in normal_stats.items():
            print(f"  {key}: {value:.6f}")

    def visualize_curvature_distribution(self, features: np.ndarray) -> None:
        """
        Visualize curvature distribution

        Args:
            features (np.ndarray): Feature array (H, W, 7)
        """
        # Extract valid curvatures
        features_flat = features.reshape(-1, 7)
        valid_mask = np.any(features_flat[:, :3] != 0, axis=1)
        valid_curvatures = features_flat[valid_mask, 6]

        if len(valid_curvatures) == 0:
            print("No valid curvature data found")
            return

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))

        # Curvature histogram
        ax1.hist(valid_curvatures, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.set_xlabel('Curvature Value')
        ax1.set_ylabel('Frequency')
        ax1.set_title('Curvature Distribution Histogram')
        ax1.grid(True, alpha=0.3)

        # Curvature box plot
        ax2.boxplot(valid_curvatures, vert=True)
        ax2.set_ylabel('Curvature Value')
        ax2.set_title('Curvature Distribution Box Plot')
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.show()

        # Print statistics
        print(f"Curvature statistics:")
        print(f"  Total: {len(valid_curvatures):,}")
        print(f"  Range: [{np.min(valid_curvatures):.6f}, {np.max(valid_curvatures):.6f}]")
        print(f"  Mean: {np.mean(valid_curvatures):.6f}")
        print(f"  Std: {np.std(valid_curvatures):.6f}")
        print(f"  Median: {np.median(valid_curvatures):.6f}")

    def process_and_visualize(self, tiff_path: str, sample_rate: float = 0.1,
                            normal_scale: float = 0.01, save_features: bool = False) -> Optional[np.ndarray]:
        """
        Complete processing and visualization pipeline

        Args:
            tiff_path (str): TIFF file path
            sample_rate (float): Visualization sampling rate
            normal_scale (float): Normal scaling factor
            save_features (bool): Whether to save extracted features

        Returns:
            np.ndarray: Extracted feature array
        """
        print(f"Starting to process point cloud file: {tiff_path}")

        # Load original point cloud
        organized_pc = self.load_point_cloud(tiff_path)
        if organized_pc is None:
            return None

        print(f"Successfully loaded point cloud, shape: {organized_pc.shape}")

        # Extract features
        if self.feature_extractor is None:
            print("Error: Feature extractor not initialized")
            return None

        print("Starting feature extraction...")
        features = self.feature_extractor.extract_features(organized_pc)
        print(f"Feature extraction completed, shape: {features.shape}")

        # Analyze features
        stats = self.analyze_features(features)
        self.print_feature_analysis(stats)

        # Visualization comparison
        print("\nGenerating comparison visualization...")
        self.compare_before_after(organized_pc, features, sample_rate, normal_scale)

        # Visualize curvature distribution
        print("\nGenerating curvature distribution plot...")
        self.visualize_curvature_distribution(features)

        # Save features (optional)
        if save_features:
            output_path = Path(tiff_path).with_suffix('.npy')
            np.save(output_path, features)
            print(f"Features saved to: {output_path}")

        return features

    def demo_with_synthetic_data(self, height: int = 80, width: int = 80) -> None:
        """
        Demo using synthetic data

        Args:
            height (int): Synthetic point cloud height
            width (int): Synthetic point cloud width
        """
        print("=" * 60)
        print("Demo with Synthetic Data")
        print("=" * 60)

        # Create synthetic point cloud
        print("Creating synthetic point cloud...")
        organized_pc = self.create_synthetic_point_cloud(height, width)
        print(f"Synthetic point cloud shape: {organized_pc.shape}")

        # Extract features
        if self.feature_extractor is None:
            print("Error: Feature extractor not initialized")
            return

        print("Starting feature extraction...")
        features = self.feature_extractor.extract_features(organized_pc)
        print(f"Feature extraction completed, shape: {features.shape}")

        # Analysis and visualization
        stats = self.analyze_features(features)
        self.print_feature_analysis(stats)

        print("\nGenerating comparison visualization...")
        self.compare_before_after(organized_pc, features, sample_rate=0.2, normal_scale=0.02)

        print("\nGenerating curvature distribution plot...")
        self.visualize_curvature_distribution(features)


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Point Cloud Feature Extraction Before/After Processing Visualizer")
    parser.add_argument("--tiff_path", type=str, help="TIFF point cloud file path")
    parser.add_argument("--sample_rate", type=float, default=0.1,
                       help="Visualization sampling rate (default: 0.1)")
    parser.add_argument("--normal_scale", type=float, default=0.01,
                       help="Normal vector scaling factor (default: 0.01)")
    parser.add_argument("--save_features", action="store_true",
                       help="Whether to save extracted features")
    parser.add_argument("--demo", action="store_true",
                       help="Run demo with synthetic data")
    parser.add_argument("--demo_size", type=int, default=80,
                       help="Demo synthetic point cloud size (default: 80)")

    args = parser.parse_args()

    # Initialize visualizer
    visualizer = PointCloudProcessingVisualizer()

    if args.demo:
        # Run demo
        visualizer.demo_with_synthetic_data(args.demo_size, args.demo_size)
    elif args.tiff_path:
        # Process specified file
        if not os.path.exists(args.tiff_path):
            print(f"Error: File does not exist {args.tiff_path}")
            return

        features = visualizer.process_and_visualize(
            args.tiff_path,
            args.sample_rate,
            args.normal_scale,
            args.save_features
        )

        if features is not None:
            print("Processing completed!")
        else:
            print("Processing failed!")
    else:
        print("Error: Please specify --tiff_path or use --demo")
        parser.print_help()


if __name__ == "__main__":
    main()
