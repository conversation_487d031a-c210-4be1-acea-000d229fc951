
"""
Fast 3D Point Cloud Physical Feature Extractor
==============================================

This module provides optimized feature extraction for 3D point clouds
using vectorized operations and efficient algorithms.

Features extracted:
- Surface normals using PCA
- Surface curvature (variance-based)
- Original XYZ coordinates

Total features: 7 (x, y, z, nx, ny, nz, curvature)

输出格式兼容性:
- 特征保存路径: dataset/category/phase/anomaly_type/features/filename.npy
- 特征文件格式: (H*W, 7) numpy array，与原始项目的 enhanced_pointcloud 格式一致
- 特征文件命名: filename.npy (不含 _features 后缀)

Author: Generated for IA-CRF project
"""

import os
import numpy as np
import tifffile as tiff
from pathlib import Path
import argparse
import warnings
from tqdm import tqdm
import torch
import cv2
warnings.filterwarnings('ignore')


class FastPointCloudFeatureExtractor:
    """
    Fast feature extraction from 3D point clouds using vectorized operations.
    Supports resizing to 224x224x3 for efficient processing.
    """

    def __init__(self, k_neighbors=50, batch_size=1000, target_size=(224, 224), enable_resize=True):
        """
        Initialize the feature extractor.

        Args:
            k_neighbors (int): Number of neighbors for KNN search (default: 50)
            batch_size (int): Batch size for processing (default: 1000)
            target_size (tuple): Target size for resizing (height, width) (default: (224, 224))
            enable_resize (bool): Whether to resize point clouds before processing (default: True)
        """
        self.k_neighbors = k_neighbors
        self.batch_size = batch_size
        self.target_size = target_size
        self.enable_resize = enable_resize
        
    def load_point_cloud(self, tiff_path):
        """Load point cloud from TIFF file."""
        try:
            pc = tiff.imread(tiff_path)
            if pc.ndim != 3 or pc.shape[2] != 3:
                raise ValueError(f"Expected 3D point cloud with shape (H, W, 3), got {pc.shape}")
            return pc
        except Exception as e:
            print(f"Error loading {tiff_path}: {e}")
            return None

    def resize_organized_pc(self, organized_pc, target_height=224, target_width=224, method='torch'):
        """
        Resize organized point cloud to target size.

        Args:
            organized_pc (np.ndarray): Input point cloud of shape (H, W, 3)
            target_height (int): Target height
            target_width (int): Target width
            method (str): Resize method ('torch' or 'cv2')

        Returns:
            np.ndarray: Resized point cloud of shape (target_height, target_width, 3)
        """
        if method == 'torch':
            # Use PyTorch interpolation (same as in crossmodal-feature-mapping)
            torch_organized_pc = torch.tensor(organized_pc).permute(2, 0, 1).unsqueeze(dim=0).float()
            torch_resized_organized_pc = torch.nn.functional.interpolate(
                torch_organized_pc,
                size=(target_height, target_width),
                mode='nearest'
            )
            return torch_resized_organized_pc.squeeze().permute(1, 2, 0).numpy()
        else:
            # Use OpenCV resize (same as in EasyNet)
            return cv2.resize(organized_pc, dsize=(target_width, target_height))

    def preprocess_point_cloud(self, organized_pc):
        """
        Preprocess point cloud: resize if enabled, then prepare for feature extraction.

        Args:
            organized_pc (np.ndarray): Input organized point cloud

        Returns:
            np.ndarray: Preprocessed point cloud
        """
        if self.enable_resize:
            print(f"Resizing point cloud from {organized_pc.shape} to {self.target_size + (3,)}")
            organized_pc = self.resize_organized_pc(
                organized_pc,
                target_height=self.target_size[0],
                target_width=self.target_size[1],
                method='torch'  # Use torch method for consistency with other projects
            )

        return organized_pc
    
    def organized_to_unorganized(self, organized_pc):
        """Convert organized point cloud to unorganized format."""
        original_shape = organized_pc.shape # 800 * 800 *3
        unorganized_pc = organized_pc.reshape(-1, 3) # 640000 * 3
        
        # Find valid points (non-zero points)
        valid_mask = np.any(unorganized_pc != 0, axis=1) # x y z都不为零的点即为有效点
        valid_indices = np.where(valid_mask)[0]
        valid_points = unorganized_pc[valid_mask] # eg : 20257 * 3
        
        return valid_points, valid_indices, original_shape #返回有效点及其对应的索引与原始的shape
    
    def fast_knn_search(self, points, query_points, k):
        """
        Fast KNN search using vectorized operations.
        
        Args:
            points (np.ndarray): Reference points (N, 3)
            query_points (np.ndarray): Query points (M, 3)
            k (int): Number of neighbors
            
        Returns:
            np.ndarray: Indices of k nearest neighbors for each query point (M, k)
        """
        # For large point clouds, use a more efficient approach
        if len(points) > 10000:
            # Use a simplified approach: sample neighbors from local region
            return self.fast_local_neighbors(points, query_points, k)  #先在局部区域查找候选点，再在候选点中精确定位最近邻
        else:
            # Use full distance computation for smaller point clouds
            return self.full_knn_search(points, query_points, k)  #直接计算所有点之间的距离，然后找出最近邻
    
    def fast_local_neighbors(self, points, query_points, k): #这一步很费时间！
        """Fast local neighbor search for large point clouds."""
        indices_list = [] #用于存储每个查询点找到的邻居索引

        # 添加进度条显示KNN搜索进度
        for query_point in tqdm(query_points, desc="KNN search", leave=False):
            # Find points within a local region first
            distances = np.linalg.norm(points - query_point, axis=1) # eg: (202527,) 计算所有点到当前查询点的距离
            
            # Get a larger set of candidates (e.g., 5*k points)
            n_candidates = min(5 * k, len(points)) # 为了提高效率，不取所有点，而是取一个比 k 大的固定倍数（这里是 5 倍）的点作为候选集
            candidate_indices = np.argpartition(distances, n_candidates-1)[:n_candidates] #快速找到距离最近的 n_candidates 个点的索引
            
            # From candidates, get exact k nearest
            candidate_distances = distances[candidate_indices]
            k_actual = min(k, len(candidate_indices))
            nearest_in_candidates = np.argpartition(candidate_distances, k_actual-1)[:k_actual]
            nearest_indices = candidate_indices[nearest_in_candidates]
            
            # Pad with duplicates if necessary
            if len(nearest_indices) < k: #如果邻居数量不足 k 个，就用最后一个邻居填充
                padding = np.full(k - len(nearest_indices), nearest_indices[-1])
                nearest_indices = np.concatenate([nearest_indices, padding])
            
            indices_list.append(nearest_indices)
        
        return np.array(indices_list) #20257 * 50
    
    def full_knn_search(self, points, query_points, k):
        """Full KNN search for smaller point clouds."""
        indices_list = []
        
        for query_point in query_points:
            distances = np.linalg.norm(points - query_point, axis=1)
            k_actual = min(k, len(points))
            nearest_indices = np.argpartition(distances, k_actual-1)[:k_actual]
            
            # Pad with duplicates if necessary
            if len(nearest_indices) < k:
                padding = np.full(k - len(nearest_indices), nearest_indices[-1])
                nearest_indices = np.concatenate([nearest_indices, padding])
            
            indices_list.append(nearest_indices)
        
        return np.array(indices_list)
    
    def _improve_normal_orientation_consistency(self, normals, points):
        """
        改进的法线方向一致性处理 - 专门为3D扫描数据优化
        实现视点导向的方向一致性算法，确保法线方向朝向扫描仪方向（外部）
        
        Args:
            normals (np.ndarray): 原始法线向量 (N, 3)
            points (np.ndarray): 点云坐标 (N, 3)
            
        Returns:
            np.ndarray: 校正后的法线向量 (N, 3)
        """
        if len(normals) == 0:
            return normals
        
        improved_normals = normals.copy()
        
        # 1. 确保所有法线都是单位向量
        norms = np.linalg.norm(improved_normals, axis=1, keepdims=True)
        norms = np.maximum(norms, 1e-8)  # 避免除零
        improved_normals = improved_normals / norms
        
        # 2. 为3D扫描数据智能估计视点位置
        if len(points) > 0:
            # 计算点云边界框和几何中心
            min_coords = np.min(points, axis=0)
            max_coords = np.max(points, axis=0)
            center = (min_coords + max_coords) / 2
            extent = max_coords - min_coords
            
            # 智能视点估计：基于点云几何结构估计相机位置
            max_extent = np.max(extent)
            
            # 多候选视点策略：考虑不同的扫描角度
            possible_viewpoints = [
                center + np.array([0, 0, max_extent * 2.0]),              # 上方视点
                center + np.array([0, max_extent * 2.0, 0]),              # 前方视点
                center + np.array([max_extent * 1.5, 0, max_extent * 1.5]),  # 右上视点
                center + np.array([-max_extent * 1.5, 0, max_extent * 1.5]), # 左上视点
                center + np.array([0, -max_extent * 2.0, 0]),             # 后方视点
            ]
            
            best_consistency_score = -1
            best_viewpoint = possible_viewpoints[0]
            
            # 选择最佳视点：使法线最一致地指向该位置
            for viewpoint in possible_viewpoints:
                # 计算从每个点到视点的方向
                view_directions = viewpoint[np.newaxis, :] - points
                view_directions = view_directions / (np.linalg.norm(view_directions, axis=1, keepdims=True) + 1e-8)
                
                # 计算法线与视点方向的一致性评分
                dot_products = np.sum(improved_normals * view_directions, axis=1)
                consistency_score = np.mean(np.abs(dot_products))  # 平均一致性分数
                
                if consistency_score > best_consistency_score:
                    best_consistency_score = consistency_score
                    best_viewpoint = viewpoint
            
            # 3. 使用最佳视点进行法线方向校正
            view_directions = best_viewpoint[np.newaxis, :] - points
            view_directions = view_directions / (np.linalg.norm(view_directions, axis=1, keepdims=True) + 1e-8)
            
            # 局部校正：确保法线朝向视点方向（外部）
            dot_products = np.sum(improved_normals * view_directions, axis=1)
            
            # 如果法线与视点方向夹角大于90度，则翻转法线
            flip_mask = dot_products < 0
            improved_normals[flip_mask] = -improved_normals[flip_mask]
            
            # 4. 全局一致性检查：基于主方向的二次校正
            # 计算法线在各个轴上的分布
            mean_normal = np.mean(improved_normals, axis=0)
            mean_normal = mean_normal / (np.linalg.norm(mean_normal) + 1e-8)
            
            # 基于Z方向分布决定整体朝向
            z_component_mean = np.mean(improved_normals[:, 2])
            if z_component_mean < -0.1:  # 大部分法线指向下方，可能需要整体翻转
                # 对于与主要方向夹角大于120度的法线，进行翻转
                dot_with_mean = np.sum(improved_normals * mean_normal[np.newaxis, :], axis=1)
                global_flip_mask = dot_with_mean < -0.5  # 容忍一定的差异
                improved_normals[global_flip_mask] = -improved_normals[global_flip_mask]
            
            # 5. 最终规范化检查
            norms_final = np.linalg.norm(improved_normals, axis=1, keepdims=True)
            norms_final = np.maximum(norms_final, 1e-8)
            improved_normals = improved_normals / norms_final
        
        return improved_normals
    
    def batch_pca_normals(self, points, neighbor_indices): # 计算法线，这一步也比较费时间
        """
        [DEPRECATED] 此函数已被 extract_normals_and_curvature 替代。
        新的融合函数消除了重复计算，提高了效率。
        
        Compute normals using PCA in batches for efficiency.
        
        Args:
            points (np.ndarray): Point cloud (N, 3)
            neighbor_indices (np.ndarray): Neighbor indices (N, k)
            
        Returns:
            np.ndarray: Normal vectors (N, 3)
        """
        n_points = len(points)
        normals = np.zeros_like(points)
        
        # Process in batches 分批处理
        for start_idx in range(0, n_points, self.batch_size):
            end_idx = min(start_idx + self.batch_size, n_points)
            batch_indices = neighbor_indices[start_idx:end_idx] # 1000 * 50
            
            for i, neighbors_idx in enumerate(batch_indices):
                actual_idx = start_idx + i
                neighbors = points[neighbors_idx]
                
                # Center the neighbors
                centroid = np.mean(neighbors, axis=0) #计算质心
                centered = neighbors - centroid #中心化
                
                try:
                    # Compute covariance matrix
                    cov_matrix = np.cov(centered.T) # 3*3
                    
                    # Get eigenvectors
                    _, eigenvectors = np.linalg.eigh(cov_matrix) #特征值、特征向量3*3
                    
                    # Normal is eigenvector with smallest eigenvalue
                    normal = eigenvectors[:, 0]  # Smallest eigenvalue first
                    
                    # Ensure consistent orientation
                    query_to_centroid = centroid - points[actual_idx]
                    if np.dot(normal, query_to_centroid) < 0:
                        normal = -normal # 翻转法线方向
                    
                    normals[actual_idx] = normal
                    
                except Exception:
                    # Default normal if computation fails
                    normals[actual_idx] = np.array([0, 0, 1])
        
        return normals
    
    def estimate_curvatures_fast(self, points, normals, neighbor_indices):
        """
        [DEPRECATED] 此函数已被 extract_normals_and_curvature 替代。
        新的融合函数使用更简单高效的曲率计算方法。
        
        Fast curvature estimation using simplified approach.
        
        Args:
            points (np.ndarray): Point cloud (N, 3)
            normals (np.ndarray): Normal vectors (N, 3)
            neighbor_indices (np.ndarray): Neighbor indices (N, k)
            
        Returns:
            tuple: (mean_curvatures, gaussian_curvatures)
        """
        n_points = len(points)
        mean_curvatures = np.zeros(n_points)
        gaussian_curvatures = np.zeros(n_points)
        
        # Process in batches
        for start_idx in range(0, n_points, self.batch_size):
            end_idx = min(start_idx + self.batch_size, n_points)
            batch_indices = neighbor_indices[start_idx:end_idx]
            
            for i, neighbors_idx in enumerate(batch_indices):
                actual_idx = start_idx + i
                
                try:
                    neighbors = points[neighbors_idx]
                    normal = normals[actual_idx]
                    
                    # Center neighbors
                    centroid = np.mean(neighbors, axis=0)
                    centered = neighbors - centroid
                    
                    # Project to local coordinate system
                    if abs(normal[0]) < 0.9:
                        u = np.cross(normal, np.array([1, 0, 0]))
                    else:
                        u = np.cross(normal, np.array([0, 1, 0]))
                    u = u / np.linalg.norm(u)
                    v = np.cross(normal, u)
                    
                    # Get local coordinates
                    u_coords = np.dot(centered, u)
                    v_coords = np.dot(centered, v)
                    heights = np.dot(centered, normal)
                    
                    # Simplified curvature estimation
                    if len(u_coords) >= 6:
                        # Fit quadratic surface
                        A = np.column_stack([
                            u_coords**2, u_coords*v_coords, v_coords**2,
                            u_coords, v_coords, np.ones(len(u_coords))
                        ])
                        
                        try:
                            coeffs = np.linalg.lstsq(A, heights, rcond=None)[0]
                            a, b, c = coeffs[0], coeffs[1], coeffs[2]
                            
                            # Principal curvatures
                            H = (a + c) / 2  # Mean curvature
                            K = a * c - (b/2)**2  # Gaussian curvature
                            
                            mean_curvatures[actual_idx] = H
                            gaussian_curvatures[actual_idx] = K
                            
                        except np.linalg.LinAlgError:
                            mean_curvatures[actual_idx] = 0
                            gaussian_curvatures[actual_idx] = 0
                    
                except Exception:
                    mean_curvatures[actual_idx] = 0
                    gaussian_curvatures[actual_idx] = 0
        
        return mean_curvatures, gaussian_curvatures
    
    def extract_normals_and_curvature(self, points, neighbor_indices):
        """
        融合的法线和曲率提取，消除重复计算。
        
        优势：
        1. 消除重复计算：只进行一次PCA分解，同时获得法线和曲率
        2. 计算效率高：避免了复杂的二次曲面拟合
        3. 物理意义明确：曲率定义为最小特征值占总方差的比例
        4. 数值稳定：添加小正数避免除零错误
        5. 实现视点导向的法线方向一致性
        
        Args:
            points (np.ndarray): Point cloud (N, 3)
            neighbor_indices (np.ndarray): Neighbor indices (N, k)
            
        Returns:
            tuple: (normals, curvatures)
        """
        n_points = len(points)
        normals = np.zeros_like(points)
        curvatures = np.zeros(n_points)
        epsilon = 1e-8  # 防止除零的小正数
        
        # 添加进度条显示法线和曲率计算进度
        for start_idx in tqdm(range(0, n_points, self.batch_size), desc="Computing normals/curvatures", leave=False):
            end_idx = min(start_idx + self.batch_size, n_points)
            batch_indices = neighbor_indices[start_idx:end_idx]
            
            for i, neighbors_idx in enumerate(batch_indices):
                actual_idx = start_idx + i
                neighbors = points[neighbors_idx]
                
                # 确保邻域点足够进行稳定的平面拟合
                if len(neighbors) < 4:
                    normals[actual_idx] = np.array([0, 0, 1])
                    curvatures[actual_idx] = 0
                    continue
                
                # 只计算一次质心和中心化
                centroid = np.mean(neighbors, axis=0)
                centered = neighbors - centroid
                
                try:
                    # 使用SVD进行主成分分析，这是更稳定的方法
                    _, singular_values, Vt = np.linalg.svd(centered, full_matrices=False)
                    
                    # 检查SVD结果的有效性
                    if len(singular_values) < 3 or singular_values[0] < epsilon:
                        # SVD失败或结果无效，使用协方差矩阵方法作为备选
                        cov_matrix = np.cov(centered.T)
                        eigenvalues, eigenvectors = np.linalg.eigh(cov_matrix)
                        # 最小特征值对应的特征向量是法线
                        normal = eigenvectors[:, 0]
                        # 重新计算奇异值用于曲率计算
                        singular_values = np.sqrt(np.maximum(eigenvalues, 0))
                    else:
                        # 使用SVD结果：最小奇异值对应的右奇异向量就是法线
                        normal = Vt[-1, :]  # 最后一行对应最小奇异值
                    
                    # 确保法线是单位向量
                    norm = np.linalg.norm(normal)
                    if norm > epsilon:
                        normal = normal / norm
                    else:
                        normal = np.array([0, 0, 1])
                    
                    # 曲率：基于奇异值的几何变化指标
                    if len(singular_values) >= 3:
                        s1, s2, s3 = sorted(singular_values, reverse=True)
                        total_variance = np.sum(singular_values)
                        
                        if total_variance > epsilon:
                            # 表面变化率：最小奇异值反映了法线方向的变化
                            surface_variation = s3 / total_variance
                            
                            # 几何变化率：前两个主方向的相对变化
                            if s1 > epsilon:
                                directional_change = abs(s1 - s2) / s1
                                planarity = (s2 - s3) / s1 if s1 > epsilon else 0.0
                            else:
                                directional_change = 0.0
                                planarity = 0.0
                            
                            # 组合曲率指标：结合表面变化和几何特征
                            combined_curvature = (
                                0.5 * surface_variation +          # 主要的表面变化
                                0.3 * directional_change +         # 方向性变化
                                0.2 * (1.0 - planarity)           # 非平面性
                            )
                            
                            # 应用非线性映射增强曲率对比度
                            curvature = np.tanh(combined_curvature * 10.0) * combined_curvature
                        else:
                            curvature = 0.0
                    else:
                        curvature = 0.0
                    
                    normals[actual_idx] = normal
                    curvatures[actual_idx] = curvature
                    
                except Exception:
                    normals[actual_idx] = np.array([0, 0, 1])
                    curvatures[actual_idx] = 0
        
        # 应用视点导向的法线方向一致性处理
        normals = self._improve_normal_orientation_consistency(normals, points)
        
        return normals, curvatures

    def extract_features(self, organized_pc):
        """
        Extract all features from organized point cloud.

        Args:
            organized_pc (np.ndarray): Organized point cloud of shape (H, W, 3)

        Returns:
            np.ndarray: Feature array of shape (H*W, 7)
                      Features: [x, y, z, nx, ny, nz, curvature]，
                      格式与原始项目的 enhanced_pointcloud 保持一致
        """
        # Preprocess: resize if enabled
        organized_pc = self.preprocess_point_cloud(organized_pc)

        # Convert to unorganized format
        valid_points, valid_indices, original_shape = self.organized_to_unorganized(organized_pc)
        
        if len(valid_points) == 0:
            return np.zeros((original_shape[0] * original_shape[1], 7), dtype=np.float32)

        # Find neighbors for all points
        k_actual = min(self.k_neighbors, len(valid_points))
        neighbor_indices = self.fast_knn_search(valid_points, valid_points, k_actual)

        # Extract normals and curvatures
        normals, curvatures = self.extract_normals_and_curvature(valid_points, neighbor_indices)
        
        # Create feature array - 保持与原始项目一致的格式 (H*W, 7)
        # 这样可以直接与原始项目的数据加载器兼容
        feature_array = np.zeros((original_shape[0] * original_shape[1], 7), dtype=np.float32)

        # Fill in features for valid points
        feature_array[valid_indices, :3] = valid_points.astype(np.float32)
        feature_array[valid_indices, 3:6] = normals.astype(np.float32)
        feature_array[valid_indices, 6] = curvatures.astype(np.float32)

        # 返回扁平化格式 (H*W, 7)，与原始项目的 enhanced_pointcloud 格式一致
        return feature_array

    def process_single_file(self, tiff_path, output_dir):
        """
        Process a single TIFF file and save features.

        Args:
            tiff_path (str): Path to input TIFF file
            output_dir (str): Output directory for features

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            organized_pc = self.load_point_cloud(tiff_path)
            if organized_pc is None:
                return False

            # Extract features
            features = self.extract_features(organized_pc)

            # Create output path - 保持与原始项目一致的命名规则
            tiff_path = Path(tiff_path)
            output_path = Path(output_dir) / f"{tiff_path.stem}.npy"

            # Ensure output directory exists
            output_path.parent.mkdir(parents=True, exist_ok=True)

            # Save features
            np.save(output_path, features)

            return True

        except Exception:
            return False


def process_dataset(dataset_path, k_neighbors=50, batch_size=1000):
    """
    Process entire dataset and extract features for all point clouds.

    Args:
        dataset_path (str): Path to dataset root
        k_neighbors (int): Number of neighbors for KNN search
        batch_size (int): Batch size for processing
    """
    # Initialize feature extractor
    extractor = FastPointCloudFeatureExtractor(k_neighbors=k_neighbors, batch_size=batch_size)

    # Find all TIFF files in xyz directories
    dataset_path = Path(dataset_path)
    tiff_files = sorted(list(dataset_path.rglob("xyz/*.tiff")))

    if not tiff_files:
        return

    # 按类别分组处理
    category_files = {}
    for tiff_file in tiff_files:
        # 提取类别路径：.../category/phase/anomaly_type/xyz/file.tiff
        parts = tiff_file.parts
        if len(parts) >= 4:
            category = parts[-4]  # category
            if category not in category_files:
                category_files[category] = []
            category_files[category].append(tiff_file)

    total_successful = 0
    total_failed = 0

    # 按类别处理
    for category, files in category_files.items():
        print(f"Processing {category}: {len(files)} files")

        successful = 0
        failed = 0

        for tiff_file in tqdm(files, desc=f"{category}", leave=False):
            # Create output directory
            xyz_dir = tiff_file.parent
            anomaly_type_dir = xyz_dir.parent
            features_dir = anomaly_type_dir / "features"

            success = extractor.process_single_file(tiff_file, features_dir)

            if success:
                successful += 1
            else:
                failed += 1

        total_successful += successful
        total_failed += failed
        print(f"  {category}: {successful}/{len(files)} completed")

    print(f"\nTotal: {total_successful}/{len(tiff_files)} files processed")


def process_dataset_with_resize(dataset_path, k_neighbors=50, batch_size=1000,
                               target_size=(224, 224), enable_resize=True):
    """
    Process entire dataset with resizing support and extract features for all point clouds.

    Args:
        dataset_path (str): Path to dataset root
        k_neighbors (int): Number of neighbors for KNN search
        batch_size (int): Batch size for processing
        target_size (tuple): Target size for resizing (height, width)
        enable_resize (bool): Whether to resize point clouds before processing
    """
    # Initialize feature extractor with resize support
    extractor = FastPointCloudFeatureExtractor(
        k_neighbors=k_neighbors,
        batch_size=batch_size,
        target_size=target_size,
        enable_resize=enable_resize
    )

    # Find all TIFF files in xyz directories
    dataset_path = Path(dataset_path)
    tiff_files = sorted(list(dataset_path.rglob("xyz/*.tiff")))

    if not tiff_files:
        return

    # 按类别分组处理
    category_files = {}
    for tiff_file in tiff_files:
        # 提取类别路径：.../category/phase/anomaly_type/xyz/file.tiff
        parts = tiff_file.parts
        if len(parts) >= 4:
            category = parts[-4]  # category
            if category not in category_files:
                category_files[category] = []
            category_files[category].append(tiff_file)

    total_successful = 0
    total_failed = 0

    # 按类别处理
    for category, files in category_files.items():
        print(f"Processing {category}: {len(files)} files")

        successful = 0
        failed = 0

        for tiff_file in tqdm(files, desc=f"{category}", leave=False):
            # Create output directory
            xyz_dir = tiff_file.parent
            anomaly_type_dir = xyz_dir.parent
            features_dir = anomaly_type_dir / "features"

            success = extractor.process_single_file(tiff_file, features_dir)

            if success:
                successful += 1
            else:
                failed += 1

        total_successful += successful
        total_failed += failed
        print(f"  {category}: {successful}/{len(files)} completed")

    print(f"\nTotal: {total_successful}/{len(tiff_files)} files processed")


def verify_compatibility_with_original_project():
    """
    验证新的快速特征提取器与原始项目的兼容性
    """
    # 创建测试数据
    H, W = 224, 224
    test_pc = np.random.rand(H, W, 3) * 2 - 1
    mask = np.random.rand(H, W) > 0.3
    test_pc[~mask] = 0

    # 创建特征提取器
    extractor = FastPointCloudFeatureExtractor(k_neighbors=30, batch_size=500)

    # 提取特征
    features = extractor.extract_features(test_pc)

    # 验证格式
    expected_shape = (H * W, 7)
    if features.shape == expected_shape:
        print(f"✓ Feature format correct: {features.shape}")
        return True
    else:
        print(f"✗ Feature format error: expected {expected_shape}, got {features.shape}")
        return False


def main():
    """Main function to run the feature extraction."""
    parser = argparse.ArgumentParser(description="Fast 3D physical feature extraction from point clouds with 224x224 resizing")
    parser.add_argument("--dataset_path",type=str,default="/raid/liulinna/projects/M3DM/datasets/mvtec3d/",help="Path to dataset root directory")
    parser.add_argument("--dataset_type",type=str,choices=["mvtec", "eyecandies"],default="mvtec",help="Type of dataset (mvtec or eyecandies)")
    parser.add_argument("--k_neighbors",type=int,default=50,help="Number of neighbors for KNN search (default: 50)")
    parser.add_argument("--batch_size",type=int,default=1000,help="Batch size for processing (default: 1000)")
    parser.add_argument("--single_file",type=str,default=None, help="Process single TIFF file instead of entire dataset")
    parser.add_argument("--target_height",type=int,default=224,help="Target height for resizing (default: 224)")
    parser.add_argument("--target_width",type=int,default=224,help="Target width for resizing (default: 224)")
    parser.add_argument("--disable_resize",action="store_true",help="Disable automatic resizing to 224x224")
    parser.add_argument("--use_dataset_class",action="store_true",help="Use the new PointCloudDataset class for processing")
    parser.add_argument("--verify_compatibility",action="store_true",help="Verify compatibility with original project")

    args = parser.parse_args()

    target_size = (args.target_height, args.target_width)
    enable_resize = not args.disable_resize

    if args.verify_compatibility:
        # 验证与原始项目的兼容性
        verify_compatibility_with_original_project()
        return

    if args.use_dataset_class and args.dataset_path:
        # Use new dataset class
        print("Using PointCloudDataset class for processing...")
        try:
            from pointcloud_dataset import PointCloudDataset

            dataset = PointCloudDataset(
                dataset_path=args.dataset_path,
                dataset_type=args.dataset_type,
                target_size=target_size,
                k_neighbors=args.k_neighbors,
                batch_size=args.batch_size,
                enable_resize=enable_resize
            )

            stats = dataset.process_dataset(save_features=True)
            print(f"Dataset processing completed with {stats['success_rate']:.2%} success rate")

        except ImportError:
            print("Warning: Could not import PointCloudDataset. Falling back to legacy processing.")
            process_dataset(args.dataset_path, args.k_neighbors, args.batch_size)

    elif args.single_file:
        # Process single file
        extractor = FastPointCloudFeatureExtractor(
            k_neighbors=args.k_neighbors,
            batch_size=args.batch_size,
            target_size=target_size,
            enable_resize=enable_resize
        )
        # 保持与原始项目一致的目录结构
        single_file_path = Path(args.single_file)
        xyz_dir = single_file_path.parent  # .../xyz
        anomaly_type_dir = xyz_dir.parent  # .../good 或 .../defect_type
        output_dir = anomaly_type_dir / "features"
        extractor.process_single_file(args.single_file, output_dir)
    elif args.dataset_path:
        # Process entire dataset
        process_dataset_with_resize(args.dataset_path, args.k_neighbors, args.batch_size, target_size, enable_resize)


if __name__ == "__main__":
    main()


