# IA-CRF 预处理配置文件

# 尺度选择参数（新增）
num_scales: 3                    # 选择的尺度数量：1, 2 或 3

# 多尺度几何特征参数
multi_scale_params:
  - radius: 0.02    # 细节尺度搜索半径
    max_nn: 30      # 最大邻近点数
  - radius: 0.05    # 中等尺度搜索半径
    max_nn: 50      # 最大邻近点数
  - radius: 0.1     # 粗糙尺度搜索半径
    max_nn: 100     # 最大邻近点数

# 输出设置
output_dir: "./preprocessed_mvtec3d"

# 本征分解设置
intrinsic_decomposition:
  model_version: "v2.1"          # 使用的本征分解模型版本
  device: "cuda"                 # 计算设备
  fallback_to_simple: true       # 如果专业模型不可用，是否使用简单CNN分解

# 数据处理设置
data_processing:
  target_size: [224, 224]        # 目标图像/点云尺寸
  normalize_rgb: true            # 是否归一化RGB图像到[0,1]
  save_intermediate: true        # 是否保存中间结果
  
# 3D特征提取设置
pointcloud_processing:
  enable_normal_estimation: true  # 是否估计表面法线
  enable_curvature_estimation: true # 是否估计曲率（只计算主曲率，不计算高斯曲率）
  orientation_consistency: true   # 是否进行法线方向一致性处理
  curvature_features: "principal_only"  # 曲率特征类型：只计算主曲率
  
# 并行处理设置
parallel:
  num_workers: 4                 # 并行worker数量
  batch_size: 1                  # 批处理大小（建议为1以避免内存问题）

# 日志设置
logging:
  level: "INFO"                  # 日志级别
  save_log: true                 # 是否保存日志文件
  log_file: "preprocess.log"     # 日志文件名

# 调试设置
debug:
  save_visualizations: true      # 是否保存可视化结果
  verbose: true                  # 是否显示详细信息
  test_single_sample: false      # 是否只测试单个样本