# Performance Configuration for 3D Geometric Feature Extraction
# Different performance profiles for various hardware configurations

# Ultra-fast configuration (minimal features, maximum speed)
ultra_fast:
  num_scales: 1
  use_multiprocessing: true
  max_workers: 8
  batch_size: 100
  use_fast_normals: true
  use_parallel_processing: true
  num_threads: 6
  verbose: false
  
  # Reduced quality parameters for speed
  multi_scale_params:
    - radius: 0.03
      max_nn: 15

# Fast configuration (balanced speed and quality)
fast:
  num_scales: 1
  use_multiprocessing: true
  max_workers: 6
  batch_size: 50
  use_fast_normals: true
  use_parallel_processing: true
  num_threads: 4
  verbose: false
  
  # Standard parameters
  multi_scale_params:
    - radius: 0.02
      max_nn: 20

# Balanced configuration (good balance of speed and quality)
balanced:
  num_scales: 2
  use_multiprocessing: true
  max_workers: 4
  batch_size: 30
  use_fast_normals: true
  use_parallel_processing: true
  num_threads: 4
  verbose: false
  
  # Two-scale parameters
  multi_scale_params:
    - radius: 0.02
      max_nn: 20
    - radius: 0.05
      max_nn: 30

# Quality configuration (higher quality features, slower processing)
quality:
  num_scales: 3
  use_multiprocessing: true
  max_workers: 4
  batch_size: 20
  use_fast_normals: false
  use_parallel_processing: true
  num_threads: 4
  verbose: true
  
  # Three-scale parameters with higher quality
  multi_scale_params:
    - radius: 0.02
      max_nn: 30
    - radius: 0.05
      max_nn: 50
    - radius: 0.1
      max_nn: 100

# High-quality configuration (maximum quality, slower processing)
high_quality:
  num_scales: 3
  use_multiprocessing: true
  max_workers: 2
  batch_size: 10
  use_fast_normals: false
  use_parallel_processing: true
  num_threads: 2
  verbose: true
  
  # Three-scale parameters with maximum quality
  multi_scale_params:
    - radius: 0.015
      max_nn: 40
    - radius: 0.04
      max_nn: 60
    - radius: 0.08
      max_nn: 120

# Single-threaded configuration (for debugging or low-resource systems)
single_threaded:
  num_scales: 1
  use_multiprocessing: false
  max_workers: 1
  batch_size: 1
  use_fast_normals: true
  use_parallel_processing: false
  num_threads: 1
  verbose: true
  
  # Basic parameters
  multi_scale_params:
    - radius: 0.02
      max_nn: 20

# Hardware-specific recommendations
hardware_recommendations:
  # For systems with >= 16 cores and 32GB+ RAM
  high_end:
    profile: "ultra_fast"
    max_workers: 12
    batch_size: 100
    
  # For systems with 8-16 cores and 16-32GB RAM  
  mid_range:
    profile: "fast"
    max_workers: 6
    batch_size: 50
    
  # For systems with 4-8 cores and 8-16GB RAM
  standard:
    profile: "balanced"
    max_workers: 4
    batch_size: 30
    
  # For systems with <= 4 cores and 8GB RAM
  low_end:
    profile: "fast"
    max_workers: 2
    batch_size: 20
    use_multiprocessing: false

# Performance optimization tips
optimization_tips:
  memory:
    - "Use num_scales=1 for memory-constrained systems"
    - "Reduce batch_size if experiencing memory issues"
    - "Enable use_fast_normals to reduce memory usage"
    
  speed:
    - "Use ultra_fast profile for maximum speed"
    - "Enable multiprocessing for datasets with >100 files"
    - "Use SSD storage for faster I/O operations"
    - "Set max_workers to number of CPU cores minus 2"
    
  quality:
    - "Use quality or high_quality profiles for research"
    - "Increase max_nn values for smoother features"
    - "Use 3 scales for comprehensive multi-scale analysis"
    - "Disable use_fast_normals for highest precision"

# Category-specific optimizations for MVTec 3D-AD
category_optimizations:
  # Small objects - faster processing
  small_objects:
    categories: ["dowel", "cookie"]
    profile: "ultra_fast"
    batch_size: 150
    
  # Medium objects - balanced processing
  medium_objects:
    categories: ["bagel", "cable_gland", "foam", "rope"]
    profile: "fast"
    batch_size: 75
    
  # Large objects - more thorough processing
  large_objects:
    categories: ["carrot", "peach", "potato", "tire"]
    profile: "balanced"
    batch_size: 50

# Estimated processing times (per 1000 files on mid-range hardware)
estimated_times:
  ultra_fast: "5-10 minutes"
  fast: "10-20 minutes"
  balanced: "20-40 minutes"
  quality: "40-80 minutes"
  high_quality: "80-160 minutes"
  single_threaded: "200-400 minutes"