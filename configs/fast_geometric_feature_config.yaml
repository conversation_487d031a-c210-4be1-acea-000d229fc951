# MVTec 3D-AD数据集快速3D几何特征提取配置
# 使用优化的快速特征提取器进行高性能批量处理

# 特征提取器选择
use_fast_extractor: true  # 使用快速特征提取器

# 快速特征提取器参数
k_neighbors: 50  # KNN搜索的邻居数量
extractor_batch_size: 1000  # 快速提取器的批处理大小

# 处理参数
force_reprocess: false  # 是否强制重新处理已存在的文件
target_size: [224, 224]  # 点云resize的目标尺寸

# 多进程参数
use_multiprocessing: true  # 启用多进程处理
max_workers: 8  # 最大工作进程数
batch_size: 50  # 并行处理的批次大小

# 输出控制
verbose: false  # 详细输出模式

# 快速特征提取器特点：
# - 单尺度特征提取（7维：x,y,z,nx,ny,nz,curvature）
# - 优化的KNN搜索算法
# - 向量化的法线和曲率计算
# - 更快的处理速度和更低的内存占用

# 使用示例：
# python extract_3d_geometric_features.py \
#   --dataset_path /path/to/mvtec3d \
#   --config ./configs/fast_geometric_feature_config.yaml \
#   --single_category bagel

# 或者直接使用命令行参数：
# python extract_3d_geometric_features.py \
#   --dataset_path /path/to/mvtec3d \
#   --use_fast_extractor \
#   --k_neighbors 50 \
#   --extractor_batch_size 1000 \
#   --verbose
