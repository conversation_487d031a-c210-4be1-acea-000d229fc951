# MVTec 3D-AD数据集3D几何特征提取配置
# 专门用于3D点云几何特征的批量提取和保存

# 特征提取参数
num_scales: 2  # 多尺度特征提取的尺度数量，支持1、2或3个尺度

# 处理参数
force_reprocess: false  # 是否强制重新处理已存在的文件
target_size: [224, 224]  # 点云resize的目标尺寸

# 多尺度参数（自动根据num_scales选择前N个）
# 细节尺度: radius=0.02, max_nn=30
# 中等尺度: radius=0.05, max_nn=50  
# 粗糙尺度: radius=0.1, max_nn=100

# 输出说明
# 特征维度计算：
# - 1个尺度: 7维 (3位置 + 3法线 + 1曲率)
# - 2个尺度: 11维 (3位置 + 6法线 + 2曲率)
# - 3个尺度: 15维 (3位置 + 9法线 + 3曲率)

# 使用示例：
# python extract_3d_geometric_features.py \
#   --dataset_path /path/to/mvtec3d \
#   --output_dir ./geometric_features \
#   --config ./configs/geometric_feature_config.yaml \
#   --single_category bagel