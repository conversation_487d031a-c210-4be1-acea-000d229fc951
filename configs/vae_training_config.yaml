# VAE Training Configuration
# Configuration for 3D Point Cloud VAE training on MVTec 3D-AD dataset
# This configuration implements the internal geometric prior learning phase

# Dataset Configuration
dataset:
  dataset_path: "/raid/liulinna/projects/M3DM/datasets/mvtec3d/"
  category: "bagel"  # Target category for training
  num_points: 2048   # Number of points per sample
  phases: ["train"]  # Only use normal training samples
  normalization_method: "unit_sphere"  # Point cloud normalization

# Model Architecture Configuration
model:
  input_dim: 3          # 3D coordinates (x, y, z)
  latent_dim: 256       # Latent space dimensionality
  feature_dim: 1024     # Feature extraction dimensionality
  output_points: 2048   # Number of output points

# Training Configuration
training:
  epochs: 200
  batch_size: 8         # Adjust based on GPU memory
  learning_rate: 0.001
  weight_decay: 1e-5
  device: "auto"        # "auto", "cuda", "cpu", or specific device "cuda:0"
  grad_clip: 1.0        # Gradient clipping threshold (0 to disable)
  num_workers: 4        # DataLoader workers

# Loss Function Configuration
loss:
  reconstruction_loss: "chamfer"  # "chamfer" or "mse"
  beta: 1.0                      # KL loss weight (beta-VAE)
  use_efficient_chamfer: false   # Use memory-efficient chamfer distance (disabled for stability)
  
  # Adaptive beta scheduling for KL loss
  beta_scheduler:
    use_scheduler: true
    initial_beta: 0.0      # Start with low KL weight
    final_beta: 1.0        # Final KL weight
    warmup_epochs: 50      # Number of epochs for beta warmup  
    mode: "linear"         # "linear", "cosine", or "exponential"

# Data Augmentation Configuration
augmentation:
  enable_rotation: true
  enable_scaling: true
  enable_jittering: true
  enable_translation: false
  
  # Rotation parameters (degrees)
  rotation_range:
    x: 15     # Small rotation around x-axis
    y: 15     # Small rotation around y-axis  
    z: 360    # Full rotation around z-axis
  
  # Scaling parameters
  scaling_range: [0.95, 1.05]  # Scale factor range
  
  # Jittering parameters
  jitter_std: 0.01      # Standard deviation of Gaussian noise
  jitter_clip: 0.02     # Clipping range for noise
  
  # Translation parameters
  translation_range: 0.05  # Translation range

# Validation Configuration
validation:
  enable_validation: false  # Enable validation during training
  frequency: 10            # Validation frequency (epochs)
  validation_split: 0.2    # Fraction of data for validation

# Checkpoint and Saving Configuration
checkpoint:
  save_dir: "checkpoints/vae_{category}/"  # {category} will be replaced
  save_frequency: 20                       # Save every N epochs
  max_checkpoints: 10                      # Maximum checkpoints to keep

# # Sampling and Evaluation Configuration
# sampling:
#   sample_dir: "samples/vae_{category}/"    # {category} will be replaced
#   num_samples: 16                          # Number of samples to generate
#   sample_frequency: 20                     # Sample every N epochs

# Logging Configuration
logging:
  use_tensorboard: true
  tensorboard_dir: "logs/vae_{category}/"  # {category} will be replaced
  log_frequency: 10                        # Log every N batches

# Advanced Configuration
advanced:
  # Weight initialization
  initialization:
    weight_init: "xavier_uniform"  # "xavier_uniform", "xavier_normal", "kaiming_uniform", "kaiming_normal"
  
  # Learning rate scheduling
  lr_scheduler:
    type: "cosine"        # "none", "cosine", "step", "plateau"
    cosine:
      T_max: 200         # Maximum number of iterations
      eta_min: 1e-6      # Minimum learning rate
    step:
      step_size: 50      # Step size for StepLR
      gamma: 0.5         # Multiplicative factor
    plateau:
      patience: 10       # Number of epochs with no improvement
      factor: 0.5        # Factor by which to reduce LR
      threshold: 1e-4    # Threshold for measuring improvement

# Category-specific configurations
# Override default parameters for specific categories if needed
category_configs:
  bagel:
    training:
      batch_size: 8
      epochs: 200
    model:
      latent_dim: 256
  
  cable_gland:
    training:
      batch_size: 6      # Smaller batch size for more complex geometry
      epochs: 300
    model:
      latent_dim: 512    # Larger latent space for complex shapes
  
  carrot:
    training:
      batch_size: 8
      epochs: 150
    augmentation:
      scaling_range: [0.9, 1.1]  # More aggressive scaling for organic shapes
  
  cookie:
    training:
      batch_size: 8
      epochs: 200
    model:
      latent_dim: 256
  
  dowel:
    training:
      batch_size: 8
      epochs: 150
    model:
      latent_dim: 128    # Simpler geometry, smaller latent space
  
  foam:
    training:
      batch_size: 6
      epochs: 300
    model:
      latent_dim: 512    # Complex foam structure
  
  peach:
    training:
      batch_size: 8
      epochs: 200
    augmentation:
      scaling_range: [0.9, 1.1]
  
  potato:
    training:
      batch_size: 8
      epochs: 250
    model:
      latent_dim: 384
    augmentation:
      scaling_range: [0.85, 1.15]  # High variability in potato shapes
  
  rope:
    training:
      batch_size: 6
      epochs: 300
    model:
      latent_dim: 512    # Complex rope geometry
  
  tire:
    training:
      batch_size: 8
      epochs: 200
    model:
      latent_dim: 256