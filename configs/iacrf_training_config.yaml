# IA-CRF Network Training Configuration
# Second-stage end-to-end multi-task cooperative training configuration

# Training configuration
training:
  # Basic training parameters
  epochs: 200
  batch_size: 4
  learning_rate: 0.0001  # 使用明确的浮点数格式
  weight_decay: 0.00001  # 使用明确的浮点数格式
  gradient_clip_norm: 1.0
  
  # Learning rate scheduler
  scheduler_type: 'cosine'  # 'cosine', 'step', 'none'
  warmup_epochs: 10
  
  # Early stopping
  early_stopping:
    patience: 20
    min_delta: 0.000001  # 使用明确的浮点数而不是科学计数法
  
  # Loss weights for multi-task learning
  # [albedo_recon, shading_recon, pointcloud_recon, kl_divergence, crossmodal_consistency]
  loss_weights: [1.0, 1.0, 0.5, 0.1, 0.5]
  
  # Device configuration
  device: 'auto'  # 'auto', 'cuda', 'cpu'
  num_workers: 2  # 使用少量工作进程
  pin_memory: true

# Model architecture configuration
model:
  # Albedo/Appearance encoder
  albedo_encoder:
    base_width: 64
    feature_dim: 256
  
  # Shading/Geometry-lighting encoder  
  shading_encoder:
    base_width: 64
    feature_dim: 256
  
  # 3D VAE branch (loaded from stage 1)
  vae_branch:
    input_dim: 7  # 使用7维增强几何特征（XYZ + Normal + Curvature）
    latent_dim: 256 
    feature_dim: 1024  # 与预训练模型保持一致
    output_points: 2048
  
  # Albedo reconstruction decoder
  albedo_decoder:
    base_width: 64
  
  # Shading reconstruction decoder
  shading_decoder:
    base_width: 64
  
  # Cross-modal mapping MLP
  cross_modal:
    hidden_dim: 512
  
  # Multi-source fusion module
  fusion:
    aligned_dim: 256
    attention_hidden_dim: 128
    output_dim: 256

# Loss function configuration
loss:
  # Albedo reconstruction loss
  albedo_recon:
    l1_weight: 1.0
    ssim_weight: 0.3
    perceptual_weight: 0.0  # Disabled for efficiency
  
  # Shading reconstruction loss
  shading_recon:
    l1_weight: 1.0
    ssim_weight: 0.3
    perceptual_weight: 0.0  # Disabled for efficiency
  
  # Point cloud reconstruction loss
  pointcloud_recon:
    use_efficient: true  # Use memory-efficient Chamfer distance
  
  # Cross-modal consistency loss
  crossmodal:
    loss_type: 'cosine'  # 'cosine', 'mse'
  
  # Overall loss weights
  weights: [1.0, 1.0, 0.5, 0.1, 0.5]

# Dataset configuration
dataset:
  # Dataset path (use placeholder for category)
  dataset_path: "/raid/liulinna/projects/M3DM/datasets/mvtec3d/"
  category: "bagel"  # Will be overridden in training script
  
  # Image preprocessing
  image_size: 224
  normalize: true
  
  # Data augmentation (minimal for consistency)
  augmentation:
    horizontal_flip: false
    rotation_range: 0
    color_jitter: 0.0

# Pseudo anomaly generation configuration (integrated in dataset)
pseudo_anomaly:
  # Enable pseudo anomaly generation during training
  enable: true
  
  # Geometric anomaly generation (probability-based per sample)
  geometric:
    probability: 0.7  # Probability of generating geometric anomaly per sample
    num_patches_range: [1, 2]
    patch_coverage_ratio: 0.15
    displacement_strength_range: [0.02, 0.08]
  
  # Material anomaly generation (probability-based per sample)
  material:
    probability: 0.5  # Probability of generating material anomaly per sample
    texture_strength_range: [0.3, 0.8]
    pattern_types: ['texture', 'color', 'brightness']
    texture_path: "/raid/liulinna/projects/EasyNet/datasets/dtd/images/" # Path to texture dataset (e.g., DTD), set to null to disable material anomaly

# Checkpointing and logging
checkpoint:
  # Checkpoint saving
  save_dir: "./checkpoints/stage2/{category}/"
  save_frequency: 10  # Save every N epochs
  keep_last_n: 5     # Keep last N checkpoints
  
  # Model initialization
  vae_checkpoint_path: "./checkpoints/vae_{category}/best.pth"
  freeze_vae: false  # Allow VAE fine-tuning
  
  # Resume training
  resume_from: null  # Path to checkpoint to resume from

# Logging configuration
logging:
  # TensorBoard logging
  tensorboard_dir: "./logs/stage2/{category}/"
  log_frequency: 10  # Log every N steps
  
  # Console logging
  print_frequency: 50  # Print every N steps (when verbose=true)
  verbose: false  # Detailed logging (default: false for clean output)
  
  # Metrics to track
  track_metrics:
    - "total_loss"
    - "albedo_recon"
    - "shading_recon" 
    - "pointcloud_recon"
    - "kl_divergence"
    - "crossmodal_consistency"
    - "learning_rate"
    - "gradient_norm"

# Validation configuration
validation:
  # Validation frequency
  frequency: 5  # Validate every N epochs
  
  # Validation metrics
  metrics:
    - "reconstruction_quality"
    - "cross_modal_consistency"
    - "anomaly_detection_performance"
  
  # Save validation visualizations
  save_visualizations: true
  visualization_dir: "./visualizations/stage2/{category}/"

# Category-specific configurations for MVTec 3D-AD
categories:
  bagel:
    training:
      batch_size: 4
      learning_rate: 0.0001  # 1e-4
      loss_weights: [1.0, 1.0, 0.5, 0.1, 0.5]
    
  cable_gland:
    training:
      batch_size: 4  
      learning_rate: 0.00008  # 8e-5
      loss_weights: [1.0, 1.2, 0.6, 0.1, 0.4]
    
  carrot:
    training:
      batch_size: 6
      learning_rate: 0.00012  # 1.2e-4
      loss_weights: [1.0, 1.0, 0.4, 0.1, 0.6]
    
  cookie:
    training:
      batch_size: 4
      learning_rate: 0.0001  # 1e-4
      loss_weights: [1.2, 1.0, 0.5, 0.1, 0.5]
    
  dowel:
    training:
      batch_size: 6
      learning_rate: 1e-4
      loss_weights: [1.0, 1.0, 0.6, 0.1, 0.4]
    
  foam:
    training:
      batch_size: 4
      learning_rate: 8e-5
      loss_weights: [1.0, 1.1, 0.5, 0.1, 0.5]
    
  peach:
    training:
      batch_size: 4
      learning_rate: 1e-4
      loss_weights: [1.0, 1.0, 0.4, 0.1, 0.6]
    
  potato:
    training:
      batch_size: 4
      learning_rate: 1.2e-4
      loss_weights: [1.0, 1.0, 0.5, 0.1, 0.5]
    
  rope:
    training:
      batch_size: 6
      learning_rate: 1e-4
      loss_weights: [1.0, 1.2, 0.6, 0.1, 0.4]
    
  tire:
    training:
      batch_size: 4
      learning_rate: 1e-4
      loss_weights: [1.0, 1.0, 0.5, 0.1, 0.5]

# Advanced configuration
advanced:
  # Mixed precision training
  use_amp: false  # Automatic Mixed Precision
  
  # Gradient accumulation
  gradient_accumulation_steps: 1
  
  # Memory optimization
  memory_efficient: true
  
  # Deterministic training
  seed: 42
  deterministic: false  # May reduce performance
  
  # Experimental features
  experimental:
    adaptive_loss_weighting: false  # Use uncertainty-based loss weighting
    cross_modal_attention: false    # Add attention between modalities
    adversarial_training: false     # Add adversarial loss