import os
import glob
import numpy as np
import torch
import cv2
import tifffile as tiff
from PIL import Image
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
from typing import Optional
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 移除物理分解模块依赖，改为直接加载预处理的albedo和shading图像
from pseudo_anomaly_generation.geometric.geometric_anomaly_generator import GeometricAnomalyGenerator
from pseudo_anomaly_generation.material.albedo_anomaly_generator import AlbedoMaterialAnomalyGenerator

def mvtec3d_classes():
    """MVTec 3D-AD数据集类别列表"""
    return [
        "bagel", "cable_gland", "carrot", "cookie", "dowel",
        "foam", "peach", "potato", "rope", "tire"
    ]

class IACRFDataset(Dataset):
    """
    IA-CRF数据集基类
    集成物理信息提取功能，支持多模态数据加载
    """
    
    def __init__(self, split, class_name, img_size, dataset_path, config):
        self.IMAGENET_MEAN = [0.485, 0.456, 0.406]
        self.IMAGENET_STD = [0.229, 0.224, 0.225]
        
        self.split = split
        self.cls = class_name
        self.img_size = img_size
        self.dataset_path = dataset_path
        self.config = config
        
        self.img_path = os.path.join(dataset_path, self.cls, split)
        
        # RGB图像变换
        self.rgb_transform = transforms.Compose([
            transforms.Resize((224, 224), interpolation=transforms.InterpolationMode.BICUBIC),
            transforms.ToTensor(),
            transforms.Normalize(mean=self.IMAGENET_MEAN, std=self.IMAGENET_STD)
        ])
        
        # 移除物理信息提取模块初始化，改为直接加载预处理数据
        
        # 加载数据集
        self.data_items = self.load_dataset()
        
    def load_dataset(self):
        """加载数据集，由子类实现"""
        raise NotImplementedError
    
    def __len__(self):
        return len(self.data_items)
    
    def load_rgb_image(self, rgb_path):
        """加载RGB图像"""
        image = Image.open(rgb_path).convert('RGB')
        return self.rgb_transform(image)
    
    def load_organized_pointcloud(self, tiff_path, target_size=(224, 224)):
        """
        加载有组织点云数据并resize到目标尺寸
        """
        # 读取.tiff文件
        xyz_data = tiff.imread(tiff_path)
        
        # Resize到目标尺寸
        torch_organized_pc = torch.tensor(xyz_data).permute(2, 0, 1).unsqueeze(dim=0).contiguous().float()
        torch_resized_organized_pc = torch.nn.functional.interpolate(
            torch_organized_pc, 
            size=target_size,
            mode='nearest'
        )
        resized_organized_pc = torch_resized_organized_pc.squeeze(dim=0).permute(1, 2, 0).contiguous().numpy()
        
        return resized_organized_pc
    
    def load_albedo_image(self, albedo_path):
        """加载预处理的Albedo图像"""
        if albedo_path and os.path.exists(albedo_path):
            image = Image.open(albedo_path).convert('RGB')
            return self.rgb_transform(image)
        else:
            raise FileNotFoundError(f"Albedo image not found: {albedo_path}")
    
    def load_shading_image(self, shading_path):
        """加载预处理的Shading图像"""
        if shading_path and os.path.exists(shading_path):
            image = Image.open(shading_path).convert('RGB')
            return self.rgb_transform(image)
        else:
            raise FileNotFoundError(f"Shading image not found: {shading_path}")
    
    def _load_precomputed_features(self, tiff_path: str, features_path: str = None) -> Optional[np.ndarray]:
        """加载预计算的3D几何特征"""
        try:
            # 如果提供了直接的features路径，优先使用
            if features_path and os.path.exists(features_path):
                feature_path = features_path
            else:
                # 构建特征文件路径
                # 将 xyz/filename.tiff 转换为 features/filename.npy
                tiff_dir = os.path.dirname(tiff_path)  # .../good/xyz
                dataset_dir = os.path.dirname(tiff_dir)  # .../good
                filename = os.path.splitext(os.path.basename(tiff_path))[0]
                features_dir = os.path.join(dataset_dir, 'features')
                feature_path = os.path.join(features_dir, f"{filename}.npy")
            
            if not os.path.exists(feature_path):
                if self.config['logging']['verbose']:
                    pass
                return None
                
            features = np.load(feature_path)
            
            # 验证特征数据有效性
            if features.shape[0] == 0:
                return None
            
            if len(features.shape) != 2:
                return None
            
            # 检查特征维度（预期7维：[x,y,z,nx,ny,nz,curvature]）
            if features.shape[1] < 6:  # 至少包含位置和法线
                return None
                
            # 计算有效点数量（非全零点）
            valid_points = np.any(features[:, :3] != 0, axis=1)
            valid_count = np.sum(valid_points)
            
            if self.config['logging']['verbose']:
                print(f"加载预计算的3D特征: {feature_path}")
                print(f"  特征形状: {features.shape}")
                print(f"  有效点数: {valid_count}/{features.shape[0]} ({valid_count/features.shape[0]*100:.1f}%)")
            
            return features
                
        except Exception as e:
            print(f"Error loading precomputed features: {e}")
            return None


class IACRFTrainDataset(IACRFDataset):
    """
    IA-CRF训练数据集
    只使用正常样本进行训练，集成伪异常生成功能
    """
    
    def __init__(self, class_name, img_size, dataset_path, config, 
                 enable_pseudo_anomaly=True, anomaly_source_path=None):
        self.enable_pseudo_anomaly = enable_pseudo_anomaly
        self.anomaly_source_path = anomaly_source_path
        
        super().__init__("train", class_name, img_size, dataset_path, config)
        
        # 初始化伪异常生成器
        if self.enable_pseudo_anomaly:
            self._init_pseudo_anomaly_generators()
    
    def _init_pseudo_anomaly_generators(self):
        """
        初始化伪异常生成器
        """
        try:
            # 初始化几何异常生成器
            geometric_config = {
                'patch_selection': {
                    'min_patch_size': 100,
                    'max_patch_size': 400,
                    'expansion_radius': 0.03
                },
                'displacement': {
                    'displacement_strength_range': self.config.get('pseudo_anomaly', {}).get('geometric', {}).get('displacement_strength_range', [0.02, 0.08]),
                    'center_weight': 1.0,
                    'edge_weight': 0.2
                },
                'shading_transformation': {
                    'light_direction': [0.3, 0.3, 0.9],
                    'ambient_light': 0.2,
                    'diffuse_strength': 0.8,
                    'gaussian_blur_sigma': 1.5
                },
                'num_patches_range': self.config.get('pseudo_anomaly', {}).get('geometric', {}).get('num_patches_range', [1, 3]),
                'patch_coverage_ratio': self.config.get('pseudo_anomaly', {}).get('geometric', {}).get('patch_coverage_ratio', 0.15),
                'anomaly_probability': self.config.get('pseudo_anomaly', {}).get('geometric', {}).get('probability', 0.7)
            }
            
            self.geometric_generator = GeometricAnomalyGenerator(geometric_config)
            print("Geometric anomaly generator initialized")
            
            # 初始化材质异常生成器
            if self.anomaly_source_path and os.path.exists(self.anomaly_source_path):
                self.material_generator = AlbedoMaterialAnomalyGenerator(
                    anomaly_source_path=self.anomaly_source_path,
                    image_size=self.img_size
                )
                print(f"Material anomaly generator initialized with texture path: {self.anomaly_source_path}")
            else:
                self.material_generator = None
                print("Warning: Material anomaly generator not initialized - no valid texture path provided")
                
        except Exception as e:
            print(f"Warning: Failed to initialize pseudo anomaly generators: {e}")
            self.geometric_generator = None
            self.material_generator = None
    
    def _generate_pseudo_anomaly(self, albedo, shading, organized_pointcloud, enhanced_pointcloud=None):
        """
        生成伪异常样本
        
        Args:
            albedo: Albedo图像 [C, H, W] tensor, 值域[0,1]
            shading: Shading图像 [C, H, W] tensor, 值域[0,1]
            organized_pointcloud: 有组织点云 [H, W, 3] numpy
            enhanced_pointcloud: 增强几何特征 [H*W, 7] numpy (可选)
            
        Returns:
            dict: 包含增强数据的字典
        """
        # 转换为 numpy 格式进行处理
        if isinstance(albedo, torch.Tensor):
            albedo_np = albedo.permute(1, 2, 0).cpu().numpy()  # [H, W, C]
        else:
            albedo_np = albedo
            
        if isinstance(shading, torch.Tensor):
            shading_np = shading.permute(1, 2, 0).cpu().numpy()  # [H, W, C]
        else:
            shading_np = shading
        
        # 创建深度图（从点云Z坐标）
        depth_map = organized_pointcloud[:, :, 2:3]  # [H, W, 1]
        
        # 决定生成哪种类型的异常
        anomaly_type = np.random.choice(['geometric', 'material', 'both'], p=[0.4, 0.4, 0.2])
        
        # 初始化返回结果
        result = {
            'augmented_albedo': albedo,
            'augmented_shading': shading,
            'augmented_pointcloud': organized_pointcloud,
            'augmented_enhanced_pointcloud': enhanced_pointcloud,
            'geometric_anomaly_mask': np.zeros((*self.img_size, 1), dtype=np.float32),
            'material_anomaly_mask': np.zeros((*self.img_size, 1), dtype=np.float32),
            'has_geometric_anomaly': np.array([0.0], dtype=np.float32),
            'has_material_anomaly': np.array([0.0], dtype=np.float32),
            'anomaly_type': anomaly_type
        }
        
        # 生成几何异常
        if anomaly_type in ['geometric', 'both'] and self.geometric_generator is not None:
            try:
                print(f"Generating geometric anomaly (type: {anomaly_type})...")
                geometric_result = self._generate_geometric_anomaly(
                    organized_pointcloud, shading_np, enhanced_pointcloud
                )
                
                if geometric_result['has_anomaly'][0] > 0:
                    # Geometric anomaly generation successful
                    # 转换shading结果格式
                    augmented_shading = geometric_result['augmented_shading']
                    if augmented_shading is not None:
                        if isinstance(augmented_shading, np.ndarray):
                            if len(augmented_shading.shape) == 3:  # [H, W, C]
                                result['augmented_shading'] = torch.from_numpy(
                                    augmented_shading.transpose(2, 0, 1)
                                ).float()
                            else:
                                result['augmented_shading'] = shading  # 使用原始shading
                        else:
                            result['augmented_shading'] = augmented_shading
                    else:
                        result['augmented_shading'] = shading
                    
                    # 更新其他几何异常数据
                    result['augmented_pointcloud'] = geometric_result['augmented_pointcloud']
                    result['augmented_enhanced_pointcloud'] = geometric_result.get('augmented_enhanced_pointcloud')
                    result['geometric_anomaly_mask'] = geometric_result['anomaly_mask']
                    result['has_geometric_anomaly'] = geometric_result['has_anomaly']
                    
                    if geometric_result.get('shading_results'):
                        pass  # 静默处理shading变换结果
                else:
                    pass  # 异常生成失败
                    
            except Exception as e:
                # 异常生成失败，打印错误堆栈
                import traceback
                traceback.print_exc()
        
        # 生成材质异常
        if anomaly_type in ['material', 'both'] and self.material_generator is not None:
            try:
                material_result = self._generate_material_anomaly(
                    albedo_np, depth_map
                )
                
                if material_result['has_anomaly'][0] > 0:
                    result['augmented_albedo'] = torch.from_numpy(
                        material_result['augmented_albedo'].transpose(2, 0, 1)
                    ).float()
                    result['material_anomaly_mask'] = material_result['anomaly_mask'][:, :, np.newaxis]  # 添加通道维度
                    result['has_material_anomaly'] = material_result['has_anomaly']
                    
            except Exception as e:
                pass  # 材质异常生成失败
        
        return result
    
    def _generate_geometric_anomaly(self, organized_pointcloud, shading_image, enhanced_pointcloud=None):
        """
        生成几何异常
        
        Args:
            organized_pointcloud: 有组织点云 [H, W, 3]
            shading_image: Shading图像 [H, W, C]
            enhanced_pointcloud: 增强几何特征 [H*W, 7] 或 [H, W, 7] (可选)

        Returns:
            dict: 几何异常生成结果
        """
        if enhanced_pointcloud is not None:
            # 使用预计算的增强特征
            result = self.geometric_generator.generate_single_geometric_anomaly(
                geometric_features=enhanced_pointcloud,
                original_shading=shading_image
            )
        else:
            # 当没有预计算特征时，从有组织点云创建简单特征
            H, W, _ = organized_pointcloud.shape
            # 创建简化的几何特征 [H*W, 7]
            simple_features = np.zeros((H*W, 7))
            simple_features[:, :3] = organized_pointcloud.reshape(-1, 3)  # XYZ坐标
            # 简单的法线估计（向上）
            simple_features[:, 3:6] = np.array([0, 0, 1])  # 简单向上法线
            simple_features[:, 6] = 0.01  # 简单曲率值

            result = self.geometric_generator.generate_single_geometric_anomaly(
                geometric_features=simple_features,
                original_shading=shading_image
            )
        
        # 检查异常生成结果
        if result['has_anomaly'][0] <= 0:
            # 几何异常生成失败，返回原始数据
            return {
                'has_anomaly': np.array([0.0]),
                'augmented_shading': shading_image,
                'augmented_pointcloud': organized_pointcloud,
                'augmented_enhanced_pointcloud': enhanced_pointcloud,
                'anomaly_mask': np.zeros((*self.img_size, 1), dtype=np.float32),
                'anomaly_type': 'generation_failed'
            }
        
        # 将结果格式转换为数据集期望的格式
        
        # 确保modified_shading正确处理
        modified_shading = result['modified_shading']
        if modified_shading is None:
            # 使用原始shading
            modified_shading = shading_image
        elif modified_shading.shape != shading_image.shape:
            # shading形状不匹配
            if shading_image is not None:
                modified_shading = shading_image
        
        formatted_result = {
            'has_anomaly': result['has_anomaly'],
            'augmented_shading': modified_shading,
            'augmented_pointcloud': result['deformed_pointcloud'],
            'augmented_enhanced_pointcloud': enhanced_pointcloud,  # 保持原始特征
            'anomaly_mask': np.zeros((*self.img_size, 1), dtype=np.float32),  # 创建默认掩码
            'anomaly_type': result['anomaly_type'],
            'shading_results': result.get('shading_results', None)  # 保存shading变换结果用于调试
        }
        
        # 如果有补丁信息，创建掩码
        if result['patches_info']:
            # 创建异常掩码
            mask = np.zeros(self.img_size, dtype=np.float32)
            for i, patch_info in enumerate(result['patches_info']):
                if 'patch_mask' in patch_info:
                    patch_mask = patch_info['patch_mask']
                    # 处理补丁掩码
                    if patch_mask.shape == self.img_size:
                        mask = np.maximum(mask, patch_mask)
                    else:
                        pass  # 补丁掩码形状不匹配
            formatted_result['anomaly_mask'] = mask[:, :, np.newaxis]
            # 异常掩码创建完成
        else:
            pass  # 无补丁信息可用于创建掩码
        
        # 验证shading变化（仅保留关键错误信息）
        if result.get('shading_results'):
            shading_stats = result['shading_results'].get('shading_statistics', {})
            # 静默处理，仅在需要时记录到TensorBoard
        
        return formatted_result
    
    def _generate_material_anomaly(self, albedo_image, depth_map):
        """
        生成材质异常
        
        Args:
            albedo_image: Albedo图像 [H, W, C]
            depth_map: 深度图 [H, W, 1]
            
        Returns:
            dict: 材质异常生成结果
        """
        # 随机选择纹理图像
        texture_idx = np.random.randint(0, len(self.material_generator.anomaly_source_paths))
        texture_path = self.material_generator.anomaly_source_paths[texture_idx]
        
        # 生成材质异常
        result = self.material_generator.generate_material_anomaly(
            albedo_image=albedo_image,
            depth_map=depth_map,
            texture_path=texture_path,
            anomaly_probability=self.config.get('pseudo_anomaly', {}).get('material', {}).get('probability', 0.5)
        )
        
        return result
    
    def load_dataset(self):
        """加载训练数据集（仅正常样本）"""
        data_items = []
        
        # 检查基础路径是否存在
        if not os.path.exists(self.img_path):
            raise FileNotFoundError(f"Dataset path does not exist: {self.img_path}")
        
        good_path = os.path.join(self.img_path, 'good')
        if not os.path.exists(good_path):
            raise FileNotFoundError(f"Good directory does not exist: {good_path}")
        
        # 构建搜索路径
        rgb_search_path = os.path.join(self.img_path, 'good', 'rgb') + "/*.png"
        tiff_search_path = os.path.join(self.img_path, 'good', 'xyz') + "/*.tiff"
        albedo_search_path = os.path.join(self.img_path, 'good', 'rgb', 'albedo') + "/*.png"
        shading_search_path = os.path.join(self.img_path, 'good', 'rgb', 'shading') + "/*.png"
        features_search_path = os.path.join(self.img_path, 'good', 'features') + "/*.npy"
        
        # 获取正常样本路径
        rgb_paths = glob.glob(rgb_search_path)
        tiff_paths = glob.glob(tiff_search_path)
        
        # 获取预处理的albedo和shading路径
        albedo_paths = glob.glob(albedo_search_path)
        shading_paths = glob.glob(shading_search_path)
        
        # 获取预计算的3D几何特征路径
        features_paths = glob.glob(features_search_path)
        
        rgb_paths.sort()
        tiff_paths.sort()
        albedo_paths.sort()
        shading_paths.sort()
        features_paths.sort()
        
        # 检查文件数量匹配
        print(f"Found files:")
        print(f"  RGB: {len(rgb_paths)}")
        print(f"  Point clouds: {len(tiff_paths)}")
        print(f"  Albedo: {len(albedo_paths)}")
        print(f"  Shading: {len(shading_paths)}")
        print(f"  Features: {len(features_paths)}")
        
        # 确保基本的RGB和点云文件匹配
        assert len(rgb_paths) == len(tiff_paths), f"RGB({len(rgb_paths)})和点云({len(tiff_paths)})文件数量不匹配"
        
        # 检查预处理文件是否存在
        has_preprocessed_images = len(albedo_paths) > 0 and len(shading_paths) > 0
        has_precomputed_features = len(features_paths) > 0
        
        if has_preprocessed_images:
            # 验证预处理图像数量匹配
            expected_count = len(rgb_paths)
            if len(albedo_paths) != expected_count or len(shading_paths) != expected_count:
                has_preprocessed_images = False
        
        if has_precomputed_features:
            # 验证预计算特征数量匹配
            if len(features_paths) != len(rgb_paths):
                has_precomputed_features = False
        
        # 构建数据项列表
        for i, (rgb_path, tiff_path) in enumerate(zip(rgb_paths, tiff_paths)):
            # 获取文件名（不含扩展名）用于匹配
            rgb_filename = os.path.splitext(os.path.basename(rgb_path))[0]
            tiff_filename = os.path.splitext(os.path.basename(tiff_path))[0]
            
            # 确保文件名匹配
            if rgb_filename != tiff_filename:
                pass
                continue
            
            # 构建对应的albedo, shading和features路径
            albedo_path = None
            shading_path = None
            features_path = None
            
            if has_preprocessed_images:
                expected_albedo = os.path.join(self.img_path, 'good', 'rgb', 'albedo', f"{rgb_filename}.png")
                expected_shading = os.path.join(self.img_path, 'good', 'rgb', 'shading', f"{rgb_filename}.png")
                
                if os.path.exists(expected_albedo) and os.path.exists(expected_shading):
                    albedo_path = expected_albedo
                    shading_path = expected_shading
                else:
                    pass
            
            if has_precomputed_features:
                expected_features = os.path.join(self.img_path, 'good', 'features', f"{tiff_filename}.npy")
                if os.path.exists(expected_features):
                    features_path = expected_features
                else:
                    pass
            
            data_items.append({
                'rgb_path': rgb_path,
                'tiff_path': tiff_path,
                'albedo_path': albedo_path,
                'shading_path': shading_path,
                'features_path': features_path,
                'label': 0  # 正常样本
            })
        
        print(f"Successfully loaded {len(data_items)} training samples")
        if has_preprocessed_images:
            pass
        else:
            pass
            
        if has_precomputed_features:
            pass
        else:
            pass
        
        return data_items
    
    def __getitem__(self, idx):
        data_item = self.data_items[idx]
        
        # 加载RGB图像
        rgb_image = self.load_rgb_image(data_item['rgb_path'])
        
        # 加载有组织点云
        organized_pointcloud = self.load_organized_pointcloud(data_item['tiff_path'])
        
        # 检查是否有预处理的albedo和shading数据
        if data_item['albedo_path'] is not None and data_item['shading_path'] is not None:
            # 加载预处理的albedo和shading图像
            albedo = self.load_albedo_image(data_item['albedo_path'])
            shading = self.load_shading_image(data_item['shading_path'])
            
            # 加载预计算的3D几何特征
            enhanced_pointcloud = self._load_precomputed_features(
                data_item['tiff_path'], 
                data_item['features_path']
            )
            
            if enhanced_pointcloud is None:
                # 如果没有预计算特征，使用简化的点云处理（仅坐标）
                if self.config['logging']['verbose']:
                    print(f"Warning: 未找到预计算特征，使用简化点云处理: {data_item['tiff_path']}")
                # 仅使用XYZ坐标，不进行复杂的法线估计
                H, W, C = organized_pointcloud.shape
                enhanced_pointcloud = organized_pointcloud.reshape(-1, C)  # [H*W, 3]
            
            # 基础数据项
            base_data = {
                'rgb_image': rgb_image,
                'organized_pointcloud': torch.from_numpy(organized_pointcloud).float(),
                'albedo': albedo,  # 预处理的albedo
                'shading': shading,  # 预处理的shading
                'enhanced_pointcloud': torch.from_numpy(enhanced_pointcloud).float(),
                'label': data_item['label'],
                'rgb_path': data_item['rgb_path'],
                'tiff_path': data_item['tiff_path']
            }
            
            # 生成伪异常（如果启用）
            if self.enable_pseudo_anomaly and (self.geometric_generator is not None or self.material_generator is not None):
                try:
                    pseudo_anomaly_result = self._generate_pseudo_anomaly(
                        albedo, shading, organized_pointcloud, enhanced_pointcloud
                    )
                    
                    # 更新数据项
                    base_data.update({
                        'augmented_albedo': pseudo_anomaly_result['augmented_albedo'],
                        'augmented_shading': pseudo_anomaly_result['augmented_shading'],
                        'augmented_organized_pointcloud': torch.from_numpy(pseudo_anomaly_result['augmented_pointcloud']).float(),
                        'geometric_anomaly_mask': torch.from_numpy(pseudo_anomaly_result['geometric_anomaly_mask']).float(),
                        'material_anomaly_mask': torch.from_numpy(pseudo_anomaly_result['material_anomaly_mask']).float(),
                        'has_geometric_anomaly': torch.from_numpy(pseudo_anomaly_result['has_geometric_anomaly']).float(),
                        'has_material_anomaly': torch.from_numpy(pseudo_anomaly_result['has_material_anomaly']).float(),
                        'anomaly_type': pseudo_anomaly_result['anomaly_type']
                    })
                    
                    # 更新增强点云特征（如果有）
                    if pseudo_anomaly_result['augmented_enhanced_pointcloud'] is not None:
                        base_data['augmented_enhanced_pointcloud'] = torch.from_numpy(
                            pseudo_anomaly_result['augmented_enhanced_pointcloud']
                        ).float()
                    else:
                        base_data['augmented_enhanced_pointcloud'] = base_data['enhanced_pointcloud']
                        
                except Exception as e:
                    print(f"Warning: 伪异常生成失败，使用正常样本: {e}")
                    # 添加默认伪异常数据项（正常样本）
                    base_data.update({
                        'augmented_albedo': albedo,
                        'augmented_shading': shading,
                        'augmented_organized_pointcloud': base_data['organized_pointcloud'],
                        'augmented_enhanced_pointcloud': base_data['enhanced_pointcloud'],
                        'geometric_anomaly_mask': torch.zeros((*self.img_size, 1), dtype=torch.float32),
                        'material_anomaly_mask': torch.zeros((*self.img_size, 1), dtype=torch.float32),
                        'has_geometric_anomaly': torch.tensor([0.0], dtype=torch.float32),
                        'has_material_anomaly': torch.tensor([0.0], dtype=torch.float32),
                        'anomaly_type': 'none'
                    })
            else:
                # 不生成伪异常，添加默认数据项
                base_data.update({
                    'augmented_albedo': albedo,
                    'augmented_shading': shading,
                    'augmented_organized_pointcloud': base_data['organized_pointcloud'],
                    'augmented_enhanced_pointcloud': base_data['enhanced_pointcloud'],
                    'geometric_anomaly_mask': torch.zeros((*self.img_size, 1), dtype=torch.float32),
                    'material_anomaly_mask': torch.zeros((*self.img_size, 1), dtype=torch.float32),
                    'has_geometric_anomaly': torch.tensor([0.0], dtype=torch.float32),
                    'has_material_anomaly': torch.tensor([0.0], dtype=torch.float32),
                    'anomaly_type': 'none'
                })
            
            return base_data
            
        else:
            # 如果没有预处理的albedo和shading数据，抛出错误
            raise FileNotFoundError(
                f"预处理的albedo或shading文件不存在。请确保已运行数据预处理步骤。\n"
                f"期望的albedo路径: {data_item.get('albedo_path', 'N/A')}\n"
                f"期望的shading路径: {data_item.get('shading_path', 'N/A')}"
            )
    

class IACRFTestDataset(IACRFDataset):
    """
    IA-CRF测试数据集
    包含正常和异常样本
    """
    
    def __init__(self, class_name, img_size, dataset_path, config):
        # GT变换
        self.gt_transform = transforms.Compose([
            transforms.Resize((224, 224), interpolation=transforms.InterpolationMode.NEAREST),
            transforms.ToTensor()
        ])
        
        super().__init__("test", class_name, img_size, dataset_path, config)
    
    def load_dataset(self):
        """加载测试数据集（正常+异常样本）"""
        data_items = []
        defect_types = os.listdir(self.img_path)
        
        for defect_type in defect_types:
            if defect_type == 'good':
                # 正常样本
                rgb_paths = glob.glob(os.path.join(self.img_path, defect_type, 'rgb') + "/*.png")
                tiff_paths = glob.glob(os.path.join(self.img_path, defect_type, 'xyz') + "/*.tiff")
                gt_paths = glob.glob(os.path.join(self.img_path, defect_type, 'gt') + "/*.png")  # 正常样本也有GT文件
                
                # 获取预处理的albedo和shading路径
                albedo_paths = glob.glob(os.path.join(self.img_path, defect_type, 'rgb', 'albedo') + "/*.png")
                shading_paths = glob.glob(os.path.join(self.img_path, defect_type, 'rgb', 'shading') + "/*.png")
                
                # 获取预计算的features路径
                features_paths = glob.glob(os.path.join(self.img_path, defect_type, 'features') + "/*.npy")
                
                rgb_paths.sort()
                tiff_paths.sort()
                gt_paths.sort()
                albedo_paths.sort()
                shading_paths.sort()
                features_paths.sort()
                
                # 检查是否有预处理数据
                has_preprocessed = len(albedo_paths) > 0 and len(shading_paths) > 0
                has_features = len(features_paths) > 0
                
                # 检查GT文件数量是否匹配
                if len(gt_paths) == 0:
                    print(f"Warning: {defect_type} 目录下未找到GT文件，将使用None作为gt_path")
                elif len(gt_paths) != len(rgb_paths):
                    print(f"Warning: {defect_type} 中RGB和GT文件数量不匹配 (RGB: {len(rgb_paths)}, GT: {len(gt_paths)})")
                    # 如果数量不匹配，则不使用GT文件
                    gt_paths = [None] * len(rgb_paths)
                
                for i, rgb_path in enumerate(rgb_paths):
                    tiff_path = tiff_paths[i] if i < len(tiff_paths) else None
                    gt_path = gt_paths[i] if i < len(gt_paths) and gt_paths[i] is not None else None
                    
                    if tiff_path is None:
                        print(f"Warning: 跳过样本 {rgb_path}，未找到对应的点云文件")
                        continue
                    
                    # 获取文件名用于匹配
                    filename = os.path.splitext(os.path.basename(rgb_path))[0]
                    
                    # 构建对应路径
                    albedo_path = None
                    shading_path = None
                    features_path = None
                    
                    if has_preprocessed:
                        expected_albedo = os.path.join(self.img_path, defect_type, 'rgb', 'albedo', f"{filename}.png")
                        expected_shading = os.path.join(self.img_path, defect_type, 'rgb', 'shading', f"{filename}.png")
                        if os.path.exists(expected_albedo) and os.path.exists(expected_shading):
                            albedo_path = expected_albedo
                            shading_path = expected_shading
                    
                    if has_features:
                        tiff_filename = os.path.splitext(os.path.basename(tiff_path))[0]
                        expected_features = os.path.join(self.img_path, defect_type, 'features', f"{tiff_filename}.npy")
                        if os.path.exists(expected_features):
                            features_path = expected_features
                    
                    data_items.append({
                        'rgb_path': rgb_path,
                        'tiff_path': tiff_path,
                        'albedo_path': albedo_path,
                        'shading_path': shading_path,
                        'features_path': features_path,
                        'gt_path': gt_path,  # 现在正常样本也有GT路径（可能为None或指向全黑GT文件）
                        'label': 0,
                        'defect_type': 'good'
                    })
            else:
                # 异常样本
                rgb_paths = glob.glob(os.path.join(self.img_path, defect_type, 'rgb') + "/*.png")
                tiff_paths = glob.glob(os.path.join(self.img_path, defect_type, 'xyz') + "/*.tiff")
                gt_paths = glob.glob(os.path.join(self.img_path, defect_type, 'gt') + "/*.png")
                
                # 获取预处理的albedo和shading路径
                albedo_paths = glob.glob(os.path.join(self.img_path, defect_type, 'rgb', 'albedo') + "/*.png")
                shading_paths = glob.glob(os.path.join(self.img_path, defect_type, 'rgb', 'shading') + "/*.png")
                
                # 获取预计算的features路径
                features_paths = glob.glob(os.path.join(self.img_path, defect_type, 'features') + "/*.npy")
                
                rgb_paths.sort()
                tiff_paths.sort()
                gt_paths.sort()
                albedo_paths.sort()
                shading_paths.sort()
                features_paths.sort()
                
                # 检查是否有预处理数据
                has_preprocessed = len(albedo_paths) > 0 and len(shading_paths) > 0
                has_features = len(features_paths) > 0
                
                # 检查文件数量匹配
                if len(gt_paths) != len(rgb_paths):
                    print(f"Warning: {defect_type} 中RGB和GT文件数量不匹配 (RGB: {len(rgb_paths)}, GT: {len(gt_paths)})")
                    # 取最小数量保证匹配
                    min_len = min(len(rgb_paths), len(tiff_paths), len(gt_paths))
                    rgb_paths = rgb_paths[:min_len]
                    tiff_paths = tiff_paths[:min_len]
                    gt_paths = gt_paths[:min_len]
                
                for i, (rgb_path, tiff_path, gt_path) in enumerate(zip(rgb_paths, tiff_paths, gt_paths)):
                    # 获取文件名用于匹配
                    filename = os.path.splitext(os.path.basename(rgb_path))[0]
                    
                    # 构建对应路径
                    albedo_path = None
                    shading_path = None
                    features_path = None
                    
                    if has_preprocessed:
                        expected_albedo = os.path.join(self.img_path, defect_type, 'rgb', 'albedo', f"{filename}.png")
                        expected_shading = os.path.join(self.img_path, defect_type, 'rgb', 'shading', f"{filename}.png")
                        if os.path.exists(expected_albedo) and os.path.exists(expected_shading):
                            albedo_path = expected_albedo
                            shading_path = expected_shading
                    
                    if has_features:
                        tiff_filename = os.path.splitext(os.path.basename(tiff_path))[0]
                        expected_features = os.path.join(self.img_path, defect_type, 'features', f"{tiff_filename}.npy")
                        if os.path.exists(expected_features):
                            features_path = expected_features
                    
                    data_items.append({
                        'rgb_path': rgb_path,
                        'tiff_path': tiff_path,
                        'albedo_path': albedo_path,
                        'shading_path': shading_path,
                        'features_path': features_path,
                        'gt_path': gt_path,
                        'label': 1,
                        'defect_type': defect_type
                    })
        
        # print(f"加载测试数据: {len(data_items)} 个样本")
        return data_items
    
    def load_ground_truth(self, gt_path):
        """加载ground truth掩码"""
        if gt_path is None:
            # 正常样本，返回零掩码
            return torch.zeros(1, 224, 224)
        else:
            gt = Image.open(gt_path).convert('L')
            gt = self.gt_transform(gt)
            gt = torch.where(gt > 0.5, 1.0, 0.0)
            return gt
    
    def __getitem__(self, idx):
        data_item = self.data_items[idx]
        
        # 加载RGB图像
        rgb_image = self.load_rgb_image(data_item['rgb_path'])
        
        # 加载有组织点云
        organized_pointcloud = self.load_organized_pointcloud(data_item['tiff_path'])
        
        # 加载ground truth
        gt_mask = self.load_ground_truth(data_item['gt_path'])
        
        # 检查是否有预处理的albedo和shading数据
        if data_item.get('albedo_path') is not None and data_item.get('shading_path') is not None:
            # 加载预处理的albedo和shading图像
            albedo = self.load_albedo_image(data_item['albedo_path'])
            shading = self.load_shading_image(data_item['shading_path'])
            
            # 加载预计算的3D几何特征
            enhanced_pointcloud = self._load_precomputed_features(
                data_item['tiff_path'],
                data_item['features_path']
            )
            
            if enhanced_pointcloud is None:
                # 如果没有预计算特征，使用简化的点云处理
                if self.config['logging']['verbose']:
                    print(f"Warning: 未找到预计算特征，使用简化点云处理")
                H, W, C = organized_pointcloud.shape
                enhanced_pointcloud = organized_pointcloud.reshape(-1, C)  # [H*W, 3]
            
            return {
                'rgb_image': rgb_image,
                'organized_pointcloud': torch.from_numpy(organized_pointcloud).float(),
                'albedo': albedo,  # 预处理的albedo
                'shading': shading,  # 预处理的shading
                'enhanced_pointcloud': torch.from_numpy(enhanced_pointcloud).float(),
                'gt_mask': gt_mask,
                'label': data_item['label'],
                'defect_type': data_item['defect_type'],
                'rgb_path': data_item['rgb_path'],
                'tiff_path': data_item['tiff_path']
            }
        else:
            # 如果没有预处理的albedo和shading数据，抛出错误
            raise FileNotFoundError(
                f"预处理的albedo或shading文件不存在。请确保已运行数据预处理步骤。\n"
                f"期望的albedo路径: {data_item.get('albedo_path', 'N/A')}\n"
                f"期望的shading路径: {data_item.get('shading_path', 'N/A')}"
            )


def get_iacrf_dataloader(split, class_name, dataset_path, config, 
                        batch_size=1, shuffle=False, num_workers=0,
                        enable_pseudo_anomaly=True, anomaly_source_path=None):
    """
    获取IA-CRF数据加载器
    
    Args:
        split: 数据集分割类型 ('train' 或 'test')
        class_name: MVTec 3D-AD类别名称
        dataset_path: 数据集路径
        config: 配置字典
        batch_size: 批次大小
        shuffle: 是否打乱数据
        num_workers: 数据加载工作进程数
        enable_pseudo_anomaly: 是否启用伪异常生成（仅训练时有效）
        anomaly_source_path: 异常纹理源路径（如DTD数据集）
        
    Returns:
        DataLoader: 配置好的数据加载器
    """
    img_size = (224, 224)  # (height, width)
    
    if split == 'train':
        dataset = IACRFTrainDataset(
            class_name=class_name,
            img_size=img_size, 
            dataset_path=dataset_path,
            config=config,
            enable_pseudo_anomaly=enable_pseudo_anomaly,
            anomaly_source_path=anomaly_source_path
        )
    elif split == 'test':
        dataset = IACRFTestDataset(
            class_name=class_name,
            img_size=img_size,
            dataset_path=dataset_path, 
            config=config
        )
    else:
        raise ValueError(f"不支持的split类型: {split}")
    
    dataloader = DataLoader(
        dataset=dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers,
        drop_last=False,
        pin_memory=True
    )
    
    return dataloader


def test_iacrf_dataset():
    """
    测试IA-CRF数据集加载器
    """
    # 配置参数
    config = {
        'logging': {
            'verbose': True
        },
        'pseudo_anomaly': {
            'geometric': {
                'displacement_strength_range': [0.02, 0.08],
                'num_patches_range': [1, 3],
                'patch_coverage_ratio': 0.15,
                'probability': 0.7
            },
            'material': {
                'probability': 0.5
            }
        }
    }
    
    # 数据集路径（需要根据实际情况修改）
    dataset_path = "/path/to/mvtec3d/"  # 替换为实际路径
    class_name = "bagel"
    
    try:
        # 测试训练数据加载器
        print("测试训练数据加载器...")
        train_loader = get_iacrf_dataloader(
            split='train',
            class_name=class_name,
            dataset_path=dataset_path,
            config=config,
            batch_size=1,
            enable_pseudo_anomaly=True
        )
        
        # 加载一个批次
        for i, batch in enumerate(train_loader):
            print(f"\n训练批次 {i}:")
            print(f"  RGB image shape: {batch['rgb_image'].shape}")
            print(f"  Albedo shape: {batch['albedo'].shape}")
            print(f"  Shading shape: {batch['shading'].shape}")
            print(f"  Point cloud shape: {batch['organized_pointcloud'].shape}")
            print(f"  Enhanced point cloud shape: {batch['enhanced_pointcloud'].shape}")
            print(f"  Label: {batch['label']}")
            print(f"  Anomaly type: {batch.get('anomaly_type', 'N/A')}")
            
            if i >= 2:  # 只测试3个批次
                break
        
        print("\n训练数据加载器测试成功!")
        
        # 测试测试数据加载器
        print("\n测试测试数据加载器...")
        test_loader = get_iacrf_dataloader(
            split='test',
            class_name=class_name,
            dataset_path=dataset_path,
            config=config,
            batch_size=1
        )
        
        # 加载一个批次
        for i, batch in enumerate(test_loader):
            print(f"\n测试批次 {i}:")
            print(f"  RGB image shape: {batch['rgb_image'].shape}")
            print(f"  Albedo shape: {batch['albedo'].shape}")
            print(f"  Shading shape: {batch['shading'].shape}")
            print(f"  Point cloud shape: {batch['organized_pointcloud'].shape}")
            print(f"  Enhanced point cloud shape: {batch['enhanced_pointcloud'].shape}")
            print(f"  GT mask shape: {batch['gt_mask'].shape}")
            print(f"  Label: {batch['label']}")
            print(f"  Defect type: {batch['defect_type']}")
            
            if i >= 2:  # 只测试3个批次
                break
        
        print("\n测试数据加载器测试成功!")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_iacrf_dataset()
