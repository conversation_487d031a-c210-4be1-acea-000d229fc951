#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Geometric Anomaly Generation
Simplified test script for geometric anomaly generation workflow
Automatically selects random samples from MVTec 3D-AD dataset for testing

Author: IA-CRF Project
Date: 2025-08-28

Usage:
python test_geometric_anomaly_generation.py --category bagel
python test_geometric_anomaly_generation.py --category cable_gland --preset moderate_deformation
"""

import os
import sys
import argparse
import numpy as np
import cv2
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime
import random
import glob

# Add project path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from pseudo_anomaly_generation.geometric import GeometricAnomalyGenerationSystem


class GeometricAnomalyTester:
    """Geometric Anomaly Generation Tester - Simplified Version"""
    
    def __init__(self, config_preset: str = "subtle_deformation"):
        """
        Initialize tester
        
        Args:
            config_preset: Configuration preset name
        """
        self.config_preset = config_preset
        self.system = None
        self.dataset_path = "/raid/liulinna/projects/M3DM/datasets/mvtec3d/"
        
        # Create output directory
        self.output_dir = Path("geometric_anomaly_test_results")
        self.output_dir.mkdir(exist_ok=True)
        
        print(f"Geometric Anomaly Tester initialized")
        print(f"Dataset path: {self.dataset_path}")
        print(f"Output directory: {self.output_dir.absolute()}")
    
    def setup_system(self, category: str = None):
        """
        Setup geometric anomaly generation system
        
        Args:
            category: Optional MVTec 3D-AD category for specific configuration
        """
        try:
            # Initialize system
            self.system = GeometricAnomalyGenerationSystem(self.config_preset)
            
            # Set category-specific configuration if provided
            if category:
                self.system.set_category_specific_config(category)
                print(f"Using category-specific configuration for: {category}")
            
            print("Geometric anomaly generation system setup complete")
            print("Configuration summary:")
            config_summary = self.system.get_config_summary()
            for key, value in config_summary.items():
                print(f"  {key}: {value}")
            
        except Exception as e:
            raise RuntimeError(f"System setup failed: {e}")
    
    def load_random_sample_from_category(self, category: str) -> tuple:
        """
        Load a random sample from the specified category
        
        Args:
            category: MVTec 3D-AD category name
            
        Returns:
            Tuple of (features_path, shading_path)
        """
        # Construct paths for the category
        category_train_path = os.path.join(self.dataset_path, category, "train", "good")
        xyz_dir = os.path.join(category_train_path, "xyz")
        rgb_dir = os.path.join(category_train_path, "rgb")
        features_dir = os.path.join(category_train_path, "features")  # Corrected path - same level as xyz
        
        # Check if directories exist
        if not os.path.exists(category_train_path):
            raise FileNotFoundError(f"Category train path not found: {category_train_path}")
        if not os.path.exists(xyz_dir):
            raise FileNotFoundError(f"XYZ directory not found: {xyz_dir}")
        if not os.path.exists(rgb_dir):
            raise FileNotFoundError(f"RGB directory not found: {rgb_dir}")
        if not os.path.exists(features_dir):
            raise FileNotFoundError(f"Features directory not found: {features_dir}")
        
        # Get all feature files
        feature_files = glob.glob(os.path.join(features_dir, "*.npy"))
        if not feature_files:
            raise FileNotFoundError(f"No feature files found in: {features_dir}")
        
        # Randomly select a feature file
        selected_feature_file = random.choice(feature_files)
        sample_name = os.path.splitext(os.path.basename(selected_feature_file))[0]
        
        # Construct corresponding shading path
        shading_path = os.path.join(rgb_dir, "shading", f"{sample_name}.png")
        if not os.path.exists(shading_path):
            print(f"Warning: Shading image not found: {shading_path}")
            shading_path = None
        
        print(f"Selected sample: {sample_name}")
        print(f"Features path: {selected_feature_file}")
        if shading_path:
            print(f"Shading path: {shading_path}")
        else:
            print("No shading image available")
        
        return selected_feature_file, shading_path
    
    def load_test_features(self, features_path: str) -> np.ndarray:
        """
        Load test geometric features
        
        Args:
            features_path: Path to .npy features file
            
        Returns:
            Geometric features [H*W, 7]
        """
        if not Path(features_path).exists():
            raise FileNotFoundError(f"Features file not found: {features_path}")
        
        features = np.load(features_path)
        
        if len(features.shape) != 2:
            raise ValueError(f"Expected features shape [N, D], got {features.shape}")
        
        print(f"Loaded geometric features: {features.shape}")
        print(f"Features format: [x, y, z, nx, ny, nz, curvature]")
        
        # Calculate statistics
        valid_mask = np.abs(features[:, :3]).sum(axis=1) > 1e-6
        valid_points = np.sum(valid_mask)
        total_points = features.shape[0]
        
        print(f"Valid points: {valid_points}/{total_points} ({valid_points/total_points*100:.1f}%)")
        
        return features
    
    def test_single_sample(
        self,
        features: np.ndarray,
        shading_path: str = None,
        category: str = None,
        custom_params: dict = None
    ) -> dict:
        """
        Test geometric anomaly generation on single sample
        
        Args:
            features: Geometric features [H*W, 7]
            shading_path: Optional path to shading image
            category: Optional category for configuration
            custom_params: Optional custom parameters
            
        Returns:
            Anomaly generation results
        """
        if self.system is None:
            self.setup_system(category)
        
        # Load shading if provided
        shading = None
        if shading_path and Path(shading_path).exists():
            try:
                # 改进的shading读取流程，确保与手动指定路径时一致
                shading = cv2.imread(shading_path, cv2.IMREAD_COLOR)
                if shading is not None:
                    # 确保图像读取成功
                    original_shape = shading.shape
                    print(f"Successfully loaded shading image: {original_shape}")
                    
                    # 转换色彩空间并归一化
                    shading = cv2.cvtColor(shading, cv2.COLOR_BGR2RGB).astype(np.float32) / 255.0
                    
                    # 检查像素值范围，确保在合理范围内
                    pixel_min, pixel_max = shading.min(), shading.max()
                    print(f"Shading pixel value range: [{pixel_min:.6f}, {pixel_max:.6f}]")
                    
                    if pixel_min < 0 or pixel_max > 1.0:
                        print(f"Warning: Shading pixel values out of [0,1] range, clipping...")
                        shading = np.clip(shading, 0.0, 1.0)
                    
                    # Resize to 224x224 to match point cloud dimensions
                    shading = cv2.resize(shading, (224, 224), interpolation=cv2.INTER_LINEAR)
                    final_shape = shading.shape
                    
                    # 再次检查resize后的像素值
                    resized_min, resized_max = shading.min(), shading.max()
                    print(f"After resize {original_shape} -> {final_shape}: pixel range [{resized_min:.6f}, {resized_max:.6f}]")
                    
                    # 如果resize后出现异常值，进行修正
                    if resized_min < 0 or resized_max > 1.0:
                        print(f"Warning: Resize introduced out-of-range values, correcting...")
                        shading = np.clip(shading, 0.0, 1.0)
                        print(f"After correction: pixel range [{shading.min():.6f}, {shading.max():.6f}]")
                    
                else:
                    print(f"Error: Failed to load shading image from {shading_path}")
                    
            except Exception as e:
                print(f"Error loading shading image: {e}")
                shading = None
        else:
            if shading_path:
                print(f"Warning: Shading path provided but file does not exist: {shading_path}")
            else:
                print("No shading path provided")
        
        # Generate geometric anomaly
        print("Generating geometric anomaly...")
        result = self.system.generate_single_anomaly(features, shading, custom_params)
        
        # Print results summary
        print(f"Anomaly generation complete:")
        print(f"  Has anomaly: {'Yes' if result['has_anomaly'][0] > 0 else 'No'}")
        print(f"  Anomaly type: {result['anomaly_type']}")
        
        if result['has_anomaly'][0] > 0 and 'generation_params' in result:
            params = result['generation_params']
            print(f"  Number of patches: {params['num_patches']}")
            print(f"  Total displaced points: {params['total_displaced_points']}")
            print(f"  Max displacement: {params['max_displacement']:.4f}")
            print(f"  Shading change magnitude: {params['shading_change_magnitude']:.4f}")
        
        return result
    
    def visualize_pointcloud_comparison(
        self,
        result: dict,
        save_name: str,
        category: str
    ):
        """
        可视化生成异常前后的点云对比图，保存为二维图像
        
        Args:
            result: 异常生成结果
            save_name: 保存文件名
            category: 类别名称
        """
        if not result['has_anomaly'][0] > 0:
            print("No anomaly generated, skipping pointcloud comparison visualization")
            return
        
        # 获取原始和变形后的点云
        original_pc = result['original_pointcloud']  # [H, W, 3]
        deformed_pc = result['deformed_pointcloud']  # [H, W, 3]
        
        if original_pc is None or deformed_pc is None:
            print("Warning: Point cloud data not available")
            return
        
        H, W, _ = original_pc.shape
        print(f"Creating pointcloud comparison visualization for {category}...")
        print(f"Point cloud dimensions: {H}x{W}")
        
        # 创建图形和子图
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'Point Cloud Anomaly Generation Comparison - {category.upper()}', fontsize=16, fontweight='bold')
        
        # 计算深度图（Z坐标）
        original_depth = original_pc[:, :, 2]
        deformed_depth = deformed_pc[:, :, 2]
        
        # 计算深度差异
        depth_diff = deformed_depth - original_depth
        
        # 创建有效点掩码（过滤背景点）
        valid_mask = np.abs(original_pc).sum(axis=2) > 1e-6
        
        # 获取异常区域掩码
        anomaly_mask = np.zeros((H, W), dtype=bool)
        if 'generation_params' in result and result['patches_info']:
            for patch_info in result['patches_info']:
                if 'patch_mask' in patch_info:
                    anomaly_mask |= patch_info['patch_mask']
        
        print(f"Depth range analysis:")
        print(f"  Original depth range: [{original_depth[valid_mask].min():.6f}, {original_depth[valid_mask].max():.6f}]")
        print(f"  Deformed depth range: [{deformed_depth[valid_mask].min():.6f}, {deformed_depth[valid_mask].max():.6f}]")
        print(f"  Depth difference range: [{depth_diff[valid_mask].min():.6f}, {depth_diff[valid_mask].max():.6f}]")
        if np.sum(anomaly_mask) > 0:
            print(f"  Anomaly region depth changes: [{depth_diff[anomaly_mask].min():.6f}, {depth_diff[anomaly_mask].max():.6f}]")
        
        # 设置colormap
        depth_cmap = 'viridis'
        diff_cmap = 'RdBu_r'
        
        # 计算深度图的合理显示范围（增强对比度）
        valid_original = original_depth[valid_mask]
        valid_deformed = deformed_depth[valid_mask]
        
        # 使用百分位数来提高对比度
        depth_min = np.percentile(valid_original, 1)  # 使用1%和99%百分位数
        depth_max = np.percentile(valid_original, 99)
        
        # 1. 原始点云深度图（优化对比度）
        im1 = axes[0, 0].imshow(original_depth, cmap=depth_cmap, aspect='equal', 
                               vmin=depth_min, vmax=depth_max)
        axes[0, 0].set_title('Original Point Cloud\n(Depth Map)', fontsize=12, fontweight='bold')
        axes[0, 0].set_xlabel('Width (pixels)')
        axes[0, 0].set_ylabel('Height (pixels)')
        plt.colorbar(im1, ax=axes[0, 0], fraction=0.046, pad=0.04, label='Depth (m)')
        
        # 2. 变形后点云深度图（使用相同的显示范围）
        im2 = axes[0, 1].imshow(deformed_depth, cmap=depth_cmap, aspect='equal',
                               vmin=depth_min, vmax=depth_max)
        axes[0, 1].set_title('Deformed Point Cloud\n(After Anomaly Generation)', fontsize=12, fontweight='bold')
        axes[0, 1].set_xlabel('Width (pixels)')
        axes[0, 1].set_ylabel('Height (pixels)')
        plt.colorbar(im2, ax=axes[0, 1], fraction=0.046, pad=0.04, label='Depth (m)')
        
        # 3. 深度差异图（大幅优化异常区域可见性）
        # 只在有效区域显示差异
        masked_diff = np.ma.masked_where(~valid_mask, depth_diff)
        
        # 计算差异的智能显示范围
        valid_diff = depth_diff[valid_mask & (np.abs(depth_diff) > 1e-8)]
        if len(valid_diff) > 0:
            # 使用异常区域的实际变化范围
            if np.sum(anomaly_mask) > 0:
                anomaly_diff = depth_diff[anomaly_mask & valid_mask]
                if len(anomaly_diff) > 0:
                    # 基于异常区域的变化设置显示范围
                    anomaly_abs_max = np.max(np.abs(anomaly_diff))
                    display_range = max(anomaly_abs_max * 1.2, 0.0001)  # 至少0.1mm
                    vmin, vmax = -display_range, display_range
                    print(f"  Using anomaly-based display range: [{vmin:.6f}, {vmax:.6f}]")
                else:
                    vmin, vmax = -0.001, 0.001
            else:
                # 使用所有变化的统计信息
                diff_abs_max = np.max(np.abs(valid_diff))
                display_range = max(diff_abs_max * 1.5, 0.0001)
                vmin, vmax = -display_range, display_range
                print(f"  Using statistical display range: [{vmin:.6f}, {vmax:.6f}]")
        else:
            vmin, vmax = -0.001, 0.001
            print(f"  Using default display range: [{vmin:.6f}, {vmax:.6f}]")
        
        im3 = axes[0, 2].imshow(masked_diff, cmap=diff_cmap, aspect='equal', vmin=vmin, vmax=vmax)
        axes[0, 2].set_title('Depth Difference\n(Anomaly Regions Enhanced)', fontsize=12, fontweight='bold')
        axes[0, 2].set_xlabel('Width (pixels)')
        axes[0, 2].set_ylabel('Height (pixels)')
        cbar3 = plt.colorbar(im3, ax=axes[0, 2], fraction=0.046, pad=0.04, label='Depth Change (m)')
        
        # 4. 异常区域可视化（改进版）
        if 'generation_params' in result and result['patches_info'] and np.sum(anomaly_mask) > 0:
            # 创建组合可视化：深度图 + 异常区域高亮
            # 使用原始深度作为背景
            background = axes[1, 0].imshow(original_depth, cmap='gray', alpha=0.6, aspect='equal',
                                          vmin=depth_min, vmax=depth_max)
            
            # 突出显示异常区域
            anomaly_overlay = np.zeros((H, W, 4))  # RGBA
            anomaly_overlay[anomaly_mask, 0] = 1.0  # 红色
            anomaly_overlay[anomaly_mask, 3] = 0.8  # 透明度
            axes[1, 0].imshow(anomaly_overlay, aspect='equal')
            
            # 添加边界线显示
            from scipy.ndimage import binary_dilation, binary_erosion
            boundary = binary_dilation(anomaly_mask, iterations=2) & (~binary_erosion(anomaly_mask, iterations=1))
            boundary_overlay = np.zeros((H, W, 4))
            boundary_overlay[boundary, :3] = [1.0, 1.0, 0.0]  # 黄色边界
            boundary_overlay[boundary, 3] = 1.0
            axes[1, 0].imshow(boundary_overlay, aspect='equal')
            
            axes[1, 0].set_title(f'Anomaly Patches\n(Red = Anomaly, Yellow = Boundary)\nCoverage: {np.sum(anomaly_mask)}/{H*W} pixels ({np.sum(anomaly_mask)/(H*W)*100:.2f}%)', 
                                fontsize=12, fontweight='bold')
            axes[1, 0].set_xlabel('Width (pixels)')
            axes[1, 0].set_ylabel('Height (pixels)')
            
            print(f"  Anomaly mask coverage: {np.sum(anomaly_mask)} pixels ({np.sum(anomaly_mask)/(H*W)*100:.2f}%)")
        else:
            axes[1, 0].text(0.5, 0.5, 'No Anomaly\nMask Available', 
                           ha='center', va='center', transform=axes[1, 0].transAxes,
                           fontsize=14, bbox=dict(boxstyle='round', facecolor='lightgray'))
            axes[1, 0].set_title('Anomaly Patches', fontsize=12, fontweight='bold')
        
        # 5. 表面法线变化与局部放大显示
        if original_pc.shape[2] >= 3 and np.sum(anomaly_mask) > 0:
            # 计算表面法线的变化（改进版本）
            def compute_surface_normals_enhanced(pc):
                """计算增强版表面法线"""
                # 使用Sobel算子计算更精确的梯度
                from scipy import ndimage
                depth = pc[:, :, 2]
                grad_x = ndimage.sobel(depth, axis=1)
                grad_y = ndimage.sobel(depth, axis=0)
                
                # 计算法线向量的模长
                normal_magnitude = np.sqrt(grad_x**2 + grad_y**2 + 1e-8)
                return normal_magnitude, grad_x, grad_y
            
            original_normals, orig_gx, orig_gy = compute_surface_normals_enhanced(original_pc)
            deformed_normals, def_gx, def_gy = compute_surface_normals_enhanced(deformed_pc)
            normal_diff = deformed_normals - original_normals
            
            # 只在异常区域显示变化
            anomaly_normal_diff = np.zeros_like(normal_diff)
            anomaly_normal_diff[anomaly_mask] = normal_diff[anomaly_mask]
            
            # 计算显示范围
            if np.sum(anomaly_mask) > 0:
                anomaly_normal_changes = normal_diff[anomaly_mask]
                normal_range = max(np.max(np.abs(anomaly_normal_changes)) * 1.2, 1e-6)
            else:
                normal_range = 1e-3
            
            im5 = axes[1, 1].imshow(anomaly_normal_diff, cmap=diff_cmap, aspect='equal',
                                   vmin=-normal_range, vmax=normal_range)
            axes[1, 1].set_title('Surface Normal Changes\n(Enhanced Gradient Analysis)', fontsize=12, fontweight='bold')
            axes[1, 1].set_xlabel('Width (pixels)')
            axes[1, 1].set_ylabel('Height (pixels)')
            plt.colorbar(im5, ax=axes[1, 1], fraction=0.046, pad=0.04, label='Normal Change')
            
            print(f"  Normal change range: [{-normal_range:.6f}, {normal_range:.6f}]")
            
        elif np.sum(anomaly_mask) == 0:
            # 如果没有异常，显示提示信息
            axes[1, 1].text(0.5, 0.5, 'No Anomaly\nGenerated', 
                           ha='center', va='center', transform=axes[1, 1].transAxes,
                           fontsize=14, bbox=dict(boxstyle='round', facecolor='lightyellow'))
            axes[1, 1].set_title('Surface Normal Changes', fontsize=12, fontweight='bold')
        else:
            # 数据不可用
            axes[1, 1].text(0.5, 0.5, 'Normal Data\nNot Available', 
                           ha='center', va='center', transform=axes[1, 1].transAxes,
                           fontsize=14, bbox=dict(boxstyle='round', facecolor='lightgray'))
            axes[1, 1].set_title('Surface Normal Changes', fontsize=12, fontweight='bold')
        
        # 6. 增强统计信息显示
        axes[1, 2].axis('off')  # 隐藏轴
        
        # 创建详细统计信息文本
        stats_text = []
        stats_text.append(f"Category: {category.upper()}")
        stats_text.append(f"Point Cloud Size: {H} × {W}")
        stats_text.append(f"Valid Points: {np.sum(valid_mask):,}/{H*W:,} ({np.sum(valid_mask)/(H*W)*100:.1f}%)")
        
        if result['has_anomaly'][0] > 0:
            stats_text.append(f"\n✅ Anomaly Generated")
            if 'generation_params' in result:
                params = result['generation_params']
                stats_text.append(f"Patches: {params.get('num_patches', 'N/A')}")
                stats_text.append(f"Displaced Points: {params.get('total_displaced_points', 'N/A'):,}")
                stats_text.append(f"Max Displacement: {params.get('max_displacement', 0):.4f}m")
                
            # 异常区域统计
            if np.sum(anomaly_mask) > 0:
                stats_text.append(f"\nAnomaly Region Analysis:")
                stats_text.append(f"Anomaly Pixels: {np.sum(anomaly_mask):,} ({np.sum(anomaly_mask)/(H*W)*100:.2f}%)")
                
                # 异常区域的深度变化
                anomaly_depth_changes = depth_diff[anomaly_mask]
                if len(anomaly_depth_changes) > 0:
                    stats_text.append(f"Depth Changes in Anomaly:")
                    stats_text.append(f"  Mean: {np.mean(anomaly_depth_changes):.6f}m")
                    stats_text.append(f"  Std: {np.std(anomaly_depth_changes):.6f}m")
                    stats_text.append(f"  Range: [{np.min(anomaly_depth_changes):.6f}, {np.max(anomaly_depth_changes):.6f}]m")
        else:
            stats_text.append(f"\n❌ No Anomaly Generated")
        
        # 全局深度变化统计
        if len(valid_diff) > 0:
            stats_text.append(f"\nGlobal Depth Changes:")
            stats_text.append(f"Changed Points: {len(valid_diff):,}")
            stats_text.append(f"Mean Change: {np.mean(valid_diff):.6f}m")
            stats_text.append(f"Max |Change|: {np.max(np.abs(valid_diff)):.6f}m")
            
            # 变化分布分析
            positive_changes = np.sum(valid_diff > 1e-8)
            negative_changes = np.sum(valid_diff < -1e-8)
            stats_text.append(f"Positive Changes: {positive_changes:,}")
            stats_text.append(f"Negative Changes: {negative_changes:,}")
        
        # 显示统计信息
        stats_display = '\n'.join(stats_text)
        axes[1, 2].text(0.05, 0.95, stats_display, 
                        transform=axes[1, 2].transAxes, 
                        fontsize=10, 
                        verticalalignment='top',
                        bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8))
        axes[1, 2].set_title('Detailed Analysis', fontsize=12, fontweight='bold')
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图像
        save_path = self.output_dir / f"{save_name}_pointcloud_comparison.png"
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Point cloud comparison visualization saved: {save_path}")
        
        # 显示图像（可选）
        # plt.show()
        
        # 清理内存
        plt.close(fig)
    
    def visualize_lighting_intensity_comparison(
        self,
        result: dict,
        save_name: str,
        category: str
    ):
        """
        可视化原始和异常后的 Lighting Intensity 对比图
        以光照强度的方式展现原始点云和异常后点云
        
        Args:
            result: 异常生成结果
            save_name: 保存文件名
            category: 类别名称
        """
        if not result['has_anomaly'][0] > 0:
            print("No anomaly generated, skipping lighting intensity comparison visualization")
            return
        
        # 获取原始和变形后的点云
        original_pc = result['original_pointcloud']  # [H, W, 3]
        deformed_pc = result['deformed_pointcloud']  # [H, W, 3]
        
        if original_pc is None or deformed_pc is None:
            print("Warning: Point cloud data not available for lighting intensity visualization")
            return
        
        # 获取 shading 相关数据
        original_shading = result.get('original_shading', None)
        modified_shading = result.get('modified_shading', None)
        shading_results = result.get('shading_results', None)
        
        H, W, _ = original_pc.shape
        print(f"Creating lighting intensity comparison visualization for {category}...")
        print(f"Point cloud dimensions: {H}x{W}")
        
        # 创建图形和子图 - 2行3列布局
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'Lighting Intensity Comparison - {category.upper()}', fontsize=16, fontweight='bold')
        
        # 如果有 shading transformation 数据，计算原始的 lighting intensity
        if shading_results is not None:
            # 从 shading_results 中获取lighting相关信息
            intensity_changes = shading_results.get('intensity_changes', None)
            
            # 尝试重新计算原始光照强度
            if hasattr(self.system.generator, 'shading_transformer'):
                transformer = self.system.generator.shading_transformer
                
                # 计算原始法线
                try:
                    original_normals = transformer.recalculate_normals_after_deformation(original_pc)
                    deformed_normals = transformer.recalculate_normals_after_deformation(deformed_pc)
                    
                    # 计算原始和变形后的光照强度
                    original_lighting = transformer._calculate_diffuse_lighting(original_normals)
                    deformed_lighting = transformer._calculate_diffuse_lighting(deformed_normals)
                    
                    print(f"Successfully calculated lighting intensities using transformer")
                    
                except Exception as e:
                    print(f"Error calculating lighting with transformer: {e}")
                    # 使用简化方法计算光照
                    original_lighting, deformed_lighting = self._calculate_simple_lighting(original_pc, deformed_pc)
            else:
                print("No shading transformer available, using simplified lighting calculation")
                original_lighting, deformed_lighting = self._calculate_simple_lighting(original_pc, deformed_pc)
        else:
            print("No shading results available, using simplified lighting calculation")
            original_lighting, deformed_lighting = self._calculate_simple_lighting(original_pc, deformed_pc)
        
        # 计算光照强度差异
        lighting_difference = deformed_lighting - original_lighting
        
        # 创建有效点掩码
        valid_mask = np.abs(original_pc).sum(axis=2) > 1e-6
        
        # 获取异常区域掩码
        anomaly_mask = np.zeros((H, W), dtype=bool)
        if 'generation_params' in result and result['patches_info']:
            for patch_info in result['patches_info']:
                if 'patch_mask' in patch_info:
                    anomaly_mask |= patch_info['patch_mask']
        
        # 设置显示参数
        lighting_cmap = 'plasma'  # 使用plasma色彩映射表现光照强度
        diff_cmap = 'RdBu_r'      # 差异图使用红蓝配色
        
        # 计算合理的显示范围
        valid_orig_lighting = original_lighting[valid_mask]
        valid_def_lighting = deformed_lighting[valid_mask]
        
        if len(valid_orig_lighting) > 0:
            lighting_min = np.percentile(valid_orig_lighting, 1)
            lighting_max = np.percentile(valid_orig_lighting, 99)
        else:
            lighting_min, lighting_max = 0, 1
        
        # 1. 原始点云的光照强度
        im1 = axes[0, 0].imshow(original_lighting, cmap=lighting_cmap, aspect='equal',
                               vmin=lighting_min, vmax=lighting_max)
        axes[0, 0].set_title('Original Point Cloud\n(Lighting Intensity)', fontsize=12, fontweight='bold')
        axes[0, 0].set_xlabel('Width (pixels)')
        axes[0, 0].set_ylabel('Height (pixels)')
        plt.colorbar(im1, ax=axes[0, 0], fraction=0.046, pad=0.04, label='Lighting Intensity')
        
        # 2. 变形后点云的光照强度
        im2 = axes[0, 1].imshow(deformed_lighting, cmap=lighting_cmap, aspect='equal',
                               vmin=lighting_min, vmax=lighting_max)
        axes[0, 1].set_title('Anomaly Point Cloud\n(Lighting Intensity)', fontsize=12, fontweight='bold')
        axes[0, 1].set_xlabel('Width (pixels)')
        axes[0, 1].set_ylabel('Height (pixels)')
        plt.colorbar(im2, ax=axes[0, 1], fraction=0.046, pad=0.04, label='Lighting Intensity')
        
        # 3. 光照强度差异图
        masked_diff = np.ma.masked_where(~valid_mask, lighting_difference)
        
        # 计算差异的显示范围
        if np.sum(anomaly_mask) > 0:
            anomaly_diff = lighting_difference[anomaly_mask & valid_mask]
            if len(anomaly_diff) > 0:
                diff_range = max(np.max(np.abs(anomaly_diff)) * 1.2, 0.01)
            else:
                diff_range = 0.1
        else:
            valid_diff = lighting_difference[valid_mask]
            if len(valid_diff) > 0:
                diff_range = max(np.max(np.abs(valid_diff)) * 1.5, 0.01)
            else:
                diff_range = 0.1
        
        vmin, vmax = -diff_range, diff_range
        im3 = axes[0, 2].imshow(masked_diff, cmap=diff_cmap, aspect='equal', vmin=vmin, vmax=vmax)
        axes[0, 2].set_title('Lighting Intensity Difference\n(Enhanced View)', fontsize=12, fontweight='bold')
        axes[0, 2].set_xlabel('Width (pixels)')
        axes[0, 2].set_ylabel('Height (pixels)')
        plt.colorbar(im3, ax=axes[0, 2], fraction=0.046, pad=0.04, label='Intensity Change')
        
        # 4. 原始 Shading 图像（如果可用）
        if original_shading is not None:
            axes[1, 0].imshow(original_shading)
            axes[1, 0].set_title('Original Shading Image', fontsize=12, fontweight='bold')
        else:
            # 如果没有原始shading，使用深度图替代
            depth_img = original_pc[:, :, 2]
            axes[1, 0].imshow(depth_img, cmap='viridis')
            axes[1, 0].set_title('Original Depth Map\n(No Shading Available)', fontsize=12, fontweight='bold')
        axes[1, 0].set_xlabel('Width (pixels)')
        axes[1, 0].set_ylabel('Height (pixels)')
        axes[1, 0].axis('on')
        
        # 5. 修改后的 Shading 图像（如果可用）
        if modified_shading is not None:
            axes[1, 1].imshow(modified_shading)
            axes[1, 1].set_title('Modified Shading Image', fontsize=12, fontweight='bold')
        else:
            # 如果没有修改后的shading，使用变形后深度图
            def_depth_img = deformed_pc[:, :, 2]
            axes[1, 1].imshow(def_depth_img, cmap='viridis')
            axes[1, 1].set_title('Deformed Depth Map\n(No Modified Shading)', fontsize=12, fontweight='bold')
        axes[1, 1].set_xlabel('Width (pixels)')
        axes[1, 1].set_ylabel('Height (pixels)')
        axes[1, 1].axis('on')
        
        # 6. 异常区域高亮 + 统计信息
        if np.sum(anomaly_mask) > 0:
            # 创建异常区域可视化
            background = axes[1, 2].imshow(original_lighting, cmap='gray', alpha=0.6, aspect='equal',
                                          vmin=lighting_min, vmax=lighting_max)
            
            # 高亮异常区域
            anomaly_overlay = np.zeros((H, W, 4))  # RGBA
            anomaly_overlay[anomaly_mask, 0] = 1.0  # 红色
            anomaly_overlay[anomaly_mask, 3] = 0.7  # 透明度
            axes[1, 2].imshow(anomaly_overlay, aspect='equal')
            
            axes[1, 2].set_title(f'Anomaly Regions\n({np.sum(anomaly_mask)} pixels, {np.sum(anomaly_mask)/(H*W)*100:.2f}%)', 
                                fontsize=12, fontweight='bold')
        else:
            axes[1, 2].text(0.5, 0.5, 'No Anomaly\nRegions Detected', 
                           ha='center', va='center', transform=axes[1, 2].transAxes,
                           fontsize=14, bbox=dict(boxstyle='round', facecolor='lightgray'))
            axes[1, 2].set_title('Anomaly Regions', fontsize=12, fontweight='bold')
        
        axes[1, 2].set_xlabel('Width (pixels)')
        axes[1, 2].set_ylabel('Height (pixels)')
        
        # 添加统计信息文本
        stats_text = []
        stats_text.append(f"Category: {category.upper()}")
        stats_text.append(f"Resolution: {H}×{W}")
        stats_text.append(f"Valid Points: {np.sum(valid_mask):,} ({np.sum(valid_mask)/(H*W)*100:.1f}%)")
        
        if len(valid_orig_lighting) > 0:
            stats_text.append(f"\nOriginal Lighting:")
            stats_text.append(f"  Range: [{np.min(valid_orig_lighting):.3f}, {np.max(valid_orig_lighting):.3f}]")
            stats_text.append(f"  Mean: {np.mean(valid_orig_lighting):.3f}")
        
        if len(valid_def_lighting) > 0:
            stats_text.append(f"\nDeformed Lighting:")
            stats_text.append(f"  Range: [{np.min(valid_def_lighting):.3f}, {np.max(valid_def_lighting):.3f}]")
            stats_text.append(f"  Mean: {np.mean(valid_def_lighting):.3f}")
        
        if np.sum(anomaly_mask) > 0:
            anomaly_lighting_changes = lighting_difference[anomaly_mask]
            if len(anomaly_lighting_changes) > 0:
                stats_text.append(f"\nAnomaly Lighting Changes:")
                stats_text.append(f"  Mean Change: {np.mean(anomaly_lighting_changes):.4f}")
                stats_text.append(f"  Max |Change|: {np.max(np.abs(anomaly_lighting_changes)):.4f}")
        
        # 在右下角添加统计信息
        stats_display = '\n'.join(stats_text)
        fig.text(0.68, 0.35, stats_display, fontsize=9, 
                verticalalignment='top',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='lightcyan', alpha=0.8))
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图像
        save_path = self.output_dir / f"{save_name}_lighting_intensity_comparison.png"
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Lighting intensity comparison visualization saved: {save_path}")
        
        # 清理内存
        plt.close(fig)
        
        return save_path
    
    def _calculate_simple_lighting(self, original_pc: np.ndarray, deformed_pc: np.ndarray) -> tuple:
        """
        使用简化方法计算光照强度
        
        Args:
            original_pc: 原始点云 [H, W, 3]
            deformed_pc: 变形后点云 [H, W, 3]
            
        Returns:
            (original_lighting, deformed_lighting): 光照强度元组
        """
        H, W, _ = original_pc.shape
        
        # 简化的光照方向（从上方照射）
        light_direction = np.array([0.0, 0.0, 1.0])
        ambient_light = 0.3
        diffuse_strength = 0.7
        
        # 计算简化的法线（使用相邻点差分）
        def compute_simple_normals(pc):
            normals = np.zeros_like(pc)
            
            # 使用中心差分计算梯度
            for i in range(1, H-1):
                for j in range(1, W-1):
                    # 计算x和y方向的梯度
                    dx = pc[i, j+1] - pc[i, j-1]
                    dy = pc[i+1, j] - pc[i-1, j]
                    
                    # 叉积得到法线
                    normal = np.cross(dx, dy)
                    
                    # 归一化
                    norm = np.linalg.norm(normal)
                    if norm > 1e-6:
                        normals[i, j] = normal / norm
                    else:
                        normals[i, j] = [0, 0, 1]  # 默认向上
            
            # 边界处理
            normals[0, :] = normals[1, :]
            normals[-1, :] = normals[-2, :]
            normals[:, 0] = normals[:, 1]
            normals[:, -1] = normals[:, -2]
            
            return normals
        
        # 计算法线
        original_normals = compute_simple_normals(original_pc)
        deformed_normals = compute_simple_normals(deformed_pc)
        
        # 计算光照强度
        def calculate_lighting(normals):
            # 计算与光源方向的点积
            dot_product = np.sum(normals * light_direction[np.newaxis, np.newaxis, :], axis=2)
            
            # 限制为正值（只考虑正面光照）
            diffuse = np.maximum(0, dot_product)
            
            # 组合环境光和漫反射光
            lighting = ambient_light + diffuse_strength * diffuse
            
            # 限制在[0, 1]范围内
            lighting = np.clip(lighting, 0, 1)
            
            return lighting
        
        original_lighting = calculate_lighting(original_normals)
        deformed_lighting = calculate_lighting(deformed_normals)
        
        return original_lighting, deformed_lighting
    
    def save_test_results(
        self,
        results: dict,
        test_name: str,
        features: np.ndarray = None,
        category: str = None
    ):
        """
        Save test results
        
        Args:
            results: Test results dictionary
            test_name: Name for the test
            features: Optional features for additional saving
            category: Category name for visualization
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        save_name = f"{test_name}_{timestamp}"
        
        # Save main results - commented out to only keep final visualization
        # self.system.save_anomaly_results(results, str(self.output_dir), save_name)
        
        # Save visualization - commented out to only keep final visualization
        # vis_path = self.output_dir / f"{save_name}_visualization.png"
        # self.system.visualize_results(results, str(vis_path))
        
        # 添加点云对比可视化
        if category and results['has_anomaly'][0] > 0:
            print(f"Generating point cloud comparison visualization...")
            self.visualize_pointcloud_comparison(results, save_name, category)
            
            # 添加 Lighting Intensity 对比可视化
            print(f"Generating lighting intensity comparison visualization...")
            lighting_path = self.visualize_lighting_intensity_comparison(results, save_name, category)
            print(f"Lighting intensity comparison saved to: {lighting_path}")
        
        print(f"Test results prepared for saving with name: {save_name}")
    
    def run_test_with_category(self, category: str):
        """
        Run test with specified category
        
        Args:
            category: MVTec 3D-AD category name
        """
        print("=" * 60)
        print(f"GEOMETRIC ANOMALY GENERATION TEST FOR CATEGORY: {category}")
        print("=" * 60)
        
        # Load random sample from category
        features_path, shading_path = self.load_random_sample_from_category(category)
        features = self.load_test_features(features_path)
        
        # Test single sample with default settings
        print("\n" + "-" * 40)
        print("TEST: Single Sample (Default Settings)")
        print("-" * 40)
        
        result = self.test_single_sample(features, shading_path, category)
        self.save_test_results(result, f"{category}_single_test", features, category)
        
        # Test with ultra-gentle custom parameters
        print("\n" + "-" * 40)
        print("TEST: Ultra-Gentle Deformation")
        print("-" * 40)
        
        ultra_gentle_params = {
            'num_patches': 1,  # 只生成一个补丁
            'patch_coverage_ratio': 0.05,  # 非常小的覆盖率
            'displacement_strength_range': [0.0002, 0.001],  # 极小的位移强度
            'center_weight': 0.3,  # 非常平缓的中心权重
            'edge_weight': 0.25   # 接近中心权重的边缘权重
        }
        
        result_gentle = self.test_single_sample(features, shading_path, category, ultra_gentle_params)
        self.save_test_results(result_gentle, f"{category}_ultra_gentle_test", features, category)
        
        # Final unified visualization - save the final result
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        final_vis_name = f"{category}_final_comparison_{timestamp}"
        
        # Save the final result visualization
        final_vis_path = self.output_dir / f"{final_vis_name}.png"
        self.system.visualize_results(result_gentle, str(final_vis_path))
        print(f"\nFinal visualization saved: {final_vis_path}")
        
        # 添加最终的点云对比可视化
        if result_gentle['has_anomaly'][0] > 0:
            print(f"\nGenerating final point cloud comparison visualization...")
            self.visualize_pointcloud_comparison(result_gentle, final_vis_name, category)
            
            # 添加最终的 Lighting Intensity 对比可视化
            print(f"Generating final lighting intensity comparison visualization...")
            final_lighting_path = self.visualize_lighting_intensity_comparison(result_gentle, final_vis_name, category)
            print(f"Final lighting intensity comparison saved: {final_lighting_path}")
        
        print("\n" + "=" * 60)
        print("TEST COMPLETE")
        print("=" * 60)
        print(f"Results saved to: {self.output_dir.absolute()}")
        
        return result


def main():
    """Main function for testing"""
    parser = argparse.ArgumentParser(description='Test Geometric Anomaly Generation - Auto Random Sample Selection')
    parser.add_argument('--category', type=str, required=True,
                       help='MVTec 3D-AD category for testing (e.g., bagel, cable_gland)')
    parser.add_argument('--preset', type=str, default='subtle_deformation',
                       help='Configuration preset to use')
    
    args = parser.parse_args()
    
    # Initialize tester
    tester = GeometricAnomalyTester(args.preset)
    
    # Run test with specified category - automatically selects random sample
    print("=" * 80)
    print(f"GEOMETRIC ANOMALY GENERATION TEST - AUTO RANDOM SAMPLE SELECTION")
    print(f"Category: {args.category.upper()}")
    print("=" * 80)
    
    result = tester.run_test_with_category(args.category)
    
    print("\nTesting complete!")
    print(f"All results saved to: {tester.output_dir.absolute()}")


if __name__ == "__main__":
    main()