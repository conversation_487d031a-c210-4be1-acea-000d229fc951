{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "markdown", "source": ["Make sure to set the runtime to GPU: Runtime -> Change runtime type -> T4 GPU\n", "\n", "You can upload your own images, then change the relevant code cells to load it and send it through the model."], "metadata": {"id": "-lKPW1wZEulh"}}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "_cPkUxN-ikld"}, "outputs": [], "source": ["# install the intrinsic decomposition repo from github\n", "!pip install https://github.com/compphoto/Intrinsic/archive/main.zip"]}, {"cell_type": "code", "source": ["import torch\n", "\n", "# import some helper functions from chrislib (will be installed by the intrinsic repo)\n", "from chrislib.general import show, view, invert\n", "from chrislib.data_util import load_from_url\n", "\n", "# import model loading and running the pipeline\n", "from intrinsic.pipeline import load_models, run_pipeline"], "metadata": {"id": "J0gn82ZSjomn"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# download the pretrained weights and return the model (may take a bit to download weights the first time)\n", "intrinsic_model = load_models('v2')"], "metadata": {"id": "Ap3HubpwC_KG"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# load the image to run through the pipeline\n", "img = load_from_url('https://raw.githubusercontent.com/compphoto/Intrinsic/refs/heads/main/figures/canal.png')"], "metadata": {"id": "ALb4Pjfvj-MU"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# run the image through the pipeline (runs all stages)\n", "result = run_pipeline(\n", "    intrinsic_model,\n", "    img,\n", "    device='cuda'\n", ")"], "metadata": {"id": "QW0TiFypkOj-"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["img = result['image']\n", "alb = view(result['hr_alb']) # gamma correct the estimated albedo\n", "dif = 1 - invert(result['dif_shd']) # tonemap the diffuse shading\n", "res = result['residual']"], "metadata": {"id": "XpYY2MNjkp2f"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["show([img, alb, dif, res], size=(30, 7))"], "metadata": {"id": "8KKbyoVLki9s"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "8BdHKJun3cyA"}, "execution_count": null, "outputs": []}]}