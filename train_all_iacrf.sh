#!/bin/bash
# Batch training script for IA-CRF Stage 2 Training
# Trains all MVTec 3D-AD categories sequentially

# MVTec 3D-AD categories
categories=("bagel" "cable_gland" "carrot" "cookie" "dowel" "foam" "peach" "potato" "rope" "tire")

echo "Starting IA-CRF Stage 2 batch training for all categories"
echo "Total categories: ${#categories[@]}"

# Training counter
success_count=0
fail_count=0
failed_categories=()

# Loop through all categories
for i in "${!categories[@]}"; do
    category="${categories[$i]}"
    echo ""
    echo "=================================================================="
    echo "Training category [$((i+1))/${#categories[@]}]: $category"
    echo "=================================================================="
    
    # Run training for this category
    python run_iacrf_training.py --category "$category" --auto-gpu
    
    # Check if training was successful
    if [ $? -eq 0 ]; then
        echo "✓ Training completed successfully for $category"
        ((success_count++))
    else
        echo "✗ Training failed for $category"
        ((fail_count++))
        failed_categories+=("$category")
    fi
done

# Print final summary
echo ""
echo "=================================================================="
echo "Batch Training Summary"
echo "=================================================================="
echo "Total categories: ${#categories[@]}"
echo "Successful: $success_count"
echo "Failed: $fail_count"

if [ ${#failed_categories[@]} -gt 0 ]; then
    echo "Failed categories: ${failed_categories[*]}"
fi

echo "=================================================================="
echo "IA-CRF Stage 2 batch training completed!"