#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试法线方向一致性修复效果 - 修复版本
"""

import numpy as np
import matplotlib
matplotlib.use("WebAgg")
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import sys
import os

# 添加featur_ext路径到sys.path
sys.path.append(os.path.join(os.path.dirname(__file__), 'feature_ext'))

try:
    from point_cloud_feature_extractor_fast import FastPointCloudFeatureExtractor
except ImportError as e:
    print(f"Import error: {e}")
    print("请确保featur_ext/point_cloud_feature_extractor_fast.py文件存在")
    sys.exit(1)

def create_test_point_cloud():
    """创建测试点云数据：半球形状"""
    # 创建半球形点云
    theta = np.linspace(0, 2*np.pi, 50)
    phi = np.linspace(0, np.pi/2, 25)  # 半球
    
    points = []
    for t in theta:
        for p in phi:
            x = np.sin(p) * np.cos(t)
            y = np.sin(p) * np.sin(t)
            z = np.cos(p)
            points.append([x, y, z])
    
    points = np.array(points)
    
    # 将点云转换为224x224有组织格式
    organized_pc = np.zeros((224, 224, 3))
    
    # 将点云放在中心区域
    start_i, start_j = 80, 80
    end_i, end_j = start_i + 64, start_j + 64
    
    # 随机放置点
    for i, point in enumerate(points[:min(len(points), (end_i-start_i)*(end_j-start_j))]):
        row = start_i + i // (end_j - start_j)
        col = start_j + i % (end_j - start_j)
        if row < end_i and col < end_j:
            organized_pc[row, col] = point
    
    return organized_pc

def analyze_normal_consistency(features):
    """分析法线方向一致性 - 修复版本"""
    # 提取特征
    points = features[:, :3]
    normals = features[:, 3:6] 
    
    # 找到有效点（非零点）
    valid_mask = np.any(points != 0, axis=1)
    valid_normals = normals[valid_mask]
    valid_points = points[valid_mask]
    
    if len(valid_normals) == 0:
        return {}
    
    # 计算法线长度分布
    normal_lengths = np.linalg.norm(valid_normals, axis=1)
    
    # 计算法线方向分布
    # 法线指向上方的比例
    upward_ratio = np.mean(valid_normals[:, 2] > 0)
    
    # 计算法线方向的一致性（与平均方向的角度差）
    mean_normal = np.mean(valid_normals, axis=0)
    mean_normal = mean_normal / (np.linalg.norm(mean_normal) + 1e-8)
    
    dot_products = np.dot(valid_normals, mean_normal)
    angles = np.arccos(np.clip(dot_products, -1, 1)) * 180 / np.pi
    
    stats = {
        'total_valid_normals': len(valid_normals),
        'normal_length_mean': np.mean(normal_lengths),
        'normal_length_std': np.std(normal_lengths),
        'upward_ratio': upward_ratio,
        'angle_mean': np.mean(angles),
        'angle_std': np.std(angles),
        'angle_max': np.max(angles),
        'consistency_score': np.mean(np.abs(dot_products))  # 与平均方向的一致性
    }
    
    return stats

def visualize_normals_3d(features, title="法线方向可视化"):
    """3D可视化法线方向 - 修复版本"""
    points = features[:, :3]
    normals = features[:, 3:6]
    
    valid_mask = np.any(points != 0, axis=1)
    valid_points = points[valid_mask]
    valid_normals = normals[valid_mask]
    
    if len(valid_points) == 0:
        print("没有有效点可视化")
        return
    
    # 随机采样用于可视化（避免太多箭头）
    max_arrows = 200
    if len(valid_points) > max_arrows:
        indices = np.random.choice(len(valid_points), max_arrows, replace=False)
        valid_points = valid_points[indices]
        valid_normals = valid_normals[indices]
    
    fig = plt.figure(figsize=(12, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    # 绘制点云
    ax.scatter(valid_points[:, 0], valid_points[:, 1], valid_points[:, 2], 
               c='lightblue', alpha=0.6, s=30)
    
    # 绘制法线方向
    scale_factor = 0.1
    ax.quiver(valid_points[:, 0], valid_points[:, 1], valid_points[:, 2],
              valid_normals[:, 0], valid_normals[:, 1], valid_normals[:, 2],
              length=scale_factor, color='red', arrow_length_ratio=0.3, linewidth=1)
    
    ax.set_xlabel('X')
    ax.set_ylabel('Y')
    ax.set_zlabel('Z')
    ax.set_title(title)
    
    # 设置相等的坐标轴比例
    max_range = np.array([valid_points.max()-valid_points.min()]).max() / 2.0
    mid_x = (valid_points[:, 0].max()+valid_points[:, 0].min()) * 0.5
    mid_y = (valid_points[:, 1].max()+valid_points[:, 1].min()) * 0.5
    mid_z = (valid_points[:, 2].max()+valid_points[:, 2].min()) * 0.5
    ax.set_xlim(mid_x - max_range, mid_x + max_range)
    ax.set_ylim(mid_y - max_range, mid_y + max_range)
    ax.set_zlim(mid_z - max_range, mid_z + max_range)
    
    plt.tight_layout()
    plt.show()

def test_normal_consistency():
    """测试法线方向一致性修复效果"""
    print("开始测试法线方向一致性修复效果...")
    
    # 创建测试点云
    print("1. 创建测试点云...")
    organized_pc = create_test_point_cloud()
    
    # 创建特征提取器
    print("2. 初始化特征提取器...")
    extractor = FastPointCloudFeatureExtractor(
        k_neighbors=30,
        batch_size=500,
        enable_resize=False  # 使用原始尺寸测试
    )
    
    # 提取特征
    print("3. 提取几何特征...")
    features = extractor.extract_features(organized_pc)
    
    # 分析结果
    print("4. 分析法线一致性...")
    stats = analyze_normal_consistency(features)
    
    print("=== 法线一致性分析结果 ===")
    print(f"有效法线数量: {stats['total_valid_normals']}")
    print(f"法线长度均值: {stats['normal_length_mean']:.6f}")
    print(f"法线长度标准差: {stats['normal_length_std']:.6f}")
    print(f"向上法线比例: {stats['upward_ratio']:.3f} ({stats['upward_ratio']*100:.1f}%)")
    print(f"角度差均值: {stats['angle_mean']:.2f}°")
    print(f"角度差标准差: {stats['angle_std']:.2f}°")
    print(f"最大角度差: {stats['angle_max']:.2f}°")
    print(f"一致性评分: {stats['consistency_score']:.3f}")
    
    # 评估修复效果
    print("\n=== 修复效果评估 ===")
    if stats['normal_length_mean'] > 0.99 and stats['normal_length_std'] < 0.01:
        print("✓ 法线归一化: 良好")
    else:
        print("✗ 法线归一化: 需要改进")
    
    if stats['upward_ratio'] > 0.8:
        print("✓ 法线方向一致性: 良好 (大部分法线指向外部)")
    elif stats['upward_ratio'] > 0.6:
        print("⚠ 法线方向一致性: 一般")
    else:
        print("✗ 法线方向一致性: 较差")
    
    if stats['angle_mean'] < 45:
        print("✓ 法线方向收敛性: 良好")
    elif stats['angle_mean'] < 90:
        print("⚠ 法线方向收敛性: 一般")
    else:
        print("✗ 法线方向收敛性: 较差")
    
    if stats['consistency_score'] > 0.7:
        print("✓ 整体一致性: 良好")
    elif stats['consistency_score'] > 0.5:
        print("⚠ 整体一致性: 一般")
    else:
        print("✗ 整体一致性: 较差")
    
    # 可视化结果
    print("\n5. 可视化法线方向...")
    visualize_normals_3d(features, "修复后的法线方向")
    
    # 保存测试结果
    output_file = "test_normal_consistency_results.npy"
    np.save(output_file, features)
    print(f"\n测试特征已保存到: {output_file}")
    
    return stats

if __name__ == "__main__":
    try:
        test_normal_consistency()
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()