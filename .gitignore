# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Logs
logs/
*.log

# Checkpoints
checkpoints/

# IDE
.vscode/

# OS
.DS_Store
Thumbs.db

# Visualizations and test results
visualizations/
test_results/
test_results_fixed/
anomaly_visualization/
material_anomaly_test_results/
geometric_anomaly_test_results/
incremental_test_results/
vis_output/
