#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3D VAE Training Script for Internal Geometric Prior Learning
Trains a Variational Autoencoder on normal samples from a specific category
to learn internal geometric priors for industrial anomaly detection
"""

import os
import sys
import yaml
import time
import argparse
import numpy as np
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from tqdm import tqdm

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader

# Temporarily disable anomaly detection for training
# torch.autograd.set_detect_anomaly(True)

# TensorBoard import with fallback
try:
    from torch.utils.tensorboard import SummaryWriter
    TENSORBOARD_AVAILABLE = True
except ImportError as e:
    print(f"Warning: TensorBoard not available: {e}")
    print("Training will continue without TensorBoard logging.")
    TENSORBOARD_AVAILABLE = False
    
    # Dummy SummaryWriter class for fallback
    class SummaryWriter:
        def __init__(self, *args, **kwargs):
            pass
        def add_scalar(self, *args, **kwargs):
            pass
        def close(self):
            pass

# Add project path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from model.pointcloud_vae import PointCloudVAE
from model.vae_losses import VAELoss, AdaptiveBetaScheduler
from model.pointcloud_vae_dataset import create_vae_dataloader


class VAETrainer:
    """
    Trainer class for 3D Point Cloud VAE
    """
    def __init__(self, config: Dict):
        self.config = config
        self.device = self._setup_device()
        
        # Create directories
        self._create_directories()
        
        # Initialize model, optimizer, and loss
        self.model = self._create_model()
        self.optimizer = self._create_optimizer()
        self.loss_fn = self._create_loss_function()
        self.beta_scheduler = self._create_beta_scheduler() #用于控制KL散度项的权重
        self.lr_scheduler = self._create_lr_scheduler() #动态调整学习率
        
        # Training state
        self.current_epoch = 0
        self.best_loss = float('inf')
        self.train_losses = []
        self.val_losses = []
        
        # Logging
        self.writer = self._create_tensorboard_writer()
        
        print(f"VAE Trainer initialized for category: {config['dataset']['category']}")
        print(f"Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
        print(f"Training on device: {self.device}")
    
    def _setup_device(self) -> torch.device:
        """Setup training device"""
        device_config = self.config['training']['device']
        
        if device_config == 'auto':
            if torch.cuda.is_available():
                device = torch.device('cuda')
                print(f"Using GPU: {torch.cuda.get_device_name()}")
            else:
                device = torch.device('cpu')
                print("Using CPU")
        else:
            device = torch.device(device_config)
        
        return device
    
    def _create_directories(self):
        """Create necessary directories"""
        category = self.config['dataset']['category']
        
        # Checkpoint directory
        self.checkpoint_dir = Path(self.config['checkpoint']['save_dir'].format(category=category))
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        
        # Sample directory
        self.sample_dir = Path(self.config['sampling']['sample_dir'].format(category=category))
        self.sample_dir.mkdir(parents=True, exist_ok=True)
        
        # Log directory
        self.log_dir = Path(self.config['logging']['tensorboard_dir'].format(category=category))
        self.log_dir.mkdir(parents=True, exist_ok=True)
    
    def _create_model(self) -> PointCloudVAE:
        """Create VAE model"""
        model_config = self.config['model']
        
        model = PointCloudVAE(
            input_dim=model_config['input_dim'],
            latent_dim=model_config['latent_dim'],
            feature_dim=model_config['feature_dim'],
            output_points=model_config['output_points']
        ).to(self.device)
        
        # Initialize weights
        self._initialize_weights(model)
        
        return model
    
    def _initialize_weights(self, model: nn.Module):
        """Initialize model weights"""
        init_method = self.config['advanced']['initialization']['weight_init']
        
        for module in model.modules():
            if isinstance(module, (nn.Linear, nn.Conv1d)):
                if init_method == 'xavier_uniform':
                    nn.init.xavier_uniform_(module.weight)
                elif init_method == 'xavier_normal':
                    nn.init.xavier_normal_(module.weight)
                elif init_method == 'kaiming_uniform':
                    nn.init.kaiming_uniform_(module.weight)
                elif init_method == 'kaiming_normal':
                    nn.init.kaiming_normal_(module.weight)
                
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
    
    def _create_optimizer(self) -> optim.Optimizer:
        """Create optimizer"""
        training_config = self.config['training']
        
        # Ensure parameters are the correct type
        learning_rate = float(training_config['learning_rate'])
        weight_decay = float(training_config['weight_decay'])
        
        optimizer = optim.Adam(
            self.model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay
        )
        
        return optimizer
    
    def _create_loss_function(self) -> VAELoss:
        """Create loss function"""
        loss_config = self.config['loss']
        
        loss_fn = VAELoss(
            reconstruction_loss=loss_config['reconstruction_loss'],
            beta=loss_config['beta'],
            use_efficient_chamfer=loss_config['use_efficient_chamfer']
        )
        
        return loss_fn
    
    def _create_beta_scheduler(self) -> Optional[AdaptiveBetaScheduler]:
        """Create beta scheduler for KL loss"""
        beta_config = self.config['loss']['beta_scheduler']
        
        if not beta_config['use_scheduler']:
            return None
        
        scheduler = AdaptiveBetaScheduler(
            initial_beta=beta_config['initial_beta'],
            final_beta=beta_config['final_beta'],
            warmup_epochs=beta_config['warmup_epochs'],
            mode=beta_config['mode']
        )
        
        return scheduler
    
    def _create_lr_scheduler(self) -> Optional[optim.lr_scheduler._LRScheduler]:
        """Create learning rate scheduler"""
        lr_config = self.config['advanced']['lr_scheduler']
        
        if lr_config['type'] == 'none':
            return None
        elif lr_config['type'] == 'cosine':
            return optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=lr_config['cosine']['T_max'],
                eta_min=lr_config['cosine']['eta_min']
            )
        elif lr_config['type'] == 'step':
            return optim.lr_scheduler.StepLR(
                self.optimizer,
                step_size=lr_config['step']['step_size'],
                gamma=lr_config['step']['gamma']
            )
        elif lr_config['type'] == 'plateau':
            return optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer,
                patience=lr_config['plateau']['patience'],
                factor=lr_config['plateau']['factor'],
                threshold=lr_config['plateau']['threshold']
            )
    
    def _create_tensorboard_writer(self) -> Optional[SummaryWriter]:
        """Create TensorBoard writer"""
        if not self.config['logging']['use_tensorboard'] or not TENSORBOARD_AVAILABLE:
            if self.config['logging']['use_tensorboard'] and not TENSORBOARD_AVAILABLE:
                print("Warning: TensorBoard logging requested but not available. Continuing without logging.")
            return None
        
        return SummaryWriter(self.log_dir)
    
    def _create_dataloaders(self) -> Tuple[DataLoader, Optional[DataLoader]]:
        """Create train and validation dataloaders"""
        dataset_config = self.config['dataset']
        augmentation_config = self.config['augmentation']
        training_config = self.config['training']
        
        # Training dataloader
        train_dataloader = create_vae_dataloader(
            dataset_path=dataset_config['dataset_path'],
            category=dataset_config['category'],
            batch_size=training_config['batch_size'],
            num_workers=training_config['num_workers'],
            num_points=dataset_config['num_points'],
            augmentation_config=augmentation_config,
            phases=dataset_config['phases']
        )
        
        # Validation dataloader (if enabled)
        val_dataloader = None
        if self.config['validation']['enable_validation']: #如果启用的话，需要使用数据集中的validation数据，需要修改
            # Use same data without augmentation for validation
            val_dataloader = create_vae_dataloader(
                dataset_path=dataset_config['dataset_path'],
                category=dataset_config['category'],
                batch_size=training_config['batch_size'],
                num_workers=training_config['num_workers'],
                num_points=dataset_config['num_points'],
                augmentation_config=None,  # No augmentation for validation
                phases=dataset_config['phases']
            )
        
        return train_dataloader, val_dataloader
    
    def train_epoch(self, dataloader: DataLoader, epoch: int) -> Dict[str, float]:
        """Train for one epoch"""
        self.model.train()
        
        total_loss = 0.0
        total_recon_loss = 0.0
        total_kl_loss = 0.0
        num_batches = 0
        
        # Update beta if scheduler is used
        if self.beta_scheduler is not None:
            current_beta = self.beta_scheduler.get_beta(epoch)
            self.loss_fn.beta = current_beta
        
        # Create progress bar
        pbar = tqdm(dataloader, desc=f"Epoch {epoch}", leave=False, 
                   bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}, {rate_fmt}]')
        
        for batch_idx, batch in enumerate(pbar):
            points = batch['points'].to(self.device)  # [B, N, 3]
            
            # Forward pass
            reconstructed, mu, logvar = self.model(points)
            
            # Compute loss
            total_loss_batch, recon_loss_batch, kl_loss_batch = self.loss_fn(
                reconstructed, points, mu, logvar
            )
            
            # Backward pass
            self.optimizer.zero_grad()
            total_loss_batch.backward()
            
            # Gradient clipping
            if self.config['training']['grad_clip'] > 0:
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(), 
                    self.config['training']['grad_clip']
                )
            
            self.optimizer.step()
            
            # Accumulate losses
            total_loss += total_loss_batch.item()
            total_recon_loss += recon_loss_batch.item()
            total_kl_loss += kl_loss_batch.item()
            num_batches += 1
            
            # Update progress bar with current losses
            pbar.set_postfix({
                'Loss': f'{total_loss_batch.item():.4f}',
                'Recon': f'{recon_loss_batch.item():.4f}',
                'KL': f'{kl_loss_batch.item():.1f}'
            })
        
        # Average losses
        avg_losses = {
            'total_loss': total_loss / num_batches,
            'reconstruction_loss': total_recon_loss / num_batches,
            'kl_loss': total_kl_loss / num_batches
        }
        
        return avg_losses
    
    def validate(self, dataloader: DataLoader) -> Dict[str, float]:
        """Validate the model"""
        self.model.eval()
        
        total_loss = 0.0
        total_recon_loss = 0.0
        total_kl_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for batch in dataloader:
                points = batch['points'].to(self.device)
                
                # Forward pass
                reconstructed, mu, logvar = self.model(points)
                
                # Compute loss
                total_loss_batch, recon_loss_batch, kl_loss_batch = self.loss_fn(
                    reconstructed, points, mu, logvar
                )
                
                total_loss += total_loss_batch.item()
                total_recon_loss += recon_loss_batch.item()
                total_kl_loss += kl_loss_batch.item()
                num_batches += 1
        
        avg_losses = {
            'total_loss': total_loss / num_batches,
            'reconstruction_loss': total_recon_loss / num_batches,
            'kl_loss': total_kl_loss / num_batches
        }
        
        return avg_losses
    
    def save_checkpoint(self, epoch: int, is_best: bool = False):
        """Save model checkpoint"""
        # Only save the best model checkpoint
        if is_best:
            checkpoint = {
                'epoch': epoch,
                'model_state_dict': self.model.state_dict(),
                'optimizer_state_dict': self.optimizer.state_dict(),
                'best_loss': self.best_loss,
                'config': self.config
            }
            
            if self.lr_scheduler is not None:
                checkpoint['lr_scheduler_state_dict'] = self.lr_scheduler.state_dict()
            
            # Save only the best checkpoint
            torch.save(checkpoint, self.checkpoint_dir / 'best.pth')
    
    def train(self):
        """Main training loop"""
        print("Starting VAE training...")
        
        # Create dataloaders
        train_dataloader, val_dataloader = self._create_dataloaders()
        print(f"Training samples: {len(train_dataloader.dataset)}")
        print(f"Training batches: {len(train_dataloader)}")
        print("-" * 80)
        
        start_time = time.time()
        
        # Create progress bar for epochs
        epoch_pbar = tqdm(range(self.config['training']['epochs']), 
                         desc="Training Progress", 
                         bar_format='{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}]')
        
        for epoch in epoch_pbar:
            self.current_epoch = epoch
            epoch_start_time = time.time()
            
            # Training
            train_losses = self.train_epoch(train_dataloader, epoch)
            self.train_losses.append(train_losses)
            
            # Validation
            val_losses = None
            if (val_dataloader is not None and 
                epoch % self.config['validation']['frequency'] == 0):
                val_losses = self.validate(val_dataloader)
                self.val_losses.append(val_losses)
            
            # Learning rate scheduling
            if self.lr_scheduler is not None:
                if isinstance(self.lr_scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                    self.lr_scheduler.step(train_losses['total_loss'])
                else:
                    self.lr_scheduler.step()
            
            # Update epoch progress bar
            epoch_time = time.time() - epoch_start_time
            postfix_dict = {
                'Loss': f"{train_losses['total_loss']:.4f}",
                'Recon': f"{train_losses['reconstruction_loss']:.4f}", 
                'KL': f"{train_losses['kl_loss']:.1f}",
                'Time': f"{epoch_time:.1f}s"
            }
            
            if val_losses is not None:
                postfix_dict['Val_Loss'] = f"{val_losses['total_loss']:.4f}"
            
            epoch_pbar.set_postfix(postfix_dict)
            
            # TensorBoard logging
            if self.writer is not None and TENSORBOARD_AVAILABLE:
                try:
                    self.writer.add_scalar('Train/Total_Loss', train_losses['total_loss'], epoch)
                    self.writer.add_scalar('Train/Reconstruction_Loss', train_losses['reconstruction_loss'], epoch)
                    self.writer.add_scalar('Train/KL_Loss', train_losses['kl_loss'], epoch)
                    self.writer.add_scalar('Train/Learning_Rate', self.optimizer.param_groups[0]['lr'], epoch)
                    
                    if self.beta_scheduler is not None:
                        self.writer.add_scalar('Train/Beta', self.loss_fn.beta, epoch)
                    
                    if val_losses is not None:
                        self.writer.add_scalar('Val/Total_Loss', val_losses['total_loss'], epoch)
                        self.writer.add_scalar('Val/Reconstruction_Loss', val_losses['reconstruction_loss'], epoch)
                        self.writer.add_scalar('Val/KL_Loss', val_losses['kl_loss'], epoch)
                except Exception as e:
                    tqdm.write(f"Warning: TensorBoard logging failed: {e}")
            
            # Save checkpoint
            current_loss = val_losses['total_loss'] if val_losses else train_losses['total_loss']
            is_best = current_loss < self.best_loss
            if is_best:
                self.best_loss = current_loss
                tqdm.write(f"New best loss: {self.best_loss:.6f} at epoch {epoch}")
            
            self.save_checkpoint(epoch, is_best)
        
        total_time = time.time() - start_time
        print(f"\nTraining completed in {total_time:.2f}s ({total_time/3600:.2f}h)")
        print(f"Best loss: {self.best_loss:.6f}")
        
        if self.writer is not None and TENSORBOARD_AVAILABLE:
            try:
                self.writer.close()
            except Exception as e:
                print(f"Warning: Failed to close TensorBoard writer: {e}")


def load_config(config_path: str) -> Dict:
    """Load configuration from YAML file"""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Validate and convert numeric types
    config = validate_and_convert_config(config)
    return config


def validate_and_convert_config(config: Dict) -> Dict:
    """Validate configuration and convert types as needed"""
    # Convert training parameters
    if 'training' in config:
        training = config['training']
        if 'learning_rate' in training:
            training['learning_rate'] = float(training['learning_rate'])
        if 'weight_decay' in training:
            training['weight_decay'] = float(training['weight_decay'])
        if 'grad_clip' in training:
            training['grad_clip'] = float(training['grad_clip'])
        if 'epochs' in training:
            training['epochs'] = int(training['epochs'])
        if 'batch_size' in training:
            training['batch_size'] = int(training['batch_size'])
        if 'num_workers' in training:
            training['num_workers'] = int(training['num_workers'])
    
    # Convert loss parameters
    if 'loss' in config:
        loss = config['loss']
        if 'beta' in loss:
            loss['beta'] = float(loss['beta'])
        
        # Convert beta scheduler parameters
        if 'beta_scheduler' in loss:
            beta_sched = loss['beta_scheduler']
            if 'initial_beta' in beta_sched:
                beta_sched['initial_beta'] = float(beta_sched['initial_beta'])
            if 'final_beta' in beta_sched:
                beta_sched['final_beta'] = float(beta_sched['final_beta'])
            if 'warmup_epochs' in beta_sched:
                beta_sched['warmup_epochs'] = int(beta_sched['warmup_epochs'])
    
    # Convert model parameters
    if 'model' in config:
        model = config['model']
        if 'input_dim' in model:
            model['input_dim'] = int(model['input_dim'])
        if 'latent_dim' in model:
            model['latent_dim'] = int(model['latent_dim'])
        if 'feature_dim' in model:
            model['feature_dim'] = int(model['feature_dim'])
        if 'output_points' in model:
            model['output_points'] = int(model['output_points'])
    
    # Convert dataset parameters
    if 'dataset' in config:
        dataset = config['dataset']
        if 'num_points' in dataset:
            dataset['num_points'] = int(dataset['num_points'])
    
    # Convert advanced parameters (learning rate scheduler)
    if 'advanced' in config and 'lr_scheduler' in config['advanced']:
        lr_scheduler = config['advanced']['lr_scheduler']
        
        # Convert cosine scheduler parameters
        if 'cosine' in lr_scheduler:
            cosine = lr_scheduler['cosine']
            if 'T_max' in cosine:
                cosine['T_max'] = int(cosine['T_max'])
            if 'eta_min' in cosine:
                cosine['eta_min'] = float(cosine['eta_min'])
        
        # Convert step scheduler parameters
        if 'step' in lr_scheduler:
            step = lr_scheduler['step']
            if 'step_size' in step:
                step['step_size'] = int(step['step_size'])
            if 'gamma' in step:
                step['gamma'] = float(step['gamma'])
        
        # Convert plateau scheduler parameters
        if 'plateau' in lr_scheduler:
            plateau = lr_scheduler['plateau']
            if 'patience' in plateau:
                plateau['patience'] = int(plateau['patience'])
            if 'factor' in plateau:
                plateau['factor'] = float(plateau['factor'])
            if 'threshold' in plateau:
                plateau['threshold'] = float(plateau['threshold'])
    
    return config


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='3D VAE Training for Internal Geometric Prior Learning')
    
    parser.add_argument('--config', type=str, default='configs/vae_training_config.yaml',
                        help='Path to training configuration file')
    parser.add_argument('--category', type=str, default=None,
                        help='Object category to train on (overrides config)')
    parser.add_argument('--dataset_path', type=str, default=None,
                        help='Path to MVTec 3D-AD dataset (overrides config)')
    parser.add_argument('--epochs', type=int, default=None,
                        help='Number of training epochs (overrides config)')
    parser.add_argument('--batch_size', type=int, default=None,
                        help='Batch size (overrides config)')
    parser.add_argument('--device', type=str, default=None,
                        help='Training device (overrides config)')
    
    args = parser.parse_args()
    
    # Load configuration
    config = load_config(args.config)
    
    # Override config with command line arguments
    if args.category is not None:
        config['dataset']['category'] = args.category
    if args.dataset_path is not None:
        config['dataset']['dataset_path'] = args.dataset_path
    if args.epochs is not None:
        config['training']['epochs'] = args.epochs
    if args.batch_size is not None:
        config['training']['batch_size'] = args.batch_size
    if args.device is not None:
        config['training']['device'] = args.device
    
    print("=" * 80)
    print("3D VAE Training for Internal Geometric Prior Learning")
    print("=" * 80)
    print(f"Category: {config['dataset']['category']}")
    print(f"Dataset: {config['dataset']['dataset_path']}")
    print(f"Epochs: {config['training']['epochs']}")
    print(f"Batch size: {config['training']['batch_size']}")
    print("=" * 80)
    
    # Create trainer and start training
    trainer = VAETrainer(config)
    trainer.train()


if __name__ == "__main__":
    main()