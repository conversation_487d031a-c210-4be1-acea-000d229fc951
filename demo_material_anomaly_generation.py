"""
材质异常生成示例脚本
演示如何使用基于EasyNet方法的Albedo纹理材质异常生成系统

Author: IA-CRF Project
Date: 2025-08-26

使用方法:
1. 下载DTD数据集到本地
2. 修改配置中的路径
3. 运行脚本生成材质异常

DTD数据集下载:
wget https://www.robots.ox.ac.uk/~vgg/data/dtd/download/dtd-r1.0.1.tar.gz
tar -xf dtd-r1.0.1.tar.gz
"""

import os
import sys
import numpy as np
import cv2
from pathlib import Path
import argparse
from tqdm import tqdm

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from pseudo_anomaly_generation.material import MaterialAnomalyGenerationSystem
from pseudo_anomaly_generation.configs import get_config


def create_sample_albedo_and_depth():
    """
    创建示例Albedo和深度图像用于测试
    """
    # 创建示例Albedo图像（木纹纹理）
    height, width = 256, 256
    albedo = np.zeros((height, width, 3), dtype=np.float32)
    
    # 生成木纹效果
    for i in range(height):
        for j in range(width):
            # 基础棕色
            base_color = [0.6, 0.4, 0.2]
            # 添加纹理变化
            variation = 0.1 * np.sin(i * 0.1) * np.cos(j * 0.05)
            albedo[i, j] = [c + variation for c in base_color]
    
    # 归一化到[0, 1]
    albedo = np.clip(albedo, 0, 1)
    
    # 创建示例深度图（圆柱形物体）
    depth = np.zeros((height, width, 3), dtype=np.float32)
    center_x, center_y = width // 2, height // 2
    radius = min(width, height) // 3
    
    for i in range(height):
        for j in range(width):
            dist = np.sqrt((i - center_y)**2 + (j - center_x)**2)
            if dist <= radius:
                # 圆柱形深度分布
                depth_value = 0.5 + 0.3 * (1 - dist / radius)
                depth[i, j] = [depth_value] * 3
            else:
                depth[i, j] = [0.1] * 3  # 背景深度
    
    return albedo, depth


def demo_single_generation():
    """
    演示单个异常生成
    """
    print("=== 单个异常生成演示 ===")
    
    # 创建测试数据
    albedo, depth = create_sample_albedo_and_depth()
    
    # 创建测试纹理目录
    texture_dir = Path("demo_textures")
    texture_dir.mkdir(exist_ok=True)
    
    # 创建几个测试纹理图像
    textures = [
        np.random.randint(0, 256, (256, 256, 3), dtype=np.uint8),  # 随机纹理
        np.ones((256, 256, 3), dtype=np.uint8) * [200, 100, 50],  # 单色纹理
        np.tile(np.arange(256, dtype=np.uint8).reshape(1, -1, 1), (256, 1, 3))  # 渐变纹理
    ]
    
    for i, texture in enumerate(textures):
        cv2.imwrite(str(texture_dir / f"texture_{i}.png"), texture)
    
    # 初始化材质异常生成系统
    system = MaterialAnomalyGenerationSystem(preset="easynet_default")
    system.set_texture_source(str(texture_dir))
    
    # 生成异常
    result = system.generate_single_anomaly(
        albedo_image=albedo,
        depth_map=depth,
        category="demo"
    )
    
    # 保存结果
    output_dir = Path("demo_output")
    output_dir.mkdir(exist_ok=True)
    
    # 保存原始Albedo
    cv2.imwrite(
        str(output_dir / "original_albedo.png"),
        cv2.cvtColor((albedo * 255).astype(np.uint8), cv2.COLOR_RGB2BGR)
    )
    
    # 保存增强后的Albedo
    augmented_albedo = (result['augmented_albedo'] * 255).astype(np.uint8)
    cv2.imwrite(
        str(output_dir / "augmented_albedo.png"),
        cv2.cvtColor(augmented_albedo, cv2.COLOR_RGB2BGR)
    )
    
    # 保存异常掩码
    mask = (result['anomaly_mask'] * 255).astype(np.uint8)
    cv2.imwrite(str(output_dir / "anomaly_mask.png"), mask)
    
    # 打印结果信息
    print(f"生成异常: {'是' if result['has_anomaly'][0] > 0 else '否'}")
    print(f"异常覆盖率: {np.sum(result['anomaly_mask']) / result['anomaly_mask'].size * 100:.2f}%")
    print(f"使用纹理: {result['texture_source']}")
    print(f"Beta值: {result.get('beta', 'N/A')}")
    print(f"结果保存到: {output_dir}")


def demo_batch_generation():
    """
    演示批量异常生成
    """
    print("\n=== 批量异常生成演示 ===")
    
    batch_size = 4
    
    # 创建批量测试数据
    albedo_batch = np.zeros((batch_size, 256, 256, 3), dtype=np.float32)
    depth_batch = np.zeros((batch_size, 256, 256, 3), dtype=np.float32)
    
    for i in range(batch_size):
        albedo, depth = create_sample_albedo_and_depth()
        # 添加一些随机变化
        albedo += np.random.normal(0, 0.05, albedo.shape)
        albedo = np.clip(albedo, 0, 1)
        
        albedo_batch[i] = albedo
        depth_batch[i] = depth
    
    # 使用现有的材质异常生成系统
    texture_dir = Path("demo_textures")
    system = MaterialAnomalyGenerationSystem(preset="easynet_default")
    system.set_texture_source(str(texture_dir))
    
    # 批量生成异常
    batch_result = system.generate_batch_anomalies(
        albedo_batch=albedo_batch,
        depth_batch=depth_batch,
        categories=["demo"] * batch_size
    )
    
    # 保存批量结果
    output_dir = Path("demo_batch_output")
    output_dir.mkdir(exist_ok=True)
    
    for i in range(batch_size):
        # 保存每个样本的结果
        sample_output_dir = output_dir / f"sample_{i}"
        sample_output_dir.mkdir(exist_ok=True)
        
        # 原始Albedo
        cv2.imwrite(
            str(sample_output_dir / "original_albedo.png"),
            cv2.cvtColor((albedo_batch[i] * 255).astype(np.uint8), cv2.COLOR_RGB2BGR)
        )
        
        # 增强后的Albedo
        augmented = (batch_result['augmented_albedo'][i] * 255).astype(np.uint8)
        cv2.imwrite(
            str(sample_output_dir / "augmented_albedo.png"),
            cv2.cvtColor(augmented, cv2.COLOR_RGB2BGR)
        )
        
        # 异常掩码
        mask = (batch_result['anomaly_mask'][i] * 255).astype(np.uint8)
        cv2.imwrite(str(sample_output_dir / "anomaly_mask.png"), mask)
        
        print(f"样本 {i}: 异常={batch_result['has_anomaly'][i][0]:.0f}, "
              f"覆盖率={np.sum(batch_result['anomaly_mask'][i]) / batch_result['anomaly_mask'][i].size * 100:.2f}%")
    
    print(f"批量结果保存到: {output_dir}")


def demo_with_dtd_dataset(dtd_path: str):
    """
    演示使用DTD数据集的异常生成
    
    Args:
        dtd_path: DTD数据集路径
    """
    print(f"\n=== DTD数据集异常生成演示 ===")
    print(f"DTD数据集路径: {dtd_path}")
    
    if not Path(dtd_path).exists():
        print(f"DTD数据集路径不存在: {dtd_path}")
        print("请下载DTD数据集并设置正确的路径")
        return
    
    # 创建DTD材质异常生成系统
    system = MaterialAnomalyGenerationSystem(
        preset="easynet_default",
        dtd_dataset_path=dtd_path
    )
    
    # 创建测试数据
    albedo, depth = create_sample_albedo_and_depth()
    
    # 生成不同类别的纹理异常
    categories_to_test = ["cracked", "stained", "bumpy", "porous"]
    
    output_dir = Path("dtd_demo_output")
    output_dir.mkdir(exist_ok=True)
    
    for category in categories_to_test:
        try:
            # 生成特定类别的异常
            result = system.generator.generate_category_specific_anomaly(
                albedo_image=albedo,
                depth_map=depth,
                texture_category=category
            )
            
            # 保存结果
            category_output = output_dir / category
            category_output.mkdir(exist_ok=True)
            
            # 保存增强后的Albedo
            augmented = (result['augmented_albedo'] * 255).astype(np.uint8)
            cv2.imwrite(
                str(category_output / "augmented_albedo.png"),
                cv2.cvtColor(augmented, cv2.COLOR_RGB2BGR)
            )
            
            # 保存异常掩码
            mask = (result['anomaly_mask'] * 255).astype(np.uint8)
            cv2.imwrite(str(category_output / "anomaly_mask.png"), mask)
            
            print(f"类别 {category}: 异常={result['has_anomaly'][0]:.0f}, "
                  f"覆盖率={np.sum(result['anomaly_mask']) / result['anomaly_mask'].size * 100:.2f}%")
        
        except Exception as e:
            print(f"类别 {category} 处理失败: {e}")
    
    print(f"DTD结果保存到: {output_dir}")


def demo_config_customization():
    """
    演示配置自定义
    """
    print("\n=== 配置自定义演示 ===")
    
    # 获取默认配置
    config = get_config("easynet_default")
    
    # 自定义配置
    config["base"]["anomaly_probability"] = 1.0  # 100%生成异常
    config["beta_mixing"]["beta_range"] = [0.3, 0.7]  # 调整Beta范围
    config["perlin"]["perlin_scale"] = 4  # 调整Perlin尺度
    
    # 使用自定义配置创建系统
    texture_dir = Path("demo_textures")
    system = MaterialAnomalyGenerationSystem(config=config)
    system.set_texture_source(str(texture_dir))
    
    # 生成异常
    albedo, depth = create_sample_albedo_and_depth()
    result = system.generate_single_anomaly(albedo, depth)
    
    print(f"自定义配置生成异常: {'是' if result['has_anomaly'][0] > 0 else '否'}")
    print(f"Beta值: {result.get('beta', 'N/A')}")
    print("配置自定义演示完成")


def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description="材质异常生成演示脚本")
    parser.add_argument("--dtd-path", type=str, help="DTD数据集路径")
    parser.add_argument("--demo", type=str, choices=["single", "batch", "dtd", "config", "all"], 
                       default="all", help="演示类型")
    
    args = parser.parse_args()
    
    print("IA-CRF 材质异常生成演示")
    print("基于EasyNet方法的Albedo纹理材质异常生成")
    print("=" * 50)
    
    try:
        if args.demo in ["single", "all"]:
            demo_single_generation()
        
        if args.demo in ["batch", "all"]:
            demo_batch_generation()
        
        if args.demo in ["config", "all"]:
            demo_config_customization()
        
        if args.demo in ["dtd", "all"] and args.dtd_path:
            demo_with_dtd_dataset(args.dtd_path)
        elif args.demo == "dtd" and not args.dtd_path:
            print("\nDTD演示需要提供 --dtd-path 参数")
    
    except Exception as e:
        print(f"演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n演示完成!")


if __name__ == "__main__":
    main()