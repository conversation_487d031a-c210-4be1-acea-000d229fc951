#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MVTec 3D-AD Dataset 3D Geometric Feature Batch Extraction Script (Optimized)
Specialized for extracting and saving 3D point cloud geometric features
Optimized for high-performance batch processing with parallel execution
"""

import os
import sys
import glob
from tqdm import tqdm
import numpy as np
import argparse
import yaml
import time
from concurrent.futures import ProcessPoolExecutor, as_completed
from multiprocessing import cpu_count

# Add project path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from model.geometric_feature_extractor import GeometricFeatureExtractor
# 导入新的快速特征提取器
try:
    from feature_ext.point_cloud_feature_extractor_fast import FastPointCloudFeatureExtractor
    FAST_EXTRACTOR_AVAILABLE = True
except ImportError:
    FAST_EXTRACTOR_AVAILABLE = False
    print("Warning: Fast feature extractor not available, using original extractor")

def mvtec3d_classes():
    """MVTec 3D-AD dataset category list"""
    return ["bagel", "cable_gland", "carrot", "cookie", "dowel", 
            "foam", "peach", "potato", "rope", "tire"]


def process_single_file(args):
    """
    Process single file - optimized for parallel execution
    支持使用快速特征提取器或原始特征提取器
    """
    tiff_path, config, dataset_root, category, phase, anomaly_type = args

    try:
        sample_name = os.path.splitext(os.path.basename(tiff_path))[0]

        # Check if already processed
        features_dir = os.path.join(dataset_root, category, phase, anomaly_type, 'features')
        feature_save_path = os.path.join(features_dir, f"{sample_name}.npy")

        if os.path.exists(feature_save_path) and not config.get('force_reprocess', False):
            return True, sample_name, "Already processed"

        # 选择使用快速特征提取器还是原始特征提取器
        use_fast_extractor = config.get('use_fast_extractor', False) and FAST_EXTRACTOR_AVAILABLE

        if use_fast_extractor:
            # 使用新的快速特征提取器
            extractor = FastPointCloudFeatureExtractor(
                k_neighbors=config.get('k_neighbors', 50),
                batch_size=config.get('extractor_batch_size', 1000),
                target_size=(224, 224),
                enable_resize=True
            )

            # 直接处理单个文件
            success = extractor.process_single_file(tiff_path, features_dir)

            if success:
                # 加载保存的特征以计算统计信息
                features = np.load(feature_save_path)
                positions = features[:, :3]
                valid_mask = (np.abs(positions).sum(axis=1) > 1e-6)
                valid_points = valid_mask.sum()
                total_points = len(positions)
                valid_ratio = valid_points / total_points * 100

                return True, sample_name, f"{valid_points}/{total_points} valid points ({valid_ratio:.1f}%) [FAST]"
            else:
                return False, sample_name, "Fast extractor failed"
        else:
            # 使用原始特征提取器
            extractor = GeometricFeatureExtractor(config)

            # Load point cloud data
            organized_pc = extractor.load_and_preprocess_pointcloud(tiff_path)

            # Extract 3D geometric features
            results = extractor(organized_pointcloud=organized_pc)

            # Calculate valid point statistics
            positions = results['enhanced_pointcloud'][:, :3]
            valid_mask = (np.abs(positions).sum(axis=1) > 1e-6)
            valid_points = valid_mask.sum()
            total_points = len(positions)
            valid_ratio = valid_points / total_points * 100

            # Create features directory
            os.makedirs(features_dir, exist_ok=True)

            # Save geometric features
            extractor.save_geometric_features(
                results['enhanced_pointcloud'],
                feature_save_path
            )

            return True, sample_name, f"{valid_points}/{total_points} valid points ({valid_ratio:.1f}%) [ORIGINAL]"

    except Exception as e:
        return False, sample_name, f"Error - {e}"


class MVTec3DGeometricProcessor:
    """
    MVTec 3D-AD Dataset 3D Geometric Feature Batch Processor (Optimized)
    Functions:
    1. Batch 3D geometric feature extraction (multi-scale normals + curvature)
    2. Feature saving and management
    3. Parallel processing support
    """
    
    def __init__(self, config):
        self.config = config
        
        # Performance settings
        self.use_multiprocessing = config.get('use_multiprocessing', True) 
        self.max_workers = config.get('max_workers', min(cpu_count(), 8))
        self.batch_size = config.get('batch_size', 50)
        
        # Only show detailed info in verbose mode
        if config.get('verbose', False):
            print(f"Processor initialized:")
            print(f"  Multiprocessing: {self.use_multiprocessing}")
            print(f"  Max workers: {self.max_workers}")
            print(f"  Batch size: {self.batch_size}")
        
    def process_category(self, dataset_path, category, phases=None):
        """
        Process all data for a single category with optional parallel processing
        """
        if phases is None:
            phases = ['train', 'test', 'validation']
        
        print(f"Processing {category}...")
        
        category_path = os.path.join(dataset_path, category)
        total_files_processed = 0
        total_files_found = 0
        
        for phase in phases:
            phase_path = os.path.join(category_path, phase)
            if not os.path.exists(phase_path):
                continue
            
            for anomaly_type in os.listdir(phase_path):
                anomaly_path = os.path.join(phase_path, anomaly_type)
                if not os.path.isdir(anomaly_path):
                    continue
                
                xyz_dir = os.path.join(anomaly_path, 'xyz')
                if not os.path.exists(xyz_dir):
                    continue
                
                # Get all .tiff files
                tiff_files = glob.glob(os.path.join(xyz_dir, '*.tiff'))
                
                if not tiff_files:
                    continue
                
                # Only show detailed info in verbose mode
                if self.config.get('verbose', False):
                    print(f"  {phase}/{anomaly_type}: {len(tiff_files)} files")
                total_files_found += len(tiff_files)
                
                if self.use_multiprocessing and len(tiff_files) > 5:
                    # Use parallel processing for larger datasets
                    success_count = self._process_files_parallel(
                        tiff_files, dataset_path, category, phase, anomaly_type
                    )
                else:
                    # Use sequential processing for small datasets
                    success_count = self._process_files_sequential(
                        tiff_files, dataset_path, category, phase, anomaly_type
                    )
                
                total_files_processed += success_count
                
                # Only show detailed completion info in verbose mode
                if self.config.get('verbose', False):
                    print(f"  {phase}/{anomaly_type}: {success_count}/{len(tiff_files)} completed")
        
        # Show simple summary
        success_rate = f"({total_files_processed}/{total_files_found})" if total_files_found > 0 else "(0/0)"
        print(f"  ✓ {category} completed {success_rate}")
        return total_files_processed, total_files_found
    
    def _process_files_parallel(self, tiff_files, dataset_path, category, phase, anomaly_type):
        """
        Process files using parallel processing
        """
        # Prepare arguments for parallel processing
        args_list = [
            (tiff_path, self.config, dataset_path, category, phase, anomaly_type)
            for tiff_path in tiff_files
        ]
        
        success_count = 0
        
        # Process in batches to avoid overwhelming the system
        for i in range(0, len(args_list), self.batch_size):
            batch_args = args_list[i:i + self.batch_size]
            
            with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
                # Submit batch jobs
                future_to_args = {executor.submit(process_single_file, args): args for args in batch_args}
                
                # Process results with simplified progress bar
                with tqdm(total=len(batch_args), desc=f"Processing", leave=False, disable=not self.config.get('verbose', False)) as pbar:
                    for future in as_completed(future_to_args):
                        try:
                            success, sample_name, message = future.result(timeout=300)  # 5 minute timeout
                            if success:
                                success_count += 1
                            else:
                                if self.config.get('verbose', False):
                                    print(f"      ✗ {sample_name}: {message}")
                        except Exception as e:
                            args = future_to_args[future]
                            sample_name = os.path.splitext(os.path.basename(args[0]))[0]
                            if self.config.get('verbose', False):
                                print(f"      ✗ {sample_name}: Exception - {e}")
                        
                        pbar.update(1)
        
        return success_count
    
    def _process_files_sequential(self, tiff_files, dataset_path, category, phase, anomaly_type):
        """
        Process files sequentially
        """
        success_count = 0
        
        for tiff_path in tqdm(tiff_files, desc=f"Processing", leave=False, disable=not self.config.get('verbose', False)):
            args = (tiff_path, self.config, dataset_path, category, phase, anomaly_type)
            
            try:
                success, sample_name, message = process_single_file(args)
                if success:
                    success_count += 1
                elif self.config.get('verbose', False):
                    print(f"      ✗ {sample_name}: {message}")
            except Exception as e:
                sample_name = os.path.splitext(os.path.basename(tiff_path))[0]
                if self.config.get('verbose', False):
                    print(f"      ✗ {sample_name}: Exception - {e}")
        
        return success_count
    
    def process_dataset(self, dataset_path, categories=None, phases=None):
        """
        Process entire dataset with performance monitoring
        """
        if categories is None:
            categories = mvtec3d_classes()
        
        start_time = time.time()
        
        print(f"Processing MVTec 3D-AD dataset")
        print(f"Dataset: {dataset_path}")
        print(f"Categories: {categories}")
        
        # Only show detailed config in verbose mode
        if self.config.get('verbose', False):
            print(f"Config: {self.config}")
        
        # Save config file only in verbose mode
        if self.config.get('verbose', False):
            config_save_path = os.path.join(dataset_path, 'geometric_features_config.yaml')
            with open(config_save_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
        
        total_categories = len(categories)
        total_files_processed = 0
        total_files_found = 0
        
        for i, category in enumerate(categories, 1):
            try:
                cat_processed, cat_found = self.process_category(dataset_path, category, phases)
                total_files_processed += cat_processed
                total_files_found += cat_found
            except Exception as e:
                print(f"  ✗ {category} failed: {e}")
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Simple summary
        print(f"\n✓ Completed: {total_files_processed}/{total_files_found} files processed")
        print(f"  Processing time: {processing_time:.1f}s")
        if total_files_processed > 0:
            print(f"  Average: {processing_time/total_files_processed:.2f}s per file")


def main():
    """
    Main function: parse arguments and execute batch processing with optimizations
    """
    parser = argparse.ArgumentParser(description='MVTec 3D-AD Dataset 3D Geometric Feature Batch Extraction (Optimized)')
    
    parser.add_argument('--dataset_path', type=str, default="/raid/liulinna/projects/M3DM/datasets/mvtec3d/",
                        help='MVTec 3D-AD dataset path')
    parser.add_argument('--config', type=str, default=None,
                        help='Configuration file path')
    parser.add_argument('--categories', nargs='+', default=None,
                        help='Category list to process, default processes all categories')
    parser.add_argument('--phases', nargs='+', default=None,
                        help='Phase list to process (train/test/validation), default processes all phases')
    parser.add_argument('--single_category', type=str, default=None,
                        help='Process only single category')
    parser.add_argument('--num_scales', type=int, default=1, choices=[1, 2, 3],
                        help='Number of scales for multi-scale feature extraction (1, 2, or 3)')
    parser.add_argument('--force_reprocess', action='store_true',
                        help='Force reprocess existing files')  #强制处理已经存在的文件
    
    # Performance optimization arguments
    parser.add_argument('--use_multiprocessing', action='store_true', default=True,
                        help='Enable multiprocessing (default: True)') #使用多进程处理
    parser.add_argument('--max_workers', type=int, default=None,
                        help='Maximum number of worker processes') # 最大工作进程数，可自动确定
    parser.add_argument('--batch_size', type=int, default=50,
                        help='Batch size for parallel processing')
    parser.add_argument('--use_fast_normals', action='store_true', default=True,
                        help='Use fast normal estimation algorithm') #使用快速法线估计算法
    parser.add_argument('--use_parallel_processing', action='store_true', default=True,
                        help='Use parallel processing for curvature computation') #使用并行处理计算曲率

    parser.add_argument('--num_threads', type=int, default=4,
                        help='Number of threads for parallel processing')
    parser.add_argument('--verbose', action='store_true',
                        help='Enable verbose output') #启用详细输出
    parser.add_argument('--use_fast_extractor', action='store_true',
                        help='Use fast feature extractor instead of original (recommended for better performance)')
    parser.add_argument('--k_neighbors', type=int, default=50,
                        help='Number of neighbors for KNN search (only for fast extractor)')
    parser.add_argument('--extractor_batch_size', type=int, default=1000,
                        help='Batch size for fast extractor processing')
    
    args = parser.parse_args()
    
    # Load configuration
    if args.config and os.path.exists(args.config):
        if args.verbose:
            print(f"Loading config file: {args.config}")
        with open(args.config, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
    else:
        # Use default configuration
        config = {
            'num_scales': args.num_scales,
            'force_reprocess': args.force_reprocess,
            'use_multiprocessing': args.use_multiprocessing,
            'max_workers': args.max_workers or min(cpu_count(), 8),
            'batch_size': args.batch_size,
            'use_fast_normals': args.use_fast_normals,
            'use_parallel_processing': args.use_parallel_processing,
            'num_threads': args.num_threads,
            'verbose': args.verbose,
            'use_fast_extractor': args.use_fast_extractor,
            'k_neighbors': args.k_neighbors,
            'extractor_batch_size': args.extractor_batch_size
        }
    
    # Command line arguments override config file
    if args.num_scales:
        config['num_scales'] = args.num_scales
    if args.force_reprocess:
        config['force_reprocess'] = args.force_reprocess
    if args.max_workers:
        config['max_workers'] = args.max_workers
    config['batch_size'] = args.batch_size
    config['use_fast_normals'] = args.use_fast_normals
    config['use_parallel_processing'] = args.use_parallel_processing
    config['num_threads'] = args.num_threads
    config['verbose'] = args.verbose
    config['use_fast_extractor'] = args.use_fast_extractor
    config['k_neighbors'] = args.k_neighbors
    config['extractor_batch_size'] = args.extractor_batch_size
    
    # Determine categories to process
    if args.single_category:
        categories = [args.single_category]
    elif args.categories:
        categories = args.categories
    else:
        categories = None  # Process all categories
    
    # Only show performance configuration in verbose mode
    if args.verbose:
        print(f"Performance configuration:")
        print(f"  Feature extractor: {'Fast' if config.get('use_fast_extractor', False) else 'Original'}")
        print(f"  Scales: {config['num_scales']}")
        print(f"  Multiprocessing: {config.get('use_multiprocessing', True)}")
        print(f"  Max workers: {config.get('max_workers', 'auto')}")
        print(f"  Batch size: {config.get('batch_size', 50)}")
        print(f"  Fast normals: {config.get('use_fast_normals', True)}")
        print(f"  Parallel curvature: {config.get('use_parallel_processing', True)}")
        if config.get('use_fast_extractor', False):
            print(f"  K neighbors: {config.get('k_neighbors', 50)}")
            print(f"  Extractor batch size: {config.get('extractor_batch_size', 1000)}")
    
    # Create processor and execute
    processor = MVTec3DGeometricProcessor(config)
    processor.process_dataset(
        dataset_path=args.dataset_path,
        categories=categories,
        phases=args.phases
    )


if __name__ == "__main__":
    main()