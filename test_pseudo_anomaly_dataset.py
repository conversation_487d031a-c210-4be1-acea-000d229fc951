#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版：测试集成伪异常生成功能的IA-CRF数据集
修复图像显示问题和几何异常生成概率问题

Author: IA-CRF Project
Date: 2025-08-29
"""

import os
import sys
import yaml
import torch
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data.iacrf_dataset import get_iacrf_dataloader, mvtec3d_classes


def load_test_config():
    """
    加载测试配置（修复版）
    """
    config = {
        # 数据集配置
        'dataset': {
            'dataset_path': "/raid/liulinna/projects/M3DM/datasets/mvtec3d/",  # 需要修改为实际路径
            'category': 'bagel'
        },
        
        # 伪异常生成配置
        'pseudo_anomaly': {
            'enable': True,
            'geometric': {
                'probability': 1.0,  # 设置为100%用于调试
                'displacement_strength_range': [0.02, 0.08],
                'num_patches_range': [1, 3],
                'patch_coverage_ratio': 0.15
            },
            'material': {
                'probability': 0.8,  # 提高材质异常概率用于调试
                'texture_path': "/raid/liulinna/projects/EasyNet/datasets/dtd/images/",  # DTD纹理数据集路径，需要修改为实际路径
                'pattern_types': ['texture', 'color', 'brightness']
            }
        },
        
        # 日志配置
        'logging': {
            'verbose': True
        },
        
        # 训练配置
        'training': {
            'batch_size': 1,  # 减小batch size用于调试
            'num_workers': 0
        }
    }
    
    return config


def tensor_to_numpy_image(tensor_img, denormalize=True):
    """
    正确地将tensor转换为可视化的numpy图像
    """
    # ImageNet标准化参数
    IMAGENET_MEAN = np.array([0.485, 0.456, 0.406])
    IMAGENET_STD = np.array([0.229, 0.224, 0.225])
    
    if len(tensor_img.shape) == 4:
        tensor_img = tensor_img.squeeze(0)  # 移除batch维度
    
    img = tensor_img.permute(1, 2, 0).cpu().numpy()
    
    # 反标准化ImageNet预处理
    if denormalize:
        img = img * IMAGENET_STD + IMAGENET_MEAN
        img = np.clip(img, 0, 1)
    
    return img


def test_geometric_anomaly_generator():
    """
    独立测试几何异常生成器
    """
    print("\n--- 独立测试几何异常生成器 ---")
    
    try:
        from pseudo_anomaly_generation.geometric.geometric_anomaly_generator import GeometricAnomalyGenerator
        
        # 创建几何异常生成器
        geometric_config = {
            'patch_selection': {
                'min_patch_size': 100,
                'max_patch_size': 400,
                'expansion_radius': 0.03
            },
            'displacement': {
                'displacement_strength_range': [0.02, 0.08],
                'center_weight': 1.0,
                'edge_weight': 0.2
            },
            'shading_transformation': {
                'light_direction': [0.3, 0.3, 0.9],
                'ambient_light': 0.2,
                'diffuse_strength': 0.8,
                'gaussian_blur_sigma': 1.5
            },
            'num_patches_range': [1, 3],
            'patch_coverage_ratio': 0.15,
            'anomaly_probability': 1.0  # 100%概率用于测试
        }
        
        generator = GeometricAnomalyGenerator(geometric_config)
        print("✓ 几何异常生成器创建成功")
        print(f"  anomaly_probability: {generator.anomaly_probability}")
        print(f"  num_patches_range: {generator.num_patches_range}")
        print(f"  patch_coverage_ratio: {generator.patch_coverage_ratio}")
        
        # 创建模拟数据
        H, W = 224, 224
        mock_features = np.random.random((H*W, 7))
        mock_features[:, :3] = np.random.random((H*W, 3)) * 0.1  # XYZ坐标
        mock_features[:, 3:6] = np.random.random((H*W, 3)) * 2 - 1  # 法线
        mock_features[:, 6] = np.random.random(H*W) * 0.01  # 曲率
        
        mock_shading = np.random.random((H, W, 3))
        
        print(f"\n模拟数据创建:")
        print(f"  特征形状: {mock_features.shape}")
        print(f"  Shading形状: {mock_shading.shape}")
        print(f"  特征范围: XYZ[{np.min(mock_features[:,:3]):.3f}, {np.max(mock_features[:,:3]):.3f}]")
        
        # 测试异常生成
        print("\n开始测试异常生成...")
        result = generator.generate_single_geometric_anomaly(
            geometric_features=mock_features,
            original_shading=mock_shading
        )
        
        print(f"\n几何异常生成结果:")
        print(f"  有异常: {result['has_anomaly'][0]}")
        print(f"  异常类型: {result['anomaly_type']}")
        print(f"  补丁数量: {len(result['patches_info'])}")
        
        if 'generation_params' in result:
            params = result['generation_params']
            print(f"  总位移点数: {params.get('total_displaced_points', 0)}")
            print(f"  最大位移: {params.get('max_displacement', 0):.6f}")
            print(f"  Shading变化幅度: {params.get('shading_change_magnitude', 0):.6f}")
        
        return result['has_anomaly'][0] > 0
        
    except Exception as e:
        print(f"几何异常生成器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def visualize_pseudo_anomaly_sample(sample, save_path=None):
    """
    修复版：可视化伪异常样本
    """
    fig, axes = plt.subplots(3, 4, figsize=(16, 12))
    fig.suptitle('IA-CRF Pseudo Anomaly Generation Results (Fixed)', fontsize=16)
    
    # 转换tensor到numpy用于可视化（修复版）
    original_albedo = tensor_to_numpy_image(sample['albedo'], denormalize=True)
    original_shading = tensor_to_numpy_image(sample['shading'], denormalize=True)
    
    augmented_albedo = tensor_to_numpy_image(sample['augmented_albedo'], denormalize=True)
    augmented_shading = tensor_to_numpy_image(sample['augmented_shading'], denormalize=True)
    
    # 异常掩码
    geometric_mask = sample['geometric_anomaly_mask'].squeeze().cpu().numpy()
    material_mask = sample['material_anomaly_mask'].squeeze().cpu().numpy()
    
    # 异常状态
    has_geometric = sample['has_geometric_anomaly'].item()
    has_material = sample['has_material_anomaly'].item()
    anomaly_type = sample['anomaly_type']
    
    print(f"\nSample debug info (Fixed):")
    print(f"  Anomaly type: {anomaly_type}")
    print(f"  Has geometric: {has_geometric}")
    print(f"  Has material: {has_material}")
    print(f"  Geometric mask sum: {np.sum(geometric_mask)}")
    print(f"  Material mask sum: {np.sum(material_mask)}")
    print(f"  Original albedo range: [{np.min(original_albedo):.3f}, {np.max(original_albedo):.3f}]")
    print(f"  Augmented albedo range: [{np.min(augmented_albedo):.3f}, {np.max(augmented_albedo):.3f}]")
    print(f"  Original shading range: [{np.min(original_shading):.3f}, {np.max(original_shading):.3f}]")
    print(f"  Augmented shading range: [{np.min(augmented_shading):.3f}, {np.max(augmented_shading):.3f}]")
    
    # 检查是否真的有变化
    albedo_max_diff = np.max(np.abs(augmented_albedo - original_albedo))
    shading_max_diff = np.max(np.abs(augmented_shading - original_shading))
    print(f"  Albedo max difference: {albedo_max_diff:.6f}")
    print(f"  Shading max difference: {shading_max_diff:.6f}")
    
    # 检查点云变化
    original_pc = sample['organized_pointcloud'].squeeze().cpu().numpy()
    augmented_pc = sample['augmented_organized_pointcloud'].squeeze().cpu().numpy()
    pc_max_diff = np.max(np.abs(augmented_pc - original_pc))
    print(f"  Point cloud max difference: {pc_max_diff:.6f}")
    
    # 检查是否有shading transformation的详细信息
    if 'shading_results' in sample and sample['shading_results'] is not None:
        if isinstance(sample['shading_results'], dict):
            shading_stats = sample['shading_results'].get('shading_statistics', {})
            print(f"  Shading transformation statistics:")
            print(f"    Total change: {shading_stats.get('total_change', 'N/A')}")
            print(f"    Max change: {shading_stats.get('max_change', 'N/A')}")
            print(f"    Changed pixels: {shading_stats.get('num_changed_pixels', 'N/A')}")
        else:
            print(f"  Shading results available but not in expected format")
    else:
        print(f"  No shading transformation results available")
    
    # 第一行：原始vs增强 Albedo
    axes[0, 0].imshow(original_albedo)
    axes[0, 0].set_title('Original Albedo (Denormalized)')
    axes[0, 0].axis('off')
    
    axes[0, 1].imshow(augmented_albedo)
    axes[0, 1].set_title(f'Augmented Albedo\n(Material: {has_material:.1f})')
    axes[0, 1].axis('off')
    
    # 第一行：原始vs增强 Shading
    axes[0, 2].imshow(original_shading)
    axes[0, 2].set_title('Original Shading (Denormalized)')
    axes[0, 2].axis('off')
    
    axes[0, 3].imshow(augmented_shading)
    axes[0, 3].set_title(f'Augmented Shading\n(Geometric: {has_geometric:.1f})')
    axes[0, 3].axis('off')
    
    # 第二行：异常掩码
    axes[1, 0].imshow(material_mask, cmap='hot', vmin=0, vmax=1)
    axes[1, 0].set_title(f'Material Anomaly Mask\n(sum: {np.sum(material_mask):.1f})')
    axes[1, 0].axis('off')
    
    axes[1, 1].imshow(geometric_mask, cmap='hot', vmin=0, vmax=1)
    axes[1, 1].set_title(f'Geometric Anomaly Mask\n(sum: {np.sum(geometric_mask):.1f})')
    axes[1, 1].axis('off')
    
    # 组合掩码
    combined_mask = np.maximum(material_mask, geometric_mask)
    axes[1, 2].imshow(combined_mask, cmap='hot', vmin=0, vmax=1)
    axes[1, 2].set_title(f'Combined Anomaly Mask\n(sum: {np.sum(combined_mask):.1f})')
    axes[1, 2].axis('off')
    
    # 异常信息
    axes[1, 3].text(0.1, 0.9, f'Anomaly Type: {anomaly_type}', fontsize=11, 
                   transform=axes[1, 3].transAxes, weight='bold')
    axes[1, 3].text(0.1, 0.7, f'Has Geometric: {has_geometric:.1f}', fontsize=11, 
                   transform=axes[1, 3].transAxes)
    axes[1, 3].text(0.1, 0.5, f'Has Material: {has_material:.1f}', fontsize=11, 
                   transform=axes[1, 3].transAxes)
    axes[1, 3].text(0.1, 0.3, f'RGB Path: {Path(sample["rgb_path"]).name}', fontsize=9, 
                   transform=axes[1, 3].transAxes)
    axes[1, 3].text(0.1, 0.1, f'Geo mask max: {np.max(geometric_mask):.3f}', fontsize=9, 
                   transform=axes[1, 3].transAxes)
    axes[1, 3].set_title('Sample Information')
    axes[1, 3].axis('off')
    
    # 第三行：差异图
    albedo_diff = np.abs(augmented_albedo - original_albedo)
    shading_diff = np.abs(augmented_shading - original_shading)
    
    axes[2, 0].imshow(albedo_diff, vmin=0, vmax=np.max(albedo_diff) if np.max(albedo_diff) > 0 else 1)
    axes[2, 0].set_title(f'Albedo Difference\n(max: {np.max(albedo_diff):.4f})')
    axes[2, 0].axis('off')
    
    axes[2, 1].imshow(shading_diff, vmin=0, vmax=np.max(shading_diff) if np.max(shading_diff) > 0 else 1)
    axes[2, 1].set_title(f'Shading Difference\n(max: {np.max(shading_diff):.4f})')
    axes[2, 1].axis('off')
    
    # 点云统计
    original_pc = sample['organized_pointcloud'].squeeze().cpu().numpy()
    augmented_pc = sample['augmented_organized_pointcloud'].squeeze().cpu().numpy()
    
    pc_diff = np.abs(augmented_pc - original_pc)
    pc_diff_max = np.max(pc_diff, axis=2)  # 最大坐标差异
    
    axes[2, 2].imshow(pc_diff_max, cmap='viridis', vmin=0, vmax=np.max(pc_diff_max) if np.max(pc_diff_max) > 0 else 1)
    axes[2, 2].set_title(f'Point Cloud Difference\n(max: {np.max(pc_diff):.6f})')
    axes[2, 2].axis('off')
    
    # 统计信息
    axes[2, 3].text(0.05, 0.9, f'Original PC: {original_pc.shape}', fontsize=9, 
                   transform=axes[2, 3].transAxes)
    axes[2, 3].text(0.05, 0.8, f'Augmented PC: {augmented_pc.shape}', fontsize=9, 
                   transform=axes[2, 3].transAxes)
    axes[2, 3].text(0.05, 0.7, f'PC Max Diff: {np.max(pc_diff):.6f}', fontsize=9, 
                   transform=axes[2, 3].transAxes)
    axes[2, 3].text(0.05, 0.6, f'PC Mean Diff: {np.mean(pc_diff):.6f}', fontsize=9, 
                   transform=axes[2, 3].transAxes)
    axes[2, 3].text(0.05, 0.5, f'PC Valid Points: {np.sum(original_pc[:,:,2] > 0)}', fontsize=9, 
                   transform=axes[2, 3].transAxes)
    
    # 检查是否真的有异常生成
    has_any_change = (np.max(albedo_diff) > 0.001 or np.max(shading_diff) > 0.001 or np.max(pc_diff) > 0.001)
    color = 'green' if has_any_change else 'red'
    axes[2, 3].text(0.05, 0.3, f'Has Visual Change: {has_any_change}', fontsize=9, 
                   transform=axes[2, 3].transAxes, color=color, weight='bold')
    
    axes[2, 3].set_title('Statistics & Validation')
    axes[2, 3].axis('off')
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"可视化已保存至: {save_path}")
    else:
        plt.show()
    
    plt.close()
    
    return has_any_change


def test_pseudo_anomaly_dataset():
    """
    修复版：测试伪异常生成数据集
    """
    print("="*60)
    print("测试IA-CRF伪异常生成数据集 (修复版)")
    print("="*60)
    
    # 加载配置
    config = load_test_config()
    
    # 检查数据集路径
    dataset_path = config['dataset']['dataset_path']
    if not os.path.exists(dataset_path):
        print(f"Error: 数据集路径不存在: {dataset_path}")
        print("请修改 config['dataset']['dataset_path'] 为正确的MVTec 3D-AD数据集路径")
        return False
    
    # 检查纹理路径
    texture_path = config['pseudo_anomaly']['material']['texture_path']
    enable_material_anomaly = os.path.exists(texture_path) if texture_path else False
    
    if not enable_material_anomaly:
        print(f"Warning: 纹理数据集路径不存在: {texture_path}")
        print("材质异常生成将被禁用，仅测试几何异常")
        config['pseudo_anomaly']['material']['texture_path'] = None
    
    # 独立测试几何异常生成器
    geo_test_result = test_geometric_anomaly_generator()
    if not geo_test_result:
        print("Warning: 几何异常生成器独立测试失败")
    
    try:
        # 创建训练数据加载器
        print("\n创建训练数据加载器...")
        train_loader = get_iacrf_dataloader(
            split='train',
            class_name=config['dataset']['category'],
            dataset_path=dataset_path,
            config=config,
            batch_size=config['training']['batch_size'],
            shuffle=True,
            num_workers=config['training']['num_workers'],
            enable_pseudo_anomaly=config['pseudo_anomaly']['enable'],
            anomaly_source_path=texture_path if enable_material_anomaly else None
        )
        
        print(f"数据加载器创建成功，数据集大小: {len(train_loader.dataset)}")
        
        # 检查几何异常生成器是否正确初始化
        if hasattr(train_loader.dataset, 'geometric_generator') and train_loader.dataset.geometric_generator is not None:
            geo_gen = train_loader.dataset.geometric_generator
            print(f"\n几何异常生成器配置:")
            print(f"  anomaly_probability: {geo_gen.anomaly_probability}")
            print(f"  num_patches_range: {geo_gen.num_patches_range}")
            print(f"  patch_coverage_ratio: {geo_gen.patch_coverage_ratio}")
            print(f"  displacement config: {geo_gen.displacer.displacement_strength_range if hasattr(geo_gen, 'displacer') else 'N/A'}")
        else:
            print("\nWarning: 几何异常生成器未正确初始化!")
        
        # 测试多个样本以查看异常生成
        print(f"\n开始测试样本 (寻找异常生成)...")
        
        anomaly_stats = {
            'total_samples': 0,
            'geometric_anomalies': 0,
            'material_anomalies': 0,
            'both_anomalies': 0,
            'no_anomalies': 0,
            'visual_changes': 0
        }
        
        for i, batch in enumerate(train_loader):
            anomaly_stats['total_samples'] += 1
            
            print(f"\n--- 样本 {i+1} ---")
            print(f"RGB图像形状: {batch['rgb_image'].shape}")
            print(f"异常类型: {batch['anomaly_type']}")
            
            has_geo = batch['has_geometric_anomaly'].item()
            has_mat = batch['has_material_anomaly'].item()
            
            print(f"有几何异常: {has_geo}")
            print(f"有材质异常: {has_mat}")
            
            # 统计
            if has_geo > 0:
                anomaly_stats['geometric_anomalies'] += 1
            if has_mat > 0:
                anomaly_stats['material_anomalies'] += 1
            if has_geo > 0 and has_mat > 0:
                anomaly_stats['both_anomalies'] += 1
            if has_geo == 0 and has_mat == 0:
                anomaly_stats['no_anomalies'] += 1
            
            # 为批次中的每个样本创建可视化
            batch_size = batch['rgb_image'].shape[0]
            for j in range(batch_size):
                # 提取单个样本
                sample = {}
                for key, value in batch.items():
                    if isinstance(value, torch.Tensor):
                        sample[key] = value[j:j+1]  # 保持batch维度
                    elif isinstance(value, list):
                        sample[key] = value[j]
                    else:
                        sample[key] = value
                
                # 创建可视化
                save_path = f"./test_results_fixed/pseudo_anomaly_sample_{i+1}_{j+1}_fixed.png"
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                
                try:
                    has_visual_change = visualize_pseudo_anomaly_sample(sample, save_path)
                    if has_visual_change:
                        anomaly_stats['visual_changes'] += 1
                except Exception as e:
                    print(f"Warning: 可视化失败: {e}")
            
            # 测试更多样本以收集统计
            if i >= 9:  # 测试10个样本
                break
        
        # 打印统计结果
        print(f"\n" + "="*50)
        print("异常生成统计结果:")
        print(f"总样本数: {anomaly_stats['total_samples']}")
        print(f"几何异常: {anomaly_stats['geometric_anomalies']} ({anomaly_stats['geometric_anomalies']/anomaly_stats['total_samples']*100:.1f}%)")
        print(f"材质异常: {anomaly_stats['material_anomalies']} ({anomaly_stats['material_anomalies']/anomaly_stats['total_samples']*100:.1f}%)")
        print(f"混合异常: {anomaly_stats['both_anomalies']} ({anomaly_stats['both_anomalies']/anomaly_stats['total_samples']*100:.1f}%)")
        print(f"无异常: {anomaly_stats['no_anomalies']} ({anomaly_stats['no_anomalies']/anomaly_stats['total_samples']*100:.1f}%)")
        print(f"有视觉变化: {anomaly_stats['visual_changes']} ({anomaly_stats['visual_changes']/anomaly_stats['total_samples']*100:.1f}%)")
        print("="*50)
        
        print(f"\n伪异常生成数据集测试完成!")
        print(f"修复版可视化结果保存在: ./test_results_fixed/")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_pseudo_anomaly_dataset()
    
    if success:
        print("\n✓ 修复版伪异常生成数据集测试成功!")
        print("\n主要修复:")
        print("1. ✓ 修复了ImageNet反标准化问题，现在图像显示正常")
        print("2. ✓ 将几何异常概率设置为100%用于调试")
        print("3. ✓ 添加了独立的异常生成器测试")
        print("4. ✓ 增加了详细的统计信息和可视化验证")
        print("\n使用说明:")
        print("1. 检查 test_results_fixed/ 文件夹中的可视化结果")
        print("2. 如果仍然没有几何异常，请检查几何异常生成器的实现")
        print("3. 正常训练时可以将异常概率调回0.7")
    else:
        print("\n✗ 修复版伪异常生成数据集测试失败")
        print("请检查错误信息并确认数据集路径正确")