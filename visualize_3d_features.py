#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Minimal 3D geometric feature visualization tool
Input .npy file path to visualize original point cloud, depth map, curvature, and normals
"""
import matplotlib
matplotlib.use("WebAgg")

import os
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import argparse

class Visualizer:
    def __init__(self, npy_file_path):
        self.npy_file_path = npy_file_path
        self.features = np.load(npy_file_path)
        print(f"Loaded raw features shape: {self.features.shape}")
        
        # 处理224*224*7格式的数据
        if len(self.features.shape) == 3 and self.features.shape[0] == 224 and self.features.shape[1] == 224:
            # 将224*224*7转换为(224*224)*7格式
            self.features = self.features.reshape(-1, self.features.shape[2])
        
        self.num_points, self.feature_dim = self.features.shape
        self.num_scales = (self.feature_dim - 3) // 4
        self.original_shape = self._infer_shape()
        print(f"Loaded: {os.path.basename(npy_file_path)}, Shape: {self.features.shape}")
    
    def _infer_shape(self):
        sqrt_points = int(np.sqrt(self.num_points))
        if sqrt_points * sqrt_points == self.num_points:
            return (sqrt_points, sqrt_points)
        return (224, 224)
    
    def get_positions(self):
        return self.features[:, :3]
    
    def get_normals(self, scale_idx=0):
        start_idx = 3 + scale_idx * 4
        return self.features[:, start_idx:start_idx+3]
    
    def get_curvatures(self, scale_idx=0):
        curvature_idx = 3 + scale_idx * 4 + 3
        return self.features[:, curvature_idx]
    
    def filter_valid_points(self):
        positions = self.get_positions()
        return (np.abs(positions).sum(axis=1) > 1e-6)
    
    def visualize(self, save_dir=None):
        # Filter valid points and downsample
        valid_mask = self.filter_valid_points()
        valid_indices = np.where(valid_mask)[0]
        
        if len(valid_indices) == 0:
            print("No valid points found")
            return
        
        max_points = 3000 # 控制显示的点数
        if len(valid_indices) > max_points:
            sample_indices = np.random.choice(valid_indices, max_points, replace=False)
        else:
            sample_indices = valid_indices
        
        positions = self.get_positions()[sample_indices]
        
        # Create visualization
        fig = plt.figure(figsize=(16, 10))
        fig.suptitle(f'3D Geometric Features Visualization - {os.path.basename(self.npy_file_path)}', fontsize=16)
        
        # 1. Original point cloud
        ax1 = fig.add_subplot(2, 3, 1, projection='3d')
        ax1.scatter(positions[:, 0], positions[:, 1], positions[:, 2], 
                   c=positions[:, 2], cmap='viridis', alpha=0.6, s=1)
        ax1.set_title('Original Point Cloud')
        ax1.set_xlabel('X')
        ax1.set_ylabel('Y')
        ax1.set_zlabel('Z')
        
        # 2. Depth map
        ax2 = fig.add_subplot(2, 3, 2)
        H, W = self.original_shape
        depth_map = self.get_positions()[:, 2].reshape(H, W)
        depth_map[depth_map == 0] = np.nan
        im2 = ax2.imshow(depth_map, cmap='viridis', interpolation='bilinear')
        ax2.set_title('Depth Map')
        plt.colorbar(im2, ax=ax2, label='Depth Value')
        
        # 3. Curvature
        ax3 = fig.add_subplot(2, 3, 3, projection='3d')
        curvatures = self.get_curvatures(0)[sample_indices]
        scatter3 = ax3.scatter(positions[:, 0], positions[:, 1], positions[:, 2], 
                              c=curvatures, cmap='plasma', alpha=0.7, s=2)
        ax3.set_title('Curvature')
        plt.colorbar(scatter3, ax=ax3, label='Curvature Value')
        
        # # 4. Normal length
        # ax4 = fig.add_subplot(2, 3, 4, projection='3d')
        # normals = self.get_normals(0)[sample_indices]
        # normal_lengths = np.linalg.norm(normals, axis=1)
        # scatter4 = ax4.scatter(positions[:, 0], positions[:, 1], positions[:, 2], 
        #                       c=normal_lengths, cmap='coolwarm', alpha=0.7, s=2)
        # ax4.set_title('Normal Length')
        # plt.colorbar(scatter4, ax=ax4, label='Normal Length')
        # 4. Normal directions (修改部分)
        ax4 = fig.add_subplot(2, 3, 4, projection='3d')
        normals = self.get_normals(0)[sample_indices]
        # 进一步减少点数以清晰显示法向量
        max_normal_points = min(100, len(sample_indices))
        if len(sample_indices) > max_normal_points:
            normal_indices = np.random.choice(len(sample_indices), max_normal_points, replace=False)
        else:
            normal_indices = np.arange(len(sample_indices))
        
        # 绘制点云
        ax4.scatter(positions[:, 0], positions[:, 1], positions[:, 2], 
                   c='lightgray', alpha=0.3, s=1)
        
        # 绘制法向量方向
        scale_factor = 0.005  # 调整箭头长度
        ax4.quiver(positions[normal_indices, 0], positions[normal_indices, 1], positions[normal_indices, 2],
                  normals[normal_indices, 0], normals[normal_indices, 1], normals[normal_indices, 2],
                  length=scale_factor, color='red', arrow_length_ratio=0.3, linewidth=0.5)
        
        ax4.set_title('Normal Directions')
        ax4.set_xlabel('X')
        ax4.set_ylabel('Y')
        ax4.set_zlabel('Z')

        
        # 5. Curvature distribution
        ax5 = fig.add_subplot(2, 3, 5)
        all_curvatures = self.get_curvatures(0)[valid_mask]
        non_zero_curvatures = all_curvatures[all_curvatures > 1e-6]
        if len(non_zero_curvatures) > 0:
            ax5.hist(non_zero_curvatures, bins=50, alpha=0.7, edgecolor='black')
            ax5.set_xlabel('Curvature Value')
            ax5.set_ylabel('Frequency')
            ax5.set_title('Curvature Distribution')
            ax5.grid(True, alpha=0.3)
        else:
            ax5.text(0.5, 0.5, 'No valid curvature data', ha='center', va='center')
            ax5.set_title('Curvature Distribution')
        
        # 6. Basic information
        ax6 = fig.add_subplot(2, 3, 6)
        ax6.axis('off')
        
        valid_positions = self.get_positions()[valid_mask]
        stats_text = f"""File: {os.path.basename(self.npy_file_path)}
Feature Shape: {self.features.shape}
Valid Points: {len(valid_indices):,}

Position Range:
X: [{valid_positions[:, 0].min():.3f}, {valid_positions[:, 0].max():.3f}]
Y: [{valid_positions[:, 1].min():.3f}, {valid_positions[:, 1].max():.3f}]
Z: [{valid_positions[:, 2].min():.3f}, {valid_positions[:, 2].max():.3f}]

Curvature Range: [{curvatures.min():.6f}, {curvatures.max():.6f}]"""
        
        ax6.text(0.05, 0.95, stats_text, transform=ax6.transAxes, 
                fontsize=9, verticalalignment='top', fontfamily='monospace')
        
        plt.tight_layout()
        
        # Save image
        if save_dir:
            os.makedirs(save_dir, exist_ok=True)
            save_path = os.path.join(save_dir, f"{os.path.splitext(os.path.basename(self.npy_file_path))[0]}_visualization.png")
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Saved to: {save_path}")
        
        plt.show()

def main():
    parser = argparse.ArgumentParser(description='3D Geometric Feature Visualization Tool')
    parser.add_argument('--npy_file', type=str, help='.npy feature file path')
    parser.add_argument('--save_dir', type=str, default="./vis_output", help='Save directory')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.npy_file):
        print(f"Error: File not found {args.npy_file}")
        return
    
    try:
        visualizer = Visualizer(args.npy_file)
        visualizer.visualize(save_dir=args.save_dir)
    except Exception as e:
        print(f"Visualization failed: {e}")

if __name__ == "__main__":
    main()