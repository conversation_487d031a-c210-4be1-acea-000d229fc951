#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IA-CRF Network Inference and Evaluation Module
Handles model inference and anomaly detection evaluation

Author: IA-CRF Project
Date: 2025-08-27
"""

import os
import sys
import yaml
import time
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from tqdm import tqdm

import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from sklearn.metrics import roc_auc_score, average_precision_score
import cv2

# Add project path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from model.iacrf_network import IACRFNetwork
from data.iacrf_dataset import get_iacrf_dataloader


class IACRFInference:
    """
    IA-CRF Network Inference Engine
    Handles model loading and inference for anomaly detection
    """
    
    def __init__(self, model_path: str, config_path: str, category: str):
        self.model_path = model_path
        self.config_path = config_path
        self.category = category
        self.device = self._setup_device()
        
        # Load configuration
        with open(config_path, 'r') as f:
            self.config = yaml.safe_load(f)
        
        self.config['dataset']['category'] = category
        
        # Initialize model
        self.network = self._load_model()
        
        print(f"IA-CRF Inference initialized for category: {category}")
        print(f"Model loaded from: {model_path}")
        print(f"Device: {self.device}")
    
    def _setup_device(self) -> torch.device:
        """Setup inference device"""
        if torch.cuda.is_available():
            device = torch.device('cuda')
        else:
            device = torch.device('cpu')
        return device
    
    def _load_model(self) -> IACRFNetwork:
        """Load trained IA-CRF model"""
        # Create network
        network = IACRFNetwork(self.config).to(self.device)
        
        # Load checkpoint
        checkpoint = torch.load(self.model_path, map_location=self.device)
        
        if 'model_state_dict' in checkpoint:
            network.load_state_dict(checkpoint['model_state_dict'])
        else:
            network.load_state_dict(checkpoint)
        
        network.eval()
        print("Model loaded successfully")
        
        return network
    
    def predict_sample(self, sample_data: Dict) -> Dict:
        """
        Predict anomaly for single sample
        
        Args:
            sample_data: Dictionary containing input data
            
        Returns:
            predictions: Dictionary containing predictions and scores
        """
        with torch.no_grad():
            # Move data to device
            for key in sample_data:
                if isinstance(sample_data[key], torch.Tensor):
                    sample_data[key] = sample_data[key].to(self.device)
            
            # Prepare input (use original data as input for inference)
            input_data = {
                'corrupted_albedo': sample_data['albedo'],
                'corrupted_shading': sample_data['shading'],
                'corrupted_pointcloud': sample_data['enhanced_pointcloud']
            }
            
            # Forward pass
            predictions = self.network(input_data)
            
            # Extract anomaly scores
            if 'fusion_results' in predictions:
                fusion_results = predictions['fusion_results']
                if 'final_anomaly_score' in fusion_results:
                    anomaly_score = fusion_results['final_anomaly_score'].cpu().item()
                else:
                    anomaly_score = 0.0
            else:
                anomaly_score = 0.0
            
            result = {
                'anomaly_score': anomaly_score,
                'predictions': predictions,
                'sample_data': sample_data
            }
            
            return result
    
    def predict_batch(self, data_loader: DataLoader) -> List[Dict]:
        """
        Predict anomaly for batch of samples
        
        Args:
            data_loader: DataLoader for samples
            
        Returns:
            batch_predictions: List of prediction results
        """
        batch_predictions = []
        
        for batch_idx, batch in enumerate(tqdm(data_loader, desc="Inference")):
            batch_size = batch['albedo'].size(0)
            
            for i in range(batch_size):
                # Extract single sample
                sample_data = {}
                for key, value in batch.items():
                    if isinstance(value, torch.Tensor):
                        sample_data[key] = value[i:i+1]
                    else:
                        sample_data[key] = value[i] if isinstance(value, list) else value
                
                # Predict
                prediction = self.predict_sample(sample_data)
                prediction['batch_idx'] = batch_idx
                prediction['sample_idx'] = i
                
                batch_predictions.append(prediction)
        
        return batch_predictions


class IACRFEvaluator:
    """
    IA-CRF Network Evaluator
    Handles model evaluation and metric computation
    """
    
    def __init__(self, inference_engine: IACRFInference):
        self.inference_engine = inference_engine
        self.category = inference_engine.category
        
    def evaluate_category(self, save_results: bool = True) -> Dict:
        """
        Evaluate model on category test set
        
        Args:
            save_results: Whether to save evaluation results
            
        Returns:
            evaluation_results: Dictionary containing metrics and results
        """
        print(f"Evaluating IA-CRF model on {self.category} test set...")
        
        # Create test data loader
        test_loader = get_iacrf_dataloader(
            split='test',
            class_name=self.category,
            dataset_path=self.inference_engine.config['dataset']['dataset_path'],
            config=self.inference_engine.config,
            batch_size=1,
            shuffle=False,
            num_workers=0
        )
        
        # Run inference
        predictions = self.inference_engine.predict_batch(test_loader)
        
        # Collect results
        anomaly_scores = []
        ground_truth_labels = []
        sample_info = []
        
        for pred in predictions:
            anomaly_scores.append(pred['anomaly_score'])
            
            # Extract ground truth label
            if 'label' in pred['sample_data']:
                gt_label = pred['sample_data']['label']
                if isinstance(gt_label, torch.Tensor):
                    gt_label = gt_label.item()
                ground_truth_labels.append(int(gt_label))
            else:
                ground_truth_labels.append(0)  # Default to normal
            
            # Sample info
            sample_info.append({
                'rgb_path': pred['sample_data'].get('rgb_path', ''),
                'defect_type': pred['sample_data'].get('defect_type', 'unknown'),
                'anomaly_score': pred['anomaly_score']
            })
        
        # Compute metrics
        metrics = self._compute_metrics(anomaly_scores, ground_truth_labels)
        
        # Create evaluation results
        evaluation_results = {
            'category': self.category,
            'metrics': metrics,
            'predictions': {
                'anomaly_scores': anomaly_scores,
                'ground_truth_labels': ground_truth_labels,
                'sample_info': sample_info
            },
            'summary': {
                'total_samples': len(predictions),
                'normal_samples': ground_truth_labels.count(0),
                'anomaly_samples': ground_truth_labels.count(1),
                'average_score': np.mean(anomaly_scores)
            }
        }
        
        # Print results
        self._print_evaluation_results(evaluation_results)
        
        # Save results
        if save_results:
            self._save_evaluation_results(evaluation_results)
        
        return evaluation_results
    
    def _compute_metrics(self, scores: List[float], labels: List[int]) -> Dict:
        """Compute evaluation metrics"""
        scores = np.array(scores)
        labels = np.array(labels)
        
        metrics = {}
        
        try:
            # Image-level AUROC
            metrics['image_auroc'] = roc_auc_score(labels, scores)
        except:
            metrics['image_auroc'] = 0.0
        
        try:
            # Average Precision
            metrics['average_precision'] = average_precision_score(labels, scores)
        except:
            metrics['average_precision'] = 0.0
        
        # Optimal threshold (Youden's J statistic)
        if len(np.unique(labels)) > 1:
            from sklearn.metrics import roc_curve
            fpr, tpr, thresholds = roc_curve(labels, scores)
            optimal_idx = np.argmax(tpr - fpr)
            optimal_threshold = thresholds[optimal_idx]
            
            metrics['optimal_threshold'] = optimal_threshold
            metrics['optimal_sensitivity'] = tpr[optimal_idx]
            metrics['optimal_specificity'] = 1 - fpr[optimal_idx]
        else:
            metrics['optimal_threshold'] = 0.5
            metrics['optimal_sensitivity'] = 0.0
            metrics['optimal_specificity'] = 1.0
        
        # Statistics
        normal_scores = scores[labels == 0]
        anomaly_scores = scores[labels == 1]
        
        metrics['normal_score_mean'] = float(np.mean(normal_scores)) if len(normal_scores) > 0 else 0.0
        metrics['normal_score_std'] = float(np.std(normal_scores)) if len(normal_scores) > 0 else 0.0
        metrics['anomaly_score_mean'] = float(np.mean(anomaly_scores)) if len(anomaly_scores) > 0 else 0.0
        metrics['anomaly_score_std'] = float(np.std(anomaly_scores)) if len(anomaly_scores) > 0 else 0.0
        
        return metrics
    
    def _print_evaluation_results(self, results: Dict):
        """Print evaluation results"""
        metrics = results['metrics']
        summary = results['summary']
        
        print(f"\n{'='*60}")
        print(f"Evaluation Results for {self.category}")
        print(f"{'='*60}")
        print(f"Total samples: {summary['total_samples']}")
        print(f"Normal samples: {summary['normal_samples']}")
        print(f"Anomaly samples: {summary['anomaly_samples']}")
        print(f"Average score: {summary['average_score']:.4f}")
        print(f"\nMetrics:")
        print(f"  Image AUROC: {metrics['image_auroc']:.4f}")
        print(f"  Average Precision: {metrics['average_precision']:.4f}")
        print(f"  Optimal Threshold: {metrics['optimal_threshold']:.4f}")
        print(f"  Sensitivity: {metrics['optimal_sensitivity']:.4f}")
        print(f"  Specificity: {metrics['optimal_specificity']:.4f}")
        print(f"\nScore Statistics:")
        print(f"  Normal - Mean: {metrics['normal_score_mean']:.4f}, Std: {metrics['normal_score_std']:.4f}")
        print(f"  Anomaly - Mean: {metrics['anomaly_score_mean']:.4f}, Std: {metrics['anomaly_score_std']:.4f}")
        print(f"{'='*60}\n")
    
    def _save_evaluation_results(self, results: Dict):
        """Save evaluation results"""
        # Create results directory
        results_dir = Path(f"./results/stage2/{self.category}/")
        results_dir.mkdir(parents=True, exist_ok=True)
        
        # Save metrics
        import json
        metrics_path = results_dir / "evaluation_metrics.json"
        with open(metrics_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        print(f"Evaluation results saved to: {results_dir}")


def main():
    """Main evaluation function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='IA-CRF Model Evaluation')
    parser.add_argument('--model', type=str, required=True,
                       help='Path to trained model checkpoint')
    parser.add_argument('--config', type=str, default='configs/iacrf_training_config.yaml',
                       help='Path to configuration file')
    parser.add_argument('--category', type=str, required=True,
                       help='MVTec 3D-AD category to evaluate')
    parser.add_argument('--save-results', action='store_true',
                       help='Save evaluation results')
    
    args = parser.parse_args()
    
    # Create inference engine
    inference_engine = IACRFInference(
        model_path=args.model,
        config_path=args.config,
        category=args.category
    )
    
    # Create evaluator
    evaluator = IACRFEvaluator(inference_engine)
    
    # Run evaluation
    results = evaluator.evaluate_category(save_results=args.save_results)
    
    print("Evaluation completed!")


if __name__ == "__main__":
    main()