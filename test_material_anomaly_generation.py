"""
测试材质异常生成脚本
输入albedo图像，生成伪异常并可视化对比原始与异常图像

Author: IA-CRF Project
Date: 2025-08-27

使用方法:
python test_material_anomaly_generation.py --albedo_path path/to/albedo.png
"""

import os
import sys
import argparse
import numpy as np
import cv2
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime
import json

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from pseudo_anomaly_generation.material import MaterialAnomalyGenerationSystem
from pseudo_anomaly_generation.configs import get_config


class MaterialAnomalyTester:
    """材质异常生成测试器"""
    
    def __init__(self, config_preset: str = "easynet_default"):
        """
        初始化测试器
        
        Args:
            config_preset: 配置预设名称
        """
        self.config_preset = config_preset
        self.system = None
        
        # 创建输出目录
        self.output_dir = Path("material_anomaly_test_results")
        self.output_dir.mkdir(exist_ok=True)
        
        print(f"材质异常生成测试器初始化完成")
        print(f"输出目录: {self.output_dir.absolute()}")
    
    def setup_system(self, texture_source_path: str = None):
        """
        设置材质异常生成系统
        
        Args:
            texture_source_path: 纹理源路径，None时创建默认纹理
        """
        try:
            # 初始化系统
            self.system = MaterialAnomalyGenerationSystem(preset=self.config_preset)
            
            # 设置纹理源
            if texture_source_path and Path(texture_source_path).exists():
                self.system.set_texture_source(texture_source_path)
                print(f"使用纹理源: {texture_source_path}")
            else:
                # 创建默认测试纹理
                default_texture_dir = self._create_default_textures()
                self.system.set_texture_source(str(default_texture_dir))
                print(f"使用默认纹理源: {default_texture_dir}")
            
            print("材质异常生成系统设置完成")
            
        except Exception as e:
            raise RuntimeError(f"系统设置失败: {e}")
    
    def _create_default_textures(self) -> Path:
        """
        创建默认测试纹理
        
        Returns:
            纹理目录路径
        """
        texture_dir = self.output_dir / "default_textures"
        texture_dir.mkdir(exist_ok=True)
        
        # 创建不同类型的纹理图像
        textures = {
            "noise_texture": self._create_noise_texture(),
            "line_texture": self._create_line_texture(),
            "dot_texture": self._create_dot_texture(),
            "crack_texture": self._create_crack_texture(),
            "stain_texture": self._create_stain_texture()
        }
        
        for name, texture in textures.items():
            cv2.imwrite(str(texture_dir / f"{name}.png"), texture)
        
        print(f"创建了 {len(textures)} 个默认纹理图像")
        return texture_dir
    
    def _create_noise_texture(self, size: int = 256) -> np.ndarray:
        """创建噪声纹理"""
        noise = np.random.randint(0, 256, (size, size, 3), dtype=np.uint8)
        # 添加一些模糊使其更自然
        noise = cv2.GaussianBlur(noise, (3, 3), 1)
        return noise
    
    def _create_line_texture(self, size: int = 256) -> np.ndarray:
        """创建线条纹理"""
        texture = np.ones((size, size, 3), dtype=np.uint8) * 128
        
        # 添加随机线条
        for _ in range(20):
            x1, y1 = np.random.randint(0, size, 2)
            x2, y2 = np.random.randint(0, size, 2)
            color = np.random.randint(50, 200, 3).tolist()
            thickness = np.random.randint(1, 4)
            cv2.line(texture, (x1, y1), (x2, y2), color, thickness)
        
        return texture
    
    def _create_dot_texture(self, size: int = 256) -> np.ndarray:
        """创建点状纹理"""
        texture = np.ones((size, size, 3), dtype=np.uint8) * 200
        
        # 添加随机圆点
        for _ in range(30):
            center = (np.random.randint(0, size), np.random.randint(0, size))
            radius = np.random.randint(3, 15)
            color = np.random.randint(0, 150, 3).tolist()
            cv2.circle(texture, center, radius, color, -1)
        
        return texture
    
    def _create_crack_texture(self, size: int = 256) -> np.ndarray:
        """创建裂纹纹理"""
        texture = np.ones((size, size, 3), dtype=np.uint8) * 180
        
        # 创建裂纹模式
        for _ in range(15):
            # 随机起点
            x, y = size // 2 + np.random.randint(-50, 50), size // 2 + np.random.randint(-50, 50)
            
            # 绘制分叉裂纹
            for step in range(np.random.randint(20, 60)):
                # 随机方向
                dx = np.random.randint(-2, 3)
                dy = np.random.randint(-2, 3)
                x += dx
                y += dy
                
                # 边界检查
                x = np.clip(x, 0, size - 1)
                y = np.clip(y, 0, size - 1)
                
                # 绘制裂纹
                cv2.circle(texture, (x, y), 1, (50, 50, 50), -1)
        
        return texture
    
    def _create_stain_texture(self, size: int = 256) -> np.ndarray:
        """创建污渍纹理"""
        texture = np.ones((size, size, 3), dtype=np.uint8) * 220
        
        # 添加污渍
        for _ in range(10):
            center = (np.random.randint(0, size), np.random.randint(0, size))
            axes = (np.random.randint(10, 40), np.random.randint(10, 40))
            angle = np.random.randint(0, 180)
            color = np.random.randint(80, 150, 3).tolist()
            
            cv2.ellipse(texture, center, axes, angle, 0, 360, color, -1)
        
        # 添加模糊效果
        texture = cv2.GaussianBlur(texture, (5, 5), 2)
        
        return texture
    
    def load_albedo_image(self, albedo_path: str) -> np.ndarray:
        """
        加载albedo图像
        
        Args:
            albedo_path: albedo图像路径
            
        Returns:
            归一化的albedo图像 [0, 1]
        """
        if not Path(albedo_path).exists():
            raise FileNotFoundError(f"Albedo图像不存在: {albedo_path}")
        
        # 加载图像
        image = cv2.imread(albedo_path, cv2.IMREAD_COLOR)
        if image is None:
            raise ValueError(f"无法加载图像: {albedo_path}")
        
        # 转换为RGB
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 归一化到[0, 1]
        image = image.astype(np.float32) / 255.0
        
        # 调整到系统要求的尺寸
        target_size = (224, 224)  # 默认尺寸
        if image.shape[:2] != target_size:
            image = cv2.resize(image, (target_size[1], target_size[0]))
        
        print(f"成功加载albedo图像: {albedo_path}")
        print(f"图像尺寸: {image.shape}")
        
        return image
    
    def load_real_depth_map(self, depth_path: str) -> np.ndarray:
        """
        Load real depth map from .tiff file (MVTec3D format)
        
        Args:
            depth_path: path to .tiff depth file
            
        Returns:
            depth map [0, 1]
        """
        try:
            import tifffile as tiff
            
            if not Path(depth_path).exists():
                raise FileNotFoundError(f"Depth file not found: {depth_path}")
            
            # Load tiff file
            xyz = tiff.imread(depth_path)
            xyz = np.array(xyz)
            
            # Extract Z (depth) channel
            if len(xyz.shape) == 3 and xyz.shape[2] >= 3:
                depth = xyz[:, :, 2]  # Z coordinate
            else:
                raise ValueError(f"Invalid depth file format: {xyz.shape}")
            
            # Normalize depth to [0, 1]
            valid_mask = depth > 0  # Valid depth points
            if np.any(valid_mask):
                depth_min = depth[valid_mask].min()
                depth_max = depth[valid_mask].max()
                
                if depth_max > depth_min:
                    depth_normalized = np.zeros_like(depth, dtype=np.float32)
                    depth_normalized[valid_mask] = (depth[valid_mask] - depth_min) / (depth_max - depth_min)
                else:
                    depth_normalized = np.ones_like(depth, dtype=np.float32) * 0.5
            else:
                depth_normalized = np.zeros_like(depth, dtype=np.float32)
            
            # Resize to target size
            target_size = (224, 224)
            if depth_normalized.shape[:2] != target_size:
                depth_normalized = cv2.resize(depth_normalized, (target_size[1], target_size[0]))
            
            # Convert to three channels
            depth_map_3d = np.stack([depth_normalized] * 3, axis=2)
            
            # Statistics
            foreground_ratio = np.sum(depth_normalized > 0.001) / depth_normalized.size * 100
            valid_depth_range = [depth_normalized[depth_normalized > 0.001].min(), depth_normalized.max()] if np.any(depth_normalized > 0.001) else [0, 0]
            
            print(f"Loaded real depth map from: {Path(depth_path).name}")
            print(f"Original shape: {xyz.shape}, Target shape: {depth_map_3d.shape}")
            print(f"Foreground region ratio: {foreground_ratio:.1f}%")
            print(f"Valid depth range: [{valid_depth_range[0]:.3f}, {valid_depth_range[1]:.3f}]")
            
            return depth_map_3d.astype(np.float32)
            
        except ImportError:
            print("Warning: tifffile not available. Install with: pip install tifffile")
            print("Falling back to default depth map creation...")
            return self.create_default_depth_map(albedo_shape=(224, 224, 3), depth_mode="realistic")
        except Exception as e:
            print(f"Error loading depth map: {e}")
            print("Falling back to default depth map creation...")
            return self.create_default_depth_map(albedo_shape=(224, 224, 3), depth_mode="realistic")
    
    def create_default_depth_map(self, albedo_shape: tuple, depth_mode: str = "realistic") -> np.ndarray:
        """
        Create default depth map that simulates real MVTec3D tiff depth data
        
        Args:
            albedo_shape: albedo image shape
            depth_mode: depth map mode
                - "realistic": simulate real object depth (center elevated, edges low)
                - "foreground": foreground region with uniform high depth, background depth=0
                - "uniform": uniform depth value (all regions are foreground)
            
        Returns:
            depth map [0, 1]
        """
        h, w = albedo_shape[:2]
        
        if depth_mode == "uniform":
            # Uniform depth, all regions are considered foreground
            depth_map = np.ones((h, w), dtype=np.float32) * 0.8
            
        elif depth_mode == "foreground":
            # Create an elliptical foreground region with clear background separation
            center_x, center_y = w // 2, h // 2
            y_coords, x_coords = np.ogrid[:h, :w]
            
            # Ellipse equation with smaller radius for clear separation
            ellipse_mask = ((x_coords - center_x) / (w * 0.35))**2 + ((y_coords - center_y) / (h * 0.35))**2 <= 1
            
            depth_map = np.zeros((h, w), dtype=np.float32)
            depth_map[ellipse_mask] = 0.8  # High depth for foreground
            
            # Add smooth transition at edges
            distance = np.sqrt((x_coords - center_x)**2 + (y_coords - center_y)**2)
            max_radius = min(w, h) * 0.35
            transition_mask = (distance > max_radius * 0.9) & (distance <= max_radius * 1.1)
            depth_map[transition_mask] = 0.4 * np.exp(-(distance[transition_mask] - max_radius * 0.9) / (max_radius * 0.1))
            
        else:  # "realistic"
            # Create realistic object depth map based on MVTec3D characteristics
            center_x, center_y = w // 2, h // 2
            y_coords, x_coords = np.ogrid[:h, :w]
            
            # Create a more realistic depth distribution
            # Most MVTec3D objects have valid depth in the center region
            distance = np.sqrt((x_coords - center_x)**2 + (y_coords - center_y)**2)
            max_distance = min(w, h) * 0.42  # Slightly larger valid region
            
            # Create object-like depth profile
            depth_map = np.zeros((h, w), dtype=np.float32)
            
            # Main object region with varying depth
            object_mask = distance <= max_distance
            # Use a more realistic depth profile (higher in center, gradually decreasing)
            normalized_distance = distance[object_mask] / max_distance
            depth_values = 0.9 * (1 - normalized_distance**1.5) + 0.1  # Range [0.1, 0.9]
            depth_map[object_mask] = depth_values
            
            # Add some noise to make it more realistic
            noise = np.random.normal(0, 0.02, depth_map.shape)
            depth_map = np.clip(depth_map + noise, 0, 1)
            
            # Ensure background is truly background (depth < threshold)
            background_mask = distance > max_distance
            depth_map[background_mask] = 0.0005  # Very small but not zero
        
        # Convert to three channels to match input format
        depth_map_3d = np.stack([depth_map] * 3, axis=2)
        
        # Calculate foreground region statistics
        foreground_ratio = np.sum(depth_map > 0.001) / (h * w) * 100
        valid_depth_range = [depth_map[depth_map > 0.001].min(), depth_map.max()] if np.any(depth_map > 0.001) else [0, 0]
        
        print(f"Created default depth map, size: {depth_map_3d.shape}")
        print(f"Depth mode: {depth_mode}")
        print(f"Foreground region ratio: {foreground_ratio:.1f}%")
        print(f"Valid depth range: [{valid_depth_range[0]:.3f}, {valid_depth_range[1]:.3f}]")
        print(f"Background pixels (depth <= 0.001): {np.sum(depth_map <= 0.001)} / {h*w}")
        
        return depth_map_3d.astype(np.float32)
    
    def generate_anomaly(
        self, 
        albedo_image: np.ndarray, 
        depth_map: np.ndarray = None,
        category: str = "test",
        custom_params: dict = None,
        depth_mode: str = "realistic"
    ) -> dict:
        """
        生成材质异常
        
        Args:
            albedo_image: albedo图像
            depth_map: 深度图，None时自动创建
            category: 产品类别
            custom_params: 自定义参数
            depth_mode: 深度图模式（"realistic", "foreground", "uniform"）
            
        Returns:
            异常生成结果
        """
        if self.system is None:
            raise RuntimeError("系统未初始化，请先调用setup_system()")
        
        # 创建默认深度图
        if depth_map is None:
            depth_map = self.create_default_depth_map(albedo_image.shape, depth_mode)
        
        # 设置生成参数
        if custom_params is None:
            custom_params = {
                "anomaly_probability": 1.0,  # 确保生成异常
                "beta_range": [0.3, 0.7],   # 中等强度
            }
        
        try:
            # 生成异常
            result = self.system.generate_single_anomaly(
                albedo_image=albedo_image,
                depth_map=depth_map,
                category=category,
                custom_params=custom_params
            )
            
            # Add depth map to result for visualization
            result['depth_map'] = depth_map
            result['depth_mode'] = depth_mode
            
            # Add debug information about masks
            if len(result['anomaly_mask'].shape) == 2:
                mask_2d = result['anomaly_mask']
            else:
                mask_2d = result['anomaly_mask'][:, :, 0] if len(result['anomaly_mask'].shape) == 3 else result['anomaly_mask']
            
            depth_single = depth_map[:, :, 0] if len(depth_map.shape) == 3 else depth_map
            foreground_mask = depth_single > 0.001
            
            print(f"Anomaly generation completed:")
            print(f"  - Has anomaly: {'Yes' if result['has_anomaly'][0] > 0 else 'No'}")
            print(f"  - Anomaly coverage: {np.sum(mask_2d) / mask_2d.size * 100:.2f}%")
            print(f"  - Foreground pixels: {np.sum(foreground_mask)} / {foreground_mask.size} ({np.sum(foreground_mask)/foreground_mask.size*100:.1f}%)")
            print(f"  - Anomaly pixels in foreground: {np.sum(mask_2d[foreground_mask] > 0)} / {np.sum(foreground_mask)}")
            print(f"  - Anomaly pixels in background: {np.sum(mask_2d[~foreground_mask] > 0)} / {np.sum(~foreground_mask)}")
            print(f"  - Beta value: {result.get('beta', 'N/A')}")
            print(f"  - Texture source: {Path(result['texture_source']).name if result['texture_source'] else 'None'}")
            
            return result
            
        except Exception as e:
            raise RuntimeError(f"异常生成失败: {e}")
    
    def visualize_results(
        self, 
        original_albedo: np.ndarray, 
        anomaly_result: dict, 
        save_name: str = None
    ) -> str:
        """
        可视化结果
        
        Args:
            original_albedo: 原始albedo图像
            anomaly_result: 异常生成结果
            save_name: 保存文件名
            
        Returns:
            保存的文件路径
        """
        if save_name is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_name = f"anomaly_comparison_{timestamp}"
        
        # 准备数据
        augmented_albedo = anomaly_result['augmented_albedo']
        anomaly_mask = anomaly_result['anomaly_mask']
        depth_map = anomaly_result.get('depth_map')
        depth_mode = anomaly_result.get('depth_mode', 'unknown')
        
        # Create comparison figure (2x3 layout)
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'Material Anomaly Generation Results (Depth Mode: {depth_mode})', fontsize=16, fontweight='bold')
        
        # First row: Original, Anomaly, Depth map
        axes[0, 0].imshow(original_albedo)
        axes[0, 0].set_title('Original Albedo Image', fontsize=12)
        axes[0, 0].axis('off')
        
        axes[0, 1].imshow(augmented_albedo)
        axes[0, 1].set_title('Anomalous Albedo Image', fontsize=12)
        axes[0, 1].axis('off')
        
        # Display depth map
        if depth_map is not None:
            depth_single = depth_map[:, :, 0] if len(depth_map.shape) == 3 else depth_map
            im_depth = axes[0, 2].imshow(depth_single, cmap='viridis')
            axes[0, 2].set_title('Depth Map', fontsize=12)
            axes[0, 2].axis('off')
            plt.colorbar(im_depth, ax=axes[0, 2], fraction=0.046, pad=0.04)
        else:
            axes[0, 2].text(0.5, 0.5, 'No Depth Map', ha='center', va='center', transform=axes[0, 2].transAxes)
            axes[0, 2].axis('off')
        
        # Second row: Anomaly mask, Difference map, Depth constraint visualization
        im_mask = axes[1, 0].imshow(anomaly_mask, cmap='hot')
        axes[1, 0].set_title('Anomaly Mask', fontsize=12)
        axes[1, 0].axis('off')
        plt.colorbar(im_mask, ax=axes[1, 0], fraction=0.046, pad=0.04)
        
        # Difference map
        difference = np.abs(augmented_albedo - original_albedo)
        difference = np.mean(difference, axis=2)  # Convert to grayscale
        im_diff = axes[1, 1].imshow(difference, cmap='jet')
        axes[1, 1].set_title('Difference Map', fontsize=12)
        axes[1, 1].axis('off')
        plt.colorbar(im_diff, ax=axes[1, 1], fraction=0.046, pad=0.04)
        
        # Depth constraint visualization (depth mask + anomaly region)
        if depth_map is not None:
            depth_single = depth_map[:, :, 0] if len(depth_map.shape) == 3 else depth_map
            depth_threshold = 0.001  # Use default threshold
            foreground_mask = depth_single > depth_threshold
            
            # Create composite display: blue=background, green=foreground, red=anomaly region
            composite = np.zeros((*depth_single.shape, 3))
            composite[~foreground_mask] = [0, 0, 1]  # Background as blue
            composite[foreground_mask] = [0, 1, 0]   # Foreground as green
            composite[anomaly_mask > 0] = [1, 0, 0]  # Anomaly region as red
            
            axes[1, 2].imshow(composite)
            axes[1, 2].set_title('Depth Constraint Visualization\n(Blue: Background, Green: Foreground, Red: Anomaly)', fontsize=10)
            axes[1, 2].axis('off')
        else:
            axes[1, 2].text(0.5, 0.5, 'No Depth Constraint Display', ha='center', va='center', transform=axes[1, 2].transAxes)
            axes[1, 2].axis('off')
        
        # Add statistical information
        info_text = f"""Generation Information:
Anomaly Coverage: {np.sum(anomaly_mask) / anomaly_mask.size * 100:.2f}%
Beta Value: {anomaly_result.get('beta', 'N/A')}
Texture Source: {Path(anomaly_result['texture_source']).name if anomaly_result['texture_source'] else 'None'}
Depth Mode: {depth_mode}"""
        
        if depth_map is not None:
            depth_single = depth_map[:, :, 0] if len(depth_map.shape) == 3 else depth_map
            foreground_ratio = np.sum(depth_single > 0.001) / depth_single.size * 100
            info_text += f"\nForeground Region Ratio: {foreground_ratio:.1f}%"
        
        fig.text(0.02, 0.02, info_text, fontsize=10, verticalalignment='bottom',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        
        # 保存图像
        save_path = self.output_dir / f"{save_name}.png"
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"可视化结果已保存: {save_path.absolute()}")
        
        return str(save_path)
    
    def save_individual_images(
        self, 
        original_albedo: np.ndarray, 
        anomaly_result: dict, 
        save_prefix: str = None
    ) -> dict:
        """
        保存单独的图像文件
        
        Args:
            original_albedo: 原始albedo图像
            anomaly_result: 异常生成结果
            save_prefix: 保存文件名前缀
            
        Returns:
            保存的文件路径字典
        """
        if save_prefix is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            save_prefix = f"anomaly_{timestamp}"
        
        saved_files = {}
        
        # 保存原始albedo
        original_path = self.output_dir / f"{save_prefix}_original.png"
        original_uint8 = (original_albedo * 255).astype(np.uint8)
        original_bgr = cv2.cvtColor(original_uint8, cv2.COLOR_RGB2BGR)
        cv2.imwrite(str(original_path), original_bgr)
        saved_files['original'] = str(original_path)
        
        # 保存异常albedo
        anomaly_path = self.output_dir / f"{save_prefix}_anomaly.png"
        anomaly_uint8 = (anomaly_result['augmented_albedo'] * 255).astype(np.uint8)
        anomaly_bgr = cv2.cvtColor(anomaly_uint8, cv2.COLOR_RGB2BGR)
        cv2.imwrite(str(anomaly_path), anomaly_bgr)
        saved_files['anomaly'] = str(anomaly_path)
        
        # 保存异常掩码
        mask_path = self.output_dir / f"{save_prefix}_mask.png"
        mask_uint8 = (anomaly_result['anomaly_mask'] * 255).astype(np.uint8)
        cv2.imwrite(str(mask_path), mask_uint8)
        saved_files['mask'] = str(mask_path)
        
        # 保存元数据
        metadata = {
            'has_anomaly': bool(anomaly_result['has_anomaly'][0]),
            'anomaly_coverage': float(np.sum(anomaly_result['anomaly_mask']) / anomaly_result['anomaly_mask'].size),
            'beta': float(anomaly_result.get('beta', 0)),
            'texture_source': anomaly_result['texture_source'],
            'generation_time': datetime.now().isoformat(),
            'config_preset': self.config_preset
        }
        
        metadata_path = self.output_dir / f"{save_prefix}_metadata.json"
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        saved_files['metadata'] = str(metadata_path)
        
        print(f"已保存单独图像文件:")
        for key, path in saved_files.items():
            print(f"  - {key}: {Path(path).name}")
        
        return saved_files
    
    def test_single_image(
        self, 
        albedo_path: str, 
        texture_source_path: str = None,
        category: str = "test",
        depth_mode: str = "realistic",
        depth_path: str = None,
        save_visualization: bool = True,
        save_individual: bool = True
    ) -> dict:
        """
        Test anomaly generation for single image
        
        Args:
            albedo_path: albedo image path
            texture_source_path: texture source path
            category: product category
            depth_mode: depth map mode ("realistic", "foreground", "uniform")
            depth_path: real depth map file path (.tiff format, optional)
            save_visualization: whether to save visualization comparison
            save_individual: whether to save individual images
            
        Returns:
            test result dictionary
        """
        print("="*60)
        print("Starting Material Anomaly Generation Test")
        print("="*60)
        
        try:
            # 1. Setup system
            self.setup_system(texture_source_path)
            
            # 2. Load albedo image
            albedo_image = self.load_albedo_image(albedo_path)
            
            # 3. Load or create depth map
            if depth_path and Path(depth_path).exists():
                depth_map = self.load_real_depth_map(depth_path)
                print(f"Using real depth map: {Path(depth_path).name}")
            else:
                depth_map = self.create_default_depth_map(albedo_image.shape, depth_mode)
                if depth_path:
                    print(f"Warning: Depth file not found: {depth_path}")
                print(f"Using synthetic depth map with mode: {depth_mode}")
            
            # 4. Generate anomaly
            anomaly_result = self.generate_anomaly(
                albedo_image, 
                depth_map=depth_map,
                category=category,
                depth_mode=depth_mode if not depth_path else "real"
            )
            
            # 4. 保存结果
            saved_files = {}
            
            if save_visualization:
                viz_path = self.visualize_results(albedo_image, anomaly_result)
                saved_files['visualization'] = viz_path
            
            if save_individual:
                individual_files = self.save_individual_images(albedo_image, anomaly_result)
                saved_files.update(individual_files)
            
            # 5. 返回测试结果
            test_result = {
                'success': True,
                'input_path': albedo_path,
                'anomaly_generated': bool(anomaly_result['has_anomaly'][0]),
                'anomaly_coverage': float(np.sum(anomaly_result['anomaly_mask']) / anomaly_result['anomaly_mask'].size),
                'beta_value': float(anomaly_result.get('beta', 0)),
                'texture_source': anomaly_result['texture_source'],
                'depth_mode': depth_mode,
                'saved_files': saved_files,
                'output_directory': str(self.output_dir.absolute())
            }
            
            print("="*60)
            print("测试完成！")
            print(f"输出目录: {self.output_dir.absolute()}")
            print("="*60)
            
            return test_result
            
        except Exception as e:
            error_result = {
                'success': False,
                'error': str(e),
                'input_path': albedo_path
            }
            
            print(f"测试失败: {e}")
            return error_result


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="Test material anomaly generation")
    parser.add_argument("--albedo_path", type=str, required=True, 
                       help="Input albedo image path")
    parser.add_argument("--texture_path", type=str, default="/raid/liulinna/projects/EasyNet/datasets/dtd/images/",
                       help="Texture source path (optional, use default textures if not provided)")
    parser.add_argument("--category", type=str, default="test", 
                       help="Product category")
    parser.add_argument("--depth_path", type=str,
                       help="Real depth map file path (.tiff format, optional)")
    parser.add_argument("--depth_mode", type=str, default="realistic",
                       choices=["realistic", "foreground", "uniform"],
                       help="Depth map mode: realistic(real object), foreground(foreground region), uniform(uniform depth)")
    parser.add_argument("--config", type=str, default="easynet_default",
                       choices=["easynet_default", "high_quality", "fast_generation", "debug_mode"],
                       help="Configuration preset")
    parser.add_argument("--no_viz", action="store_true",
                       help="Do not save visualization comparison images")
    parser.add_argument("--no_individual", action="store_true",
                       help="Do not save individual image files")
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not Path(args.albedo_path).exists():
        print(f"错误: 输入文件不存在: {args.albedo_path}")
        return 1
    
    # 创建测试器
    tester = MaterialAnomalyTester(config_preset=args.config)
    
    # Execute test
    result = tester.test_single_image(
        albedo_path=args.albedo_path,
        texture_source_path=args.texture_path,
        category=args.category,
        depth_mode=args.depth_mode,
        depth_path=args.depth_path,
        save_visualization=not args.no_viz,
        save_individual=not args.no_individual
    )
    
    # 输出结果
    if result['success']:
        print("\n✅ 测试成功完成!")
        print(f"异常生成: {'是' if result['anomaly_generated'] else '否'}")
        print(f"异常覆盖率: {result['anomaly_coverage']*100:.2f}%")
        print(f"深度模式: {result['depth_mode']}")
        print(f"输出目录: {result['output_directory']}")
        return 0
    else:
        print(f"\n❌ 测试失败: {result['error']}")
        return 1


if __name__ == "__main__":
    exit(main())