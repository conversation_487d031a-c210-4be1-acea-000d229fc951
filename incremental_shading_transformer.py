#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增量式Shading Transformation实现
严格按照用户描述的逻辑：在真实光照环境中注入局部扰动

Author: IA-CRF Project
Date: 2025-08-29
"""

import numpy as np
from typing import List, Optional, Dict, Tuple
from scipy.ndimage import gaussian_filter, binary_dilation


class IncrementalShadingTransformer:
    """
    增量式阴影变换器
    核心目标：为已经在3D点云上"雕刻"好的几何缺陷，计算并"渲染"出它在2D着色图上应有的、物理上正确的光影变化
    """
    
    def __init__(self, light_direction=[0, 0, 1], ambient_light=0.3, diffuse_strength=0.7):
        """
        初始化增量式阴影变换器
        
        Args:
            light_direction: 虚拟光源方向（默认从正上方）
            ambient_light: 环境光强度
            diffuse_strength: 漫反射光强度
        """
        self.light_direction = np.array(light_direction, dtype=np.float32)
        self.light_direction = self.light_direction / np.linalg.norm(self.light_direction)
        self.ambient_light = ambient_light
        self.diffuse_strength = diffuse_strength
        
        # IncrementalShadingTransformer initialized
    
    def apply_incremental_shading_transformation(
        self,
        clean_shading_map: np.ndarray,  # S_d - 原始干净的着色图
        original_pcd: np.ndarray,       # D - 原始干净的点云 [H, W, 3]
        anomalous_pcd: np.ndarray,      # D' - 带有几何缺陷的点云 [H, W, 3]
        patch_indices: List[Tuple[int, int]],  # 被修改点的索引列表
        original_normals: Optional[np.ndarray] = None  # 预计算的原始法线 [H, W, 3]
    ) -> Dict:
        """
        执行增量式阴影变换的完整流程
        
        Args:
            clean_shading_map: 原始干净的着色图 [H, W, 3]，值域[0,1]
            original_pcd: 原始点云 [H, W, 3]
            anomalous_pcd: 异常点云 [H, W, 3]
            patch_indices: 被修改点的(i,j)索引列表
            original_normals: 可选的预计算原始法线
            
        Returns:
            dict: 包含anomalous_shading_map和变换统计信息
        """
        H, W, _ = original_pcd.shape
        # 开始增量式阴影变换
        
        # 第一步：更新受影响区域的表面法线
        updated_normals = self._update_affected_normals(
            original_pcd, anomalous_pcd, patch_indices, original_normals
        )
        
        # 第二步：计算局部光照强度的变化
        lighting_ratios = self._calculate_lighting_intensity_changes(
            original_pcd, anomalous_pcd, patch_indices, 
            original_normals, updated_normals
        )
        
        # 第三步：将光照变化应用到2D着色图
        anomalous_shading_map = self._apply_lighting_changes_to_shading(
            clean_shading_map, lighting_ratios, patch_indices
        )
        
        # 第四步：可选的平滑处理
        anomalous_shading_map = self._apply_edge_smoothing(
            clean_shading_map, anomalous_shading_map, patch_indices
        )
        
        # 计算变换统计
        stats = self._calculate_transformation_statistics(
            clean_shading_map, anomalous_shading_map, patch_indices
        )
        
        # 增量式阴影变换完成
        
        return {
            'anomalous_shading_map': anomalous_shading_map,
            'lighting_ratios': lighting_ratios,
            'updated_normals': updated_normals,
            'transformation_stats': stats
        }
    
    def _update_affected_normals(
        self,
        original_pcd: np.ndarray,
        anomalous_pcd: np.ndarray,
        patch_indices: List[Tuple[int, int]],
        original_normals: Optional[np.ndarray] = None
    ) -> np.ndarray:
        """
        第一步：更新受影响区域的表面法线
        
        Args:
            original_pcd: 原始点云 [H, W, 3]
            anomalous_pcd: 异常点云 [H, W, 3]
            patch_indices: 被修改点的索引
            original_normals: 可选的原始法线
            
        Returns:
            更新后的法线 [H, W, 3]
        """
        H, W, _ = original_pcd.shape
        
        # 如果有预计算的原始法线，使用它们作为基础
        if original_normals is not None:
            updated_normals = original_normals.copy()
        else:
            # 计算原始点云的法线
            updated_normals = self._estimate_surface_normals(original_pcd)
        
        # 重新计算受影响点的法线
        
        # 对每个被修改的点，重新计算其局部表面法线
        recalculated_count = 0
        for i, j in patch_indices:
            if 0 <= i < H and 0 <= j < W:
                # 为这个点重新计算法线
                new_normal = self._calculate_local_normal(
                    anomalous_pcd, i, j, neighborhood_size=3
                )
                
                if new_normal is not None:
                    updated_normals[i, j] = new_normal
                    recalculated_count += 1
        
        # 成功重新计算法线
        return updated_normals
    
    def _calculate_local_normal(
        self, 
        pointcloud: np.ndarray, 
        i: int, j: int, 
        neighborhood_size: int = 3
    ) -> Optional[np.ndarray]:
        """
        计算单个点的局部表面法线
        
        Args:
            pointcloud: 点云 [H, W, 3]
            i, j: 点的坐标
            neighborhood_size: 邻域大小
            
        Returns:
            法线向量 [3] 或 None
        """
        H, W, _ = pointcloud.shape
        
        # 收集邻域点
        neighbors = []
        half_size = neighborhood_size // 2
        
        for di in range(-half_size, half_size + 1):
            for dj in range(-half_size, half_size + 1):
                ni, nj = i + di, j + dj
                if 0 <= ni < H and 0 <= nj < W:
                    point = pointcloud[ni, nj]
                    # 过滤掉无效点
                    if np.linalg.norm(point) > 1e-6:
                        neighbors.append(point)
        
        if len(neighbors) < 3:
            return None
        
        # 使用PCA计算法线
        neighbors = np.array(neighbors)
        centroid = np.mean(neighbors, axis=0)
        centered = neighbors - centroid
        
        # 计算协方差矩阵
        cov_matrix = np.cov(centered.T)
        
        # 获取最小特征值对应的特征向量
        eigenvalues, eigenvectors = np.linalg.eigh(cov_matrix)
        normal = eigenvectors[:, 0]  # 最小特征值对应的特征向量
        
        # 确保法线朝向一致（朝外）
        if normal[2] < 0:  # 假设Z轴向上
            normal = -normal
        
        return normal / np.linalg.norm(normal)
    
    def _calculate_lighting_intensity_changes(
        self,
        original_pcd: np.ndarray,
        anomalous_pcd: np.ndarray,
        patch_indices: List[Tuple[int, int]],
        original_normals: np.ndarray,
        updated_normals: np.ndarray
    ) -> np.ndarray:
        """
        第二步：计算局部光照强度的变化
        使用朗伯余弦定律计算光照变化比例
        
        Args:
            original_pcd: 原始点云
            anomalous_pcd: 异常点云
            patch_indices: 被修改点的索引
            original_normals: 原始法线
            updated_normals: 更新后的法线
            
        Returns:
            光照变化比例图 [H, W]，1.0表示无变化，<1.0表示变暗，>1.0表示变亮
        """
        H, W, _ = original_pcd.shape
        lighting_ratios = np.ones((H, W), dtype=np.float32)  # 默认无变化
        
        # 使用朗伯余弦定律计算光照变化
        
        epsilon = 1e-9  # 防止除零
        significant_changes = 0
        
        for i, j in patch_indices:
            if 0 <= i < H and 0 <= j < W:
                # 计算原始光照因子
                original_normal = original_normals[i, j]
                intensity_old = max(0, np.dot(original_normal, self.light_direction))
                
                # 计算新的光照因子
                new_normal = updated_normals[i, j]
                intensity_new = max(0, np.dot(new_normal, self.light_direction))
                
                # 计算变化比例
                if intensity_old > epsilon:
                    ratio = intensity_new / intensity_old
                else:
                    # 如果原始强度接近0，使用新强度作为基准
                    ratio = 1.0 + intensity_new
                
                # 限制变化范围，避免极端值
                ratio = np.clip(ratio, 0.1, 3.0)
                
                lighting_ratios[i, j] = ratio
                
                # 统计显著变化
                if abs(ratio - 1.0) > 0.05:
                    significant_changes += 1
        
        # 检测显著光照变化
        
        # 计算统计信息
        changed_mask = lighting_ratios != 1.0
        
        return lighting_ratios
    
    def _apply_lighting_changes_to_shading(
        self,
        clean_shading_map: np.ndarray,
        lighting_ratios: np.ndarray,
        patch_indices: List[Tuple[int, int]]
    ) -> np.ndarray:
        """
        第三步：将光照变化应用到2D着色图
        严格按照"增量修改"原则：只修改受影响的像素
        
        Args:
            clean_shading_map: 原始干净的着色图 [H, W, 3]
            lighting_ratios: 光照变化比例 [H, W]
            patch_indices: 被修改点的索引
            
        Returns:
            修改后的着色图 [H, W, 3]
        """
        # 创建原始着色图的副本作为"底图"
        anomalous_shading_map = clean_shading_map.copy()
        
        # 在像素位置应用光照变化
        
        modified_pixels = 0
        total_change = 0.0
        
        for i, j in patch_indices:
            H, W, _ = clean_shading_map.shape
            if 0 <= i < H and 0 <= j < W:
                # 获取原始像素值和光照比例
                original_pixel = clean_shading_map[i, j]
                ratio = lighting_ratios[i, j]
                
                # 应用光照变化：anomalous_shading_map[v, u] = original * ratio_i
                new_pixel = original_pixel * ratio
                
                # 确保像素值在有效范围内
                new_pixel = np.clip(new_pixel, 0, 1)
                
                # 应用变化
                anomalous_shading_map[i, j] = new_pixel
                
                # 统计变化
                pixel_change = np.sum(np.abs(new_pixel - original_pixel))
                total_change += pixel_change
                modified_pixels += 1
        
        # 成功修改像素
        
        return anomalous_shading_map
    
    def _apply_edge_smoothing(
        self,
        clean_shading_map: np.ndarray,
        anomalous_shading_map: np.ndarray,
        patch_indices: List[Tuple[int, int]],
        blur_sigma: float = 1.0
    ) -> np.ndarray:
        """
        第四步：对被修改像素区域应用轻微的高斯模糊
        使生成的阴影边缘更自然
        
        Args:
            clean_shading_map: 原始着色图
            anomalous_shading_map: 修改后的着色图
            patch_indices: 被修改的像素索引
            blur_sigma: 高斯模糊的sigma值
            
        Returns:
            平滑后的着色图
        """
        if len(patch_indices) == 0:
            return anomalous_shading_map
        
        H, W, C = anomalous_shading_map.shape
        
        # 创建修改区域的掩码
        modified_mask = np.zeros((H, W), dtype=bool)
        for i, j in patch_indices:
            if 0 <= i < H and 0 <= j < W:
                modified_mask[i, j] = True
        
        # 扩展掩码以包含边界区域
        extended_mask = binary_dilation(modified_mask, iterations=2)
        
        # 对像素应用边缘平滑
        
        smoothed_shading = anomalous_shading_map.copy()
        
        # 对每个颜色通道应用高斯模糊
        for c in range(C):
            channel = anomalous_shading_map[:, :, c]
            smoothed_channel = gaussian_filter(channel, sigma=blur_sigma)
            
            # 只在扩展的修改区域应用平滑
            smoothed_shading[extended_mask, c] = smoothed_channel[extended_mask]
        
        return smoothed_shading
    
    def _estimate_surface_normals(self, pointcloud: np.ndarray) -> np.ndarray:
        """
        估计点云的表面法线
        
        Args:
            pointcloud: 点云 [H, W, 3]
            
        Returns:
            法线 [H, W, 3]
        """
        H, W, _ = pointcloud.shape
        normals = np.zeros((H, W, 3))
        
        # 使用梯度估计法线
        for i in range(1, H-1):
            for j in range(1, W-1):
                # 计算局部梯度
                dx = pointcloud[i, j+1] - pointcloud[i, j-1]
                dy = pointcloud[i+1, j] - pointcloud[i-1, j]
                
                # 叉积得到法线
                normal = np.cross(dx, dy)
                norm = np.linalg.norm(normal)
                
                if norm > 1e-6:
                    normals[i, j] = normal / norm
                else:
                    normals[i, j] = [0, 0, 1]  # 默认向上
        
        # 处理边界
        normals[0, :] = normals[1, :]
        normals[-1, :] = normals[-2, :]
        normals[:, 0] = normals[:, 1]
        normals[:, -1] = normals[:, -2]
        
        return normals
    
    def _calculate_transformation_statistics(
        self,
        clean_shading_map: np.ndarray,
        anomalous_shading_map: np.ndarray,
        patch_indices: List[Tuple[int, int]]
    ) -> Dict:
        """
        计算变换统计信息
        
        Args:
            clean_shading_map: 原始着色图
            anomalous_shading_map: 异常着色图
            patch_indices: 修改的像素索引
            
        Returns:
            统计信息字典
        """
        # 计算总体差异
        diff_map = np.abs(anomalous_shading_map - clean_shading_map)
        total_change = np.sum(diff_map)
        max_change = np.max(diff_map)
        mean_change = np.mean(diff_map[diff_map > 0]) if np.any(diff_map > 0) else 0
        
        # 计算修改区域的统计
        H, W, _ = clean_shading_map.shape
        modified_mask = np.zeros((H, W), dtype=bool)
        for i, j in patch_indices:
            if 0 <= i < H and 0 <= j < W:
                modified_mask[i, j] = True
        
        modified_area_change = np.sum(diff_map[modified_mask]) if np.sum(modified_mask) > 0 else 0
        
        return {
            'total_change': total_change,
            'max_change': max_change,
            'mean_change': mean_change,
            'total_changed_pixels': len(patch_indices),
            'modified_area_change': modified_area_change,
            'change_concentration': modified_area_change / max(total_change, 1e-9)
        }