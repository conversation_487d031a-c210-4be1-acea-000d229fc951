import os
import glob
from tqdm import tqdm
import numpy as np
from PIL import Image

from chrislib.data_util import load_image
from chrislib.general import view, invert
from intrinsic.pipeline import load_models, run_pipeline

# 设置路径
root_dir = "datasets/eyecandies_preprocessed"
categories = ["CandyCane", "ChocolateCookie", "ChocolatePraline", "Confetto", "GummyBear", "HazelnutTruffle", "LicoriceSandwich", "Lollipop", "Marshmallow", "PeppermintCandy"]

# 加载模型
models = load_models('v2.1')

# 保存图像的辅助函数
def save_image(img_array, save_path):
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    img_uint8 = (np.clip(img_array, 0, 1) * 255).astype(np.uint8)
    Image.fromarray(img_uint8).save(save_path)

# 遍历图像
for category in categories:
    category_path = os.path.join(root_dir, category)
    for phase in ['train', 'test']:
        phase_path = os.path.join(category_path, phase)
        if not os.path.exists(phase_path):
            continue

        for anomaly_type in os.listdir(phase_path):
            rgb_dir = os.path.join(phase_path, anomaly_type, 'rgb')
            if not os.path.exists(rgb_dir):
                continue

            image_paths = glob.glob(os.path.join(rgb_dir, '*.png'))

            for img_path in tqdm(image_paths, desc=f"{category}/{phase}/{anomaly_type}"):
                try:
                    image = load_image(img_path)
                    results = run_pipeline(models, image, device='cuda')

                    img_name = os.path.basename(img_path)

                    # 准备保存路径
                    alb_dir = os.path.join(rgb_dir, 'albedo')
                    shd_dir = os.path.join(rgb_dir, 'shading')
                    res_dir = os.path.join(rgb_dir, 'residual')

                    # 提取并保存各个组件
                    alb = view(results['hr_alb'])
                    shd = 1 - invert(results['dif_shd'])
                    res = results['residual']

                    save_image(alb, os.path.join(alb_dir, img_name))
                    save_image(shd, os.path.join(shd_dir, img_name))
                    save_image(res, os.path.join(res_dir, img_name))
                except Exception as e:
                    print(f"Error processing {img_path}: {e}")
