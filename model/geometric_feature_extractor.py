#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3D Geometric Feature Extractor Module (Optimized Version)
Specialized for extracting multi-scale geometric features from 3D point clouds
Optimized for performance with parallel processing and algorithm improvements
"""

import os
import numpy as np
import open3d as o3d
import tifffile as tiff
from scipy.spatial import cKDTree
from sklearn.decomposition import PCA
from concurrent.futures import ThreadPoolExecutor
import time
import warnings
warnings.filterwarnings('ignore')

class GeometricFeatureExtractor:
    """
    3D Geometric Feature Extractor (Optimized)
    Extracts multi-scale geometric features from organized point clouds with performance optimizations
    """
    
    def __init__(self, config):
        self.config = config
        
        # Get scale selection parameters from config, default to 1 scale for speed
        self.num_scales = config.get('num_scales', 1)
        if self.num_scales not in [1, 2, 3]:
            raise ValueError("num_scales must be 1, 2, or 3")
        
        # Set multi-scale parameters based on selected scale count
        # 增加半径以适应224x224的重采样点云
        all_scale_params = [
            {'radius': 0.08, 'max_nn': 30},   # Fine scale - 增加半径
            {'radius': 0.15, 'max_nn': 50},   # Medium scale - 增加半径
            {'radius': 0.25, 'max_nn': 80},   # Coarse scale - 增加半径
        ]
        
        # Select corresponding number of scales based on num_scales
        self.multi_scale_params = all_scale_params[:self.num_scales]
        
        # Performance optimization settings
        self.use_parallel_processing = config.get('use_parallel_processing', True)
        self.num_threads = config.get('num_threads', 4)
        self.batch_size = config.get('batch_size', 1000)
        self.use_fast_normals = config.get('use_fast_normals', True)
        
        # Only show minimal info in non-verbose mode
        if config.get('verbose', False):
            print(f"Using {self.num_scales} scales for multi-scale 3D geometric feature extraction")
            for i, params in enumerate(self.multi_scale_params):
                print(f"Scale {i+1}: radius={params['radius']}, max_nn={params['max_nn']}")
            print(f"Parallel processing: {self.use_parallel_processing}, Threads: {self.num_threads}")
    
    def load_and_preprocess_pointcloud(self, tiff_path, target_size=(224, 224)):
        """
        从.tiff文件加载3D点云并预处理到指定尺寸
        """
        # 读取tiff文件
        xyz_data = tiff.imread(tiff_path)
        
        # Resize到目标尺寸
        xyz_resized = self._resize_organized_pc(xyz_data, target_size)
        
        return xyz_resized
    
    def _resize_organized_pc(self, organized_pc, target_size=(224, 224)):
        """
        将有组织点云resize到目标尺寸
        """
        import torch
        torch_organized_pc = torch.tensor(organized_pc).permute(2, 0, 1).unsqueeze(dim=0).contiguous().float()
        torch_resized_organized_pc = torch.nn.functional.interpolate(
            torch_organized_pc, 
            size=target_size,
            mode='nearest'
        )
        return torch_resized_organized_pc.squeeze(dim=0).permute(1, 2, 0).contiguous().numpy()
    
    def extract_3d_geometric_features(self, organized_pointcloud):
        """
        3D geometric feature extraction: compute multi-scale normals, curvature and other geometric features (Optimized)
        Input: organized_pointcloud [H, W, 3] numpy array
        Output: enhanced_features [H*W, D] where D contains position + multi-scale normals + multi-scale curvature features
        """
        start_time = time.time()
        H, W, _ = organized_pointcloud.shape
        
        # Build KD-tree spatial index with optimization
        pcd, valid_mask, point_coords = self._build_kdtree_index_optimized(organized_pointcloud)
        
        if pcd is None or len(point_coords) < 10:
            # If no valid points, return zero features
            # Feature dimensions: 3(xyz) + num_scales*4(3normals+1curvature)
            feature_dim = 3 + len(self.multi_scale_params) * 4
            return np.zeros((H*W, feature_dim))
        
        # Multi-scale surface normal estimation (optimized)
        multi_scale_normals = self._estimate_multiscale_normals_optimized(pcd, point_coords)
        
        # Multi-scale surface curvature estimation (optimized)
        multi_scale_curvatures = self._estimate_multiscale_curvatures_optimized(point_coords)
        
        # Build enhanced feature matrix
        enhanced_features = self._build_feature_matrix_optimized(
            organized_pointcloud, valid_mask, point_coords, 
            multi_scale_normals, multi_scale_curvatures, H, W
        )
        
        processing_time = time.time() - start_time
        if self.config.get('verbose', False):
            print(f"  Feature extraction completed in {processing_time:.3f}s")
        
        return enhanced_features
    
    def _build_kdtree_index_optimized(self, organized_pointcloud):
        """
        Step 1: Convert numpy array to Open3D format and build KD-tree (Optimized)
        """
        H, W, _ = organized_pointcloud.shape
        
        # Convert organized point cloud to unorganized point cloud
        points = organized_pointcloud.reshape(-1, 3)
        
        # Filter out invalid points (typically Z=0 points) with optimized condition
        valid_mask = (points[:, 2] > 1e-6) & (np.sum(np.abs(points), axis=1) > 1e-6)
        valid_points = points[valid_mask]
        
        if len(valid_points) < 10:
            return None, valid_mask, None
        
        # Create Open3D point cloud object with optimized memory allocation
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(valid_points)
        
        return pcd, valid_mask, valid_points
    
    def _estimate_multiscale_normals_optimized(self, pcd, point_coords):
        """
        Step 2: Multi-scale surface normal estimation (Optimized)
        """
        multi_scale_normals = []
        
        for scale_params in self.multi_scale_params:
            if self.use_fast_normals:
                # Use optimized normal estimation
                normals = self._fast_normal_estimation(pcd, scale_params)
            else:
                # Use original Open3D estimation
                pcd_copy = o3d.geometry.PointCloud(pcd)
                pcd_copy.estimate_normals(
                    search_param=o3d.geometry.KDTreeSearchParamHybrid(
                        radius=scale_params['radius'], 
                        max_nn=scale_params['max_nn']
                    )
                )
                
                # Normal orientation consistency processing
                pcd_copy.orient_normals_consistent_tangent_plane(50)  # Reduced for speed
                normals = np.asarray(pcd_copy.normals)
            
            # Apply improved normal orientation consistency
            normals = self._improve_normal_orientation_consistency(normals, point_coords)
            
            multi_scale_normals.append(normals)
        
        return multi_scale_normals
    
    def _improve_normal_orientation_consistency(self, normals, points):
        """
        改进的法线方向一致性处理 - 专门为3D扫描数据优化
        确保法线方向朝向扫描仪方向（外部），适合从外部角度扫描的数据
        """
        if len(normals) == 0:
            return normals
        
        improved_normals = normals.copy()
        
        # 1. 确保所有法线都是单位向量
        norms = np.linalg.norm(improved_normals, axis=1, keepdims=True)
        norms = np.maximum(norms, 1e-8)  # 避免除零
        improved_normals = improved_normals / norms
        
        # 2. 为3D扫描数据估计扫描仪位置
        if len(points) > 0:
            # 计算点云边界框和几何中心
            min_coords = np.min(points, axis=0)
            max_coords = np.max(points, axis=0)
            center = (min_coords + max_coords) / 2
            extent = max_coords - min_coords
            
            # 为3D扫描数据估计扫描仪位置
            # 扫描仪通常位于物体前方或上方，距离物体一定距离
            max_extent = np.max(extent)
            
            # 尝试多个可能的扫描仪位置
            possible_scanner_positions = [
                center + np.array([0, 0, max_extent * 2.0]),      # 上方
                center + np.array([0, max_extent * 2.0, 0]),      # 前方
                center + np.array([max_extent * 1.5, 0, max_extent * 1.5]),  # 右上方
                center + np.array([-max_extent * 1.5, 0, max_extent * 1.5]), # 左上方
            ]
            
            best_consistency_score = -1
            best_scanner_position = possible_scanner_positions[0]
            
            # 选择最佳扫描仪位置（使法线最一致地指向该位置）
            for scanner_pos in possible_scanner_positions:
                # 计算从每个点到扫描仪的方向
                scanner_directions = scanner_pos[np.newaxis, :] - points
                scanner_directions = scanner_directions / (np.linalg.norm(scanner_directions, axis=1, keepdims=True) + 1e-8)
                
                # 计算法线与扫描仪方向的一致性
                dot_products = np.sum(improved_normals * scanner_directions, axis=1)
                consistency_score = np.mean(np.abs(dot_products))  # 平均一致性分数
                
                if consistency_score > best_consistency_score:
                    best_consistency_score = consistency_score
                    best_scanner_position = scanner_pos
            
            # 3. 使用最佳扫描仪位置调整法线方向
            scanner_directions = best_scanner_position[np.newaxis, :] - points
            scanner_directions = scanner_directions / (np.linalg.norm(scanner_directions, axis=1, keepdims=True) + 1e-8)
            
            # 确保法线朝向扫描仪方向（外部）
            dot_products = np.sum(improved_normals * scanner_directions, axis=1)
            
            # 如果法线与扫描仪方向夹角大于90度，则翻转法线
            flip_mask = dot_products < 0
            improved_normals[flip_mask] = -improved_normals[flip_mask]
            
            # 4. 全局一致性检查：确保大部分法线朝向同一方向
            # 计算法线的主要方向
            mean_normal = np.mean(improved_normals, axis=0)
            mean_normal = mean_normal / (np.linalg.norm(mean_normal) + 1e-8)
            
            # 对于与主要方向夹角大于90度的法线，进行翻转
            dot_with_mean = np.sum(improved_normals * mean_normal[np.newaxis, :], axis=1)
            global_flip_mask = dot_with_mean < -0.5  # 容忍一定的差异
            improved_normals[global_flip_mask] = -improved_normals[global_flip_mask]
        
        return improved_normals
    
    def _fast_normal_estimation(self, pcd, scale_params):
        """
        Fast normal estimation using PCA - 基于真实几何特征的法线估计
        使用正确的SVD/PCA方法计算表面法线，保持表面的真实几何特征
        """
        points = np.asarray(pcd.points)
        num_points = len(points)
        
        if num_points == 0:
            return np.array([])
        
        # 构建KD-tree用于邻域搜索
        kdtree = cKDTree(points)
        normals = np.zeros_like(points)
        
        # 向量化处理以提高效率
        for i, point in enumerate(points):
            # 在指定半径内查找邻居
            neighbors_indices = kdtree.query_ball_point(point, r=scale_params['radius'])
            
            # 如果邻居太少，无法进行PCA分析
            if len(neighbors_indices) < 4:  # 需要至少4个点才能进行稳定的平面拟合
                # 尝试用更大的半径或者K近邻
                distances, knn_indices = kdtree.query(point, k=min(10, num_points))
                if len(knn_indices) >= 4:
                    neighbors_indices = knn_indices
                else:
                    # 完全孤立的点，使用Z轴正方向
                    normals[i] = np.array([0.0, 0.0, 1.0])
                    continue
            
            # 限制邻居数量以控制计算复杂度
            if len(neighbors_indices) > scale_params['max_nn']:
                neighbors_indices = neighbors_indices[:scale_params['max_nn']]
            
            # 获取邻域点坐标
            neighbor_points = points[neighbors_indices]
            
            try:
                # 计算邻域点的质心并去中心化
                centroid = np.mean(neighbor_points, axis=0)
                centered_points = neighbor_points - centroid
                
                # 检查是否所有点都是重复的
                if np.allclose(centered_points, 0, atol=1e-8):
                    normals[i] = np.array([0.0, 0.0, 1.0])
                    continue
                
                # 使用SVD进行主成分分析
                # 对于 M x N 矩阵 A，SVD: A = U @ S @ Vt
                # 其中 Vt 的最后一行对应最小奇异值，即法线方向
                U, singular_values, Vt = np.linalg.svd(centered_points, full_matrices=False)
                
                # 检查奇异值是否有效
                if len(singular_values) < 3 or singular_values[-1] / singular_values[0] > 0.1:
                    # 如果最小奇异值不够小，说明不是一个好的平面
                    # 使用协方差矩阵的方法作为备选
                    cov_matrix = np.cov(centered_points.T)
                    eigenvalues, eigenvectors = np.linalg.eigh(cov_matrix)
                    # 最小特征值对应的特征向量是法线
                    normal = eigenvectors[:, 0]  # 第一列是最小特征值的特征向量
                else:
                    # 最小奇异值对应的右奇异向量就是法线
                    normal = Vt[-1, :]
                
                # 确保法线是单位向量
                norm = np.linalg.norm(normal)
                if norm > 1e-8:
                    normal = normal / norm
                else:
                    normal = np.array([0.0, 0.0, 1.0])
                
                normals[i] = normal
                
            except (np.linalg.LinAlgError, ValueError) as e:
                # SVD失败，使用备用方法
                normals[i] = np.array([0.0, 0.0, 1.0])
        
        return normals
    
    def _estimate_multiscale_curvatures_optimized(self, point_coords):
        """
        Step 3: Multi-scale surface curvature estimation (Optimized)
        """
        multi_scale_curvatures = []
        
        # Build KDTree once for all scales
        kdtree = cKDTree(point_coords)
        
        for scale_params in self.multi_scale_params:
            if self.use_parallel_processing and len(point_coords) > 500:
                # Use parallel processing for large point clouds
                curvatures = self._parallel_curvature_computation(point_coords, kdtree, scale_params)
            else:
                # Use sequential processing for small point clouds
                curvatures = self._sequential_curvature_computation(point_coords, kdtree, scale_params)
            
            multi_scale_curvatures.append(curvatures)
        
        return multi_scale_curvatures
    
    def _parallel_curvature_computation(self, point_coords, kdtree, scale_params):
        """
        Parallel curvature computation using ThreadPoolExecutor
        """
        def compute_curvature_batch(batch_indices):
            batch_curvatures = []
            for i in batch_indices:
                point = point_coords[i]
                neighbors = kdtree.query_ball_point(point, r=scale_params['radius'])
                
                if len(neighbors) < 6:
                    batch_curvatures.append(0.0)
                    continue
                
                # Limit neighbors for speed
                if len(neighbors) > scale_params['max_nn']:
                    neighbors = neighbors[:scale_params['max_nn']]
                
                neighbor_points = point_coords[neighbors]
                curvature = self._compute_curvature_pca_fast(neighbor_points)
                batch_curvatures.append(curvature)
            
            return batch_curvatures
        
        # Split work into batches
        num_points = len(point_coords)
        batch_size = max(1, num_points // (self.num_threads * 4))
        batches = [list(range(i, min(i + batch_size, num_points))) 
                  for i in range(0, num_points, batch_size)]
        
        # Process batches in parallel
        all_curvatures = []
        with ThreadPoolExecutor(max_workers=self.num_threads) as executor:
            results = executor.map(compute_curvature_batch, batches)
            for batch_result in results:
                all_curvatures.extend(batch_result)
        
        return np.array(all_curvatures)
    
    def _sequential_curvature_computation(self, point_coords, kdtree, scale_params):
        """
        Sequential curvature computation
        """
        curvatures = []
        
        for i, point in enumerate(point_coords):
            neighbors = kdtree.query_ball_point(point, r=scale_params['radius'])
            
            if len(neighbors) < 6:
                curvatures.append(0.0)
                continue
            
            # Limit neighbors for speed
            if len(neighbors) > scale_params['max_nn']:
                neighbors = neighbors[:scale_params['max_nn']]
            
            neighbor_points = point_coords[neighbors]
            curvature = self._compute_curvature_pca_fast(neighbor_points)
            curvatures.append(curvature)
        
        return np.array(curvatures)
    
    def _compute_curvature_pca_fast(self, neighbor_points):
        """
        改进的PCA曲率计算 - 基于主曲率的真实几何曲率
        计算高斯曲率和平均曲率的组合指标
        """
        if len(neighbor_points) < 4:
            return 0.0
        
        # 中心化邻域点
        centroid = np.mean(neighbor_points, axis=0)
        centered_points = neighbor_points - centroid
        
        try:
            # 使用SVD进行主成分分析
            U, singular_values, Vt = np.linalg.svd(centered_points, full_matrices=False)
            
            # 确保奇异值非负且排序
            singular_values = np.maximum(singular_values, 1e-10)
            singular_values = np.sort(singular_values)[::-1]  # 降序排列
            
            if len(singular_values) < 3:
                return 0.0
            
            # 计算归一化的奇异值
            s1, s2, s3 = singular_values[0], singular_values[1], singular_values[2]
            total_variance = np.sum(singular_values)
            
            if total_variance < 1e-8:
                return 0.0
            
            # 计算多种曲率指标
            # 1. 表面变化率：最小奇异值反映了法线方向的变化
            surface_variation = s3 / total_variance
            
            # 2. 几何变化率：前两个主方向的相对变化
            if s1 > 1e-8:
                directional_change = abs(s1 - s2) / s1
            else:
                directional_change = 0.0
            
            # 3. 局部平整度：三个方向的相对重要性
            if s1 > 1e-8:
                planarity = (s2 - s3) / s1
                sphericity = s3 / s1
            else:
                planarity = 0.0
                sphericity = 0.0
            
            # 组合曲率指标：结合表面变化和几何特征
            combined_curvature = (
                0.5 * surface_variation +          # 主要的表面变化
                0.3 * directional_change +         # 方向性变化
                0.2 * (1.0 - planarity)           # 非平面性
            )
            
            # 应用非线性映射增强曲率对比度
            enhanced_curvature = np.tanh(combined_curvature * 10.0) * combined_curvature
            
            return float(enhanced_curvature)
            
        except (np.linalg.LinAlgError, ValueError):
            return 0.0
    
    def _build_feature_matrix_optimized(self, original_pc, valid_mask, valid_points, 
                                       multi_scale_normals, multi_scale_curvatures, H, W):
        """
        Step 4: Build final enhanced feature matrix (Optimized)
        """
        total_points = H * W
        
        # Calculate feature dimensions
        # 3 (xyz coordinates) + 3*num_scales (normals) + 1*num_scales (curvature features)
        feature_dim = 3 + len(self.multi_scale_params) * 4  # 3 + num_scales * (3normals + 1curvature)
        
        # Initialize feature matrix with optimized memory allocation
        enhanced_features = np.zeros((total_points, feature_dim), dtype=np.float32)
        
        # Fill original coordinates
        enhanced_features[:, :3] = original_pc.reshape(-1, 3)
        
        if valid_points is not None and len(valid_points) > 0:
            # Current feature index
            feat_idx = 3
            
            # Fill multi-scale normal features
            for normals in multi_scale_normals:
                enhanced_features[valid_mask, feat_idx:feat_idx+3] = normals.astype(np.float32)
                feat_idx += 3
            
            # Fill multi-scale curvature features (1 curvature per scale)
            for curvatures in multi_scale_curvatures:
                enhanced_features[valid_mask, feat_idx] = curvatures.astype(np.float32)
                feat_idx += 1
        
        return enhanced_features
    
    def save_geometric_features(self, enhanced_features, save_path):
        """
        保存几何特征到本地
        """
        # 获取目录路径
        dir_path = os.path.dirname(save_path)
        
        # 只有当目录路径不为空时才创建目录
        if dir_path:
            os.makedirs(dir_path, exist_ok=True)
        
        np.save(save_path, enhanced_features)
    
    def __call__(self, pointcloud_path=None, organized_pointcloud=None):
        """
        主要调用接口
        输入: 
            - pointcloud_path: .tiff文件路径 或者
            - organized_pointcloud: 直接的有组织点云数据 [H, W, 3]
        输出: 
            - enhanced_pointcloud: 增强型3D几何特征
        """
        # 3D点云特征提取
        if pointcloud_path is not None:
            # 从文件加载
            organized_pc = self.load_and_preprocess_pointcloud(pointcloud_path)
        elif organized_pointcloud is not None:
            # 直接使用输入的点云
            organized_pc = organized_pointcloud
        else:
            raise ValueError("必须提供pointcloud_path或organized_pointcloud参数")
        
        # 提取3D几何特征
        enhanced_pointcloud = self.extract_3d_geometric_features(organized_pc)
        
        return {
            'enhanced_pointcloud': enhanced_pointcloud,
            'original_pointcloud': organized_pc
        }


def test_geometric_feature_extractor():
    """
    测试3D几何特征提取器
    """
    # 配置参数
    config = {
        'num_scales': 2  # 使用2个尺度
    }
    
    # 创建特征提取器实例
    extractor = GeometricFeatureExtractor(config)
    
    # 创建测试数据
    H, W = 224, 224
    organized_pc = np.random.rand(H, W, 3) * 2 - 1  # [-1, 1]范围的点云
    # 将部分点设为无效（模拟真实情况）
    mask = np.random.rand(H, W) > 0.2
    organized_pc[~mask] = 0
    
    # 测试几何特征提取
    print("开始测试3D几何特征提取...")
    results = extractor(organized_pointcloud=organized_pc)
    
    print("测试完成！")
    print(f"增强几何特征形状: {results['enhanced_pointcloud'].shape}")
    print(f"原始点云形状: {results['original_pointcloud'].shape}")
    
    # 保存特征
    save_path = "test_geometric_features.npy"
    extractor.save_geometric_features(results['enhanced_pointcloud'], save_path)
    
    return results


if __name__ == "__main__":
    test_geometric_feature_extractor()