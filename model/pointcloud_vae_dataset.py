#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Point Cloud VAE Dataset and DataLoader
Loads 3D point cloud data from MVTec 3D-AD dataset for VAE training
Includes data augmentation and normalization for robust training
"""

import os
import glob
import numpy as np
import tifffile as tiff
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any

import torch
from torch.utils.data import Dataset, DataLoader
import torch.nn.functional as F


class PointCloudAugmentation:
    """
    Point cloud data augmentation for VAE training
    """
    def __init__(self, config: Dict):
        """
        Args:
            config: Augmentation configuration dictionary
        """
        self.config = config
        self.enable_rotation = config.get('enable_rotation', True)
        self.enable_scaling = config.get('enable_scaling', True)
        self.enable_jittering = config.get('enable_jittering', True)
        self.enable_translation = config.get('enable_translation', False)
        
        # Rotation parameters
        self.rotation_range = config.get('rotation_range', {'x': 15, 'y': 15, 'z': 360})
        
        # Scaling parameters
        self.scaling_range = config.get('scaling_range', [0.9, 1.1])
        
        # Jittering parameters
        self.jitter_std = config.get('jitter_std', 0.01)
        self.jitter_clip = config.get('jitter_clip', 0.02)
        
        # Translation parameters
        self.translation_range = config.get('translation_range', 0.1)
    
    def random_rotation(self, points: np.ndarray) -> np.ndarray:
        """Apply random rotation to point cloud"""
        if not self.enable_rotation:
            return points
            
        # Random angles in degrees
        angle_x = np.random.uniform(-self.rotation_range['x'], self.rotation_range['x']) * np.pi / 180
        angle_y = np.random.uniform(-self.rotation_range['y'], self.rotation_range['y']) * np.pi / 180
        angle_z = np.random.uniform(-self.rotation_range['z'], self.rotation_range['z']) * np.pi / 180
        
        # Rotation matrices
        Rx = np.array([[1, 0, 0],
                       [0, np.cos(angle_x), -np.sin(angle_x)],
                       [0, np.sin(angle_x), np.cos(angle_x)]])
        
        Ry = np.array([[np.cos(angle_y), 0, np.sin(angle_y)],
                       [0, 1, 0],
                       [-np.sin(angle_y), 0, np.cos(angle_y)]])
        
        Rz = np.array([[np.cos(angle_z), -np.sin(angle_z), 0],
                       [np.sin(angle_z), np.cos(angle_z), 0],
                       [0, 0, 1]])
        
        # Combined rotation
        R = np.dot(Rz, np.dot(Ry, Rx))
        
        return np.dot(points, R.T)
    
    def random_scaling(self, points: np.ndarray) -> np.ndarray:
        """Apply random scaling to point cloud"""
        if not self.enable_scaling:
            return points
            
        scale = np.random.uniform(self.scaling_range[0], self.scaling_range[1])
        return points * scale
    
    def random_jittering(self, points: np.ndarray) -> np.ndarray:
        """Apply random jittering (noise) to point cloud"""
        if not self.enable_jittering:
            return points
            
        noise = np.random.normal(0, self.jitter_std, points.shape)
        noise = np.clip(noise, -self.jitter_clip, self.jitter_clip)
        
        return points + noise
    
    def random_translation(self, points: np.ndarray) -> np.ndarray:
        """Apply random translation to point cloud"""
        if not self.enable_translation:
            return points
            
        translation = np.random.uniform(-self.translation_range, 
                                      self.translation_range, 
                                      size=(1, 3))
        
        return points + translation
    
    def __call__(self, points: np.ndarray) -> np.ndarray:
        """Apply augmentation pipeline to point cloud"""
        points = self.random_rotation(points)
        points = self.random_scaling(points)
        points = self.random_jittering(points)
        points = self.random_translation(points)
        
        return points


class PointCloudNormalizer:
    """
    Point cloud normalization for consistent training
    """
    def __init__(self, method: str = 'unit_sphere'):
        """
        Args:
            method: Normalization method ('unit_sphere', 'zero_center', 'unit_cube')
        """
        self.method = method
    
    def normalize(self, points: np.ndarray) -> np.ndarray:
        """
        Normalize point cloud
        
        Args:
            points: [N, 3] point cloud
            
        Returns:
            normalized_points: [N, 3] normalized point cloud
        """
        if self.method == 'unit_sphere':
            # Center and scale to unit sphere
            centroid = np.mean(points, axis=0, keepdims=True)
            points_centered = points - centroid
            
            # Scale to fit in unit sphere
            max_distance = np.max(np.linalg.norm(points_centered, axis=1))
            if max_distance > 0:
                points_normalized = points_centered / max_distance
            else:
                points_normalized = points_centered
                
        elif self.method == 'zero_center':
            # Only center at origin
            centroid = np.mean(points, axis=0, keepdims=True)
            points_normalized = points - centroid
            
        elif self.method == 'unit_cube':
            # Normalize to [-1, 1] cube
            min_coords = np.min(points, axis=0, keepdims=True)
            max_coords = np.max(points, axis=0, keepdims=True)
            
            center = (min_coords + max_coords) / 2
            scale = np.max(max_coords - min_coords) / 2
            
            points_normalized = (points - center) / scale if scale > 0 else points - center
            
        else:
            raise ValueError(f"Unknown normalization method: {self.method}")
        
        return points_normalized


class PointCloudVAEDataset(Dataset):
    """
    Dataset for loading 3D point clouds for VAE training
    Loads only normal samples from specified category
    """
    def __init__(self,
                 dataset_path: str,
                 category: str,
                 num_points: int = 2048,
                 phases: List[str] = ['train'],
                 augmentation_config: Optional[Dict] = None,
                 normalization_method: str = 'unit_sphere'):
        """
        Args:
            dataset_path: Path to MVTec 3D-AD dataset
            category: Object category (e.g., 'bagel', 'cable_gland')
            num_points: Target number of points per cloud
            phases: Dataset phases to include ('train', 'test')
            augmentation_config: Data augmentation configuration
            normalization_method: Point cloud normalization method
        """
        self.dataset_path = Path(dataset_path)
        self.category = category
        self.num_points = num_points
        self.phases = phases
        
        # Setup augmentation and normalization
        self.augmentation = None
        if augmentation_config is not None:
            self.augmentation = PointCloudAugmentation(augmentation_config)
        
        self.normalizer = PointCloudNormalizer(normalization_method)
        
        # Load file paths
        self.file_paths = self._collect_file_paths()
        
        print(f"PointCloudVAEDataset initialized:")
        print(f"  Category: {category}")
        print(f"  Phases: {phases}")
        print(f"  Files found: {len(self.file_paths)}")
        print(f"  Target points: {num_points}")
        print(f"  Augmentation: {'Enabled' if self.augmentation else 'Disabled'}")
    
    def _collect_file_paths(self) -> List[Path]:
        """Collect all normal sample file paths"""
        file_paths = []
        
        category_path = self.dataset_path / self.category
        
        for phase in self.phases:
            phase_path = category_path / phase
            
            if not phase_path.exists():
                print(f"Warning: Phase path does not exist: {phase_path}")
                continue
            
            # Only collect normal samples (good subfolder)
            normal_path = phase_path / 'good' / 'xyz'
            
            if not normal_path.exists():
                print(f"Warning: Normal samples path does not exist: {normal_path}")
                continue
            
            # Find all .tiff files
            tiff_files = list(normal_path.glob('*.tiff'))
            file_paths.extend(tiff_files)
            
            print(f"Found {len(tiff_files)} files in {normal_path}")
        
        return file_paths
    
    def _load_pointcloud(self, file_path: Path) -> np.ndarray:
        """
        Load point cloud from .tiff file
        
        Args:
            file_path: Path to .tiff file
            
        Returns:
            points: [N, 3] point cloud array
        """
        try:
            # Load organized point cloud
            organized_pc = tiff.imread(str(file_path))  # [H, W, 3] 800*800*3
            
            # Convert to unorganized point cloud
            points = organized_pc.reshape(-1, 3)  # [H*W, 3]
            
            # Filter out invalid points (zeros)
            valid_mask = (np.abs(points).sum(axis=1) > 1e-6)
            points = points[valid_mask]
            
            if len(points) == 0:
                raise ValueError("No valid points found in point cloud")
                
            return points
            
        except Exception as e:
            print(f"Error loading {file_path}: {e}")
            # Return random points as fallback
            return np.random.randn(1000, 3).astype(np.float32)
    
    def _sample_points(self, points: np.ndarray) -> np.ndarray:
        """
        Sample fixed number of points from point cloud
        
        Args:
            points: [N, 3] input points
            
        Returns:
            sampled_points: [num_points, 3] sampled points
        """
        N = len(points)
        
        if N >= self.num_points:
            # Random sampling without replacement
            indices = np.random.choice(N, self.num_points, replace=False)
            return points[indices]
        else:
            # Random sampling with replacement
            indices = np.random.choice(N, self.num_points, replace=True)
            return points[indices]
    
    def __len__(self) -> int:
        return len(self.file_paths)
    
    def __getitem__(self, idx: int) -> Dict[str, Any]:
        """
        Get a single sample
        
        Args:
            idx: Sample index
            
        Returns:
            sample: Dictionary containing point cloud and metadata
        """
        file_path = self.file_paths[idx]
        
        # Load point cloud
        points = self._load_pointcloud(file_path)
        
        # Normalize
        points = self.normalizer.normalize(points)
        
        # Sample points
        points = self._sample_points(points)
        
        # Apply augmentation
        if self.augmentation is not None:
            points = self.augmentation(points)
        
        # Convert to tensor
        points_tensor = torch.from_numpy(points.astype(np.float32))
        
        return {
            'points': points_tensor,
            'file_path': str(file_path),
            'category': self.category
        }


def create_vae_dataloader(dataset_path: str,
                         category: str,
                         batch_size: int = 8,
                         num_workers: int = 4,
                         num_points: int = 2048,
                         augmentation_config: Optional[Dict] = None,
                         phases: List[str] = ['train'],
                         shuffle: bool = True) -> DataLoader:
    """
    Create DataLoader for VAE training
    
    Args:
        dataset_path: Path to MVTec 3D-AD dataset
        category: Object category
        batch_size: Batch size
        num_workers: Number of data loading workers
        num_points: Number of points per sample
        augmentation_config: Data augmentation configuration
        phases: Dataset phases to include
        shuffle: Whether to shuffle data
        
    Returns:
        DataLoader for VAE training
    """
    dataset = PointCloudVAEDataset(
        dataset_path=dataset_path,
        category=category,
        num_points=num_points,
        phases=phases,
        augmentation_config=augmentation_config
    )
    
    dataloader = DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available(),
        drop_last=True  # Ensure consistent batch sizes
    )
    
    return dataloader

def test_vae_dataset():
    """Test function for VAE dataset and dataloader"""
    
    # Test configuration
    dataset_path = "/raid/liulinna/projects/M3DM/datasets/mvtec3d/"
    category = "bagel"
    
    # Augmentation configuration
    augmentation_config = {
        'enable_rotation': True,
        'enable_scaling': True,
        'enable_jittering': True,
        'rotation_range': {'x': 15, 'y': 15, 'z': 360},
        'scaling_range': [0.9, 1.1],
        'jitter_std': 0.01,
        'jitter_clip': 0.02
    }
    
    print("Testing VAE Dataset...")
    
    # Create dataset
    dataset = PointCloudVAEDataset(
        dataset_path=dataset_path,
        category=category,
        num_points=2048,
        phases=['train'],
        augmentation_config=augmentation_config
    )
    
    print(f"Dataset size: {len(dataset)}")
    
    # Test single sample
    if len(dataset) > 0:
        sample = dataset[0]
        print(f"Sample keys: {sample.keys()}")
        print(f"Points shape: {sample['points'].shape}")
        print(f"Points range: [{sample['points'].min():.3f}, {sample['points'].max():.3f}]")
    
    # Create dataloader
    dataloader = create_vae_dataloader(
        dataset_path=dataset_path,
        category=category,
        batch_size=4,
        num_workers=2,
        num_points=2048,
        augmentation_config=augmentation_config
    )
    
    print(f"DataLoader created with {len(dataloader)} batches")
    
    # Test batch
    for batch in dataloader:
        print(f"Batch points shape: {batch['points'].shape}")
        print(f"Batch points range: [{batch['points'].min():.3f}, {batch['points'].max():.3f}]")
        break
    
    print("VAE dataset test completed!")


if __name__ == "__main__":
    test_vae_dataset()