#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Multi-task Learning Core Module for IA-CRF Network
Handles multi-task optimization, loss balancing, and training coordination

Author: IA-CRF Project
Date: 2025-08-27
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional
import numpy as np


class MultiTaskLossBalancer(nn.Module):
    """
    Adaptive loss balancing for multi-task learning
    Implements uncertainty-based weighting and dynamic loss balancing
    """
    
    def __init__(self, num_tasks: int, initial_weights: Optional[List[float]] = None):
        super(MultiTaskLossBalancer, self).__init__()
        
        self.num_tasks = num_tasks
        
        # Initialize task weights
        if initial_weights is None:
            initial_weights = [1.0] * num_tasks
        
        # Learnable task weights (log-variance based)
        self.log_vars = nn.Parameter(torch.tensor(initial_weights, dtype=torch.float32))
        
    def forward(self, losses: List[torch.Tensor]) -> Tuple[torch.Tensor, Dict]:
        """
        Compute balanced multi-task loss
        
        Args:
            losses: List of individual task losses
            
        Returns:
            balanced_loss: Weighted sum of losses
            loss_weights: Current loss weights
        """
        if len(losses) != self.num_tasks:
            raise ValueError(f"Expected {self.num_tasks} losses, got {len(losses)}")
        
        # 检查输入损失值
        for i, loss in enumerate(losses):
            if torch.isnan(loss) or torch.isinf(loss):
                print(f"WARNING: Loss {i} is NaN or Inf: {loss.item()}")
                return torch.tensor(0.0, device=loss.device, requires_grad=True), {}
            if loss.item() == 0.0:
                print(f"WARNING: Loss {i} is exactly zero: {loss.item()}")
        
        # 使用简单的加权和方式，不使用不确定性加权
        # Compute uncertainty-based weights
        # precision = torch.exp(-self.log_vars)
        
        # 使用简单的固定权重
        weights = torch.softmax(self.log_vars, dim=0)  # 确保权重和为1
        
        balanced_loss = torch.tensor(0.0, device=losses[0].device, requires_grad=True)
        loss_info = {}
        
        for i, loss in enumerate(losses):
            # 简单加权：weight * loss
            weighted_loss = weights[i] * loss
            balanced_loss = balanced_loss + weighted_loss
            
            loss_info[f'task_{i}_weight'] = weights[i].item()
            loss_info[f'task_{i}_loss'] = loss.item()
            loss_info[f'task_{i}_weighted_loss'] = weighted_loss.item()
        
        loss_info['total_loss'] = balanced_loss.item()
        loss_info['total_unweighted_loss'] = sum([l.item() for l in losses])
        
        # 检查最终结果
        if torch.isnan(balanced_loss) or torch.isinf(balanced_loss) or balanced_loss.item() == 0.0:
            print(f"WARNING: Final balanced loss is problematic: {balanced_loss.item()}")
            print(f"Individual losses: {[l.item() for l in losses]}")
            print(f"Weights: {[w.item() for w in weights]}")
        
        return balanced_loss, loss_info


class MultiTaskOptimizer:
    """
    Multi-task optimizer with gradient management
    Handles gradient balancing and conflicting gradients
    """
    
    def __init__(self, model_params, lr=1e-4, weight_decay=1e-5, gradient_clip_norm=1.0):
        # 确保参数是正确的数值类型
        lr = float(lr) if not isinstance(lr, (int, float)) else lr
        weight_decay = float(weight_decay) if not isinstance(weight_decay, (int, float)) else weight_decay
        gradient_clip_norm = float(gradient_clip_norm) if not isinstance(gradient_clip_norm, (int, float)) else gradient_clip_norm
        
        self.optimizer = torch.optim.Adam(model_params, lr=lr, weight_decay=weight_decay)
        self.gradient_clip_norm = gradient_clip_norm
        
        # Gradient statistics
        self.gradient_norms = []
        self.gradient_conflicts = []
        
    def step(self, loss: torch.Tensor, model: nn.Module) -> Dict:
        """
        Perform optimization step with gradient analysis
        
        Args:
            loss: Total loss
            model: Model to optimize
            
        Returns:
            stats: Optimization statistics
        """
        self.optimizer.zero_grad()
        loss.backward()
        
        # Compute gradient statistics
        total_norm = 0
        for param in model.parameters():
            if param.grad is not None:
                param_norm = param.grad.data.norm(2)
                total_norm += param_norm.item() ** 2
        total_norm = total_norm ** (1. / 2)
        
        self.gradient_norms.append(total_norm)
        
        # Gradient clipping
        if self.gradient_clip_norm > 0:
            torch.nn.utils.clip_grad_norm_(model.parameters(), self.gradient_clip_norm)
        
        self.optimizer.step()
        
        stats = {
            'gradient_norm': total_norm,
            'learning_rate': self.optimizer.param_groups[0]['lr']
        }
        
        return stats
    
    def get_lr(self):
        """Get current learning rate"""
        return self.optimizer.param_groups[0]['lr']
    
    def set_lr(self, lr):
        """Set learning rate"""
        for param_group in self.optimizer.param_groups:
            param_group['lr'] = lr


class MultiTaskScheduler:
    """
    Learning rate scheduler for multi-task training
    """
    
    def __init__(self, optimizer: MultiTaskOptimizer, scheduler_type='cosine', 
                 max_epochs=200, warmup_epochs=10):
        self.optimizer = optimizer
        self.scheduler_type = scheduler_type
        self.max_epochs = max_epochs
        self.warmup_epochs = warmup_epochs
        self.current_epoch = 0
        self.base_lr = optimizer.get_lr()
        
    def step(self, epoch: Optional[int] = None):
        """Step the scheduler"""
        if epoch is not None:
            self.current_epoch = epoch
        else:
            self.current_epoch += 1
        
        lr = self._compute_lr()
        self.optimizer.set_lr(lr)
        
        return lr
    
    def _compute_lr(self) -> float:
        """Compute learning rate based on current epoch"""
        if self.current_epoch < self.warmup_epochs:
            # Warmup phase
            lr = self.base_lr * (self.current_epoch + 1) / self.warmup_epochs
        else:
            # Main training phase
            if self.scheduler_type == 'cosine':
                progress = (self.current_epoch - self.warmup_epochs) / (self.max_epochs - self.warmup_epochs)
                lr = self.base_lr * 0.5 * (1 + np.cos(np.pi * progress))
            elif self.scheduler_type == 'step':
                if self.current_epoch < self.max_epochs * 0.6:
                    lr = self.base_lr
                elif self.current_epoch < self.max_epochs * 0.8:
                    lr = self.base_lr * 0.1
                else:
                    lr = self.base_lr * 0.01
            else:
                lr = self.base_lr
        
        return lr


class MultiTaskTracker:
    """
    Training progress tracker for multi-task learning
    """
    
    def __init__(self):
        self.epoch_losses = []
        self.task_losses = {
            'albedo_recon': [],
            'shading_recon': [], 
            'pointcloud_recon': [],
            'kl_divergence': [],
            'crossmodal_consistency': []
        }
        self.learning_rates = []
        self.gradient_norms = []
        
    def update(self, epoch: int, losses: Dict, lr: float, grad_norm: float):
        """Update tracking metrics"""
        self.epoch_losses.append(losses.get('total_loss', 0))
        self.learning_rates.append(lr)
        self.gradient_norms.append(grad_norm)
        
        # Update individual task losses
        for task_name in self.task_losses.keys():
            if task_name in losses:
                self.task_losses[task_name].append(losses[task_name])
    
    def get_metrics(self, window_size: int = 10) -> Dict:
        """Get recent training metrics"""
        metrics = {}
        
        if len(self.epoch_losses) > 0:
            recent_losses = self.epoch_losses[-window_size:]
            metrics['avg_total_loss'] = np.mean(recent_losses)
            metrics['loss_trend'] = np.mean(recent_losses[-5:]) - np.mean(recent_losses[:5]) if len(recent_losses) >= 5 else 0
        
        if len(self.gradient_norms) > 0:
            metrics['avg_grad_norm'] = np.mean(self.gradient_norms[-window_size:])
        
        if len(self.learning_rates) > 0:
            metrics['current_lr'] = self.learning_rates[-1]
        
        # Task-specific metrics
        for task_name, task_losses in self.task_losses.items():
            if len(task_losses) > 0:
                metrics[f'{task_name}_loss'] = np.mean(task_losses[-window_size:])
        
        return metrics
    
    def should_early_stop(self, patience: int = 20, min_delta: float = 1e-6) -> bool:
        """Check if training should early stop"""
        if len(self.epoch_losses) < patience:
            return False
        
        recent_losses = self.epoch_losses[-patience:]
        best_loss = min(recent_losses)
        current_loss = recent_losses[-1]
        
        return current_loss > best_loss + min_delta


class MultiTaskCore:
    """
    Core multi-task learning manager
    Integrates all multi-task learning components
    """
    
    def __init__(self, model: nn.Module, config: Dict):
        self.model = model
        self.config = config
        
        # Initialize components
        self.loss_balancer = MultiTaskLossBalancer(
            num_tasks=5,  # albedo, shading, 3d, kl, crossmodal
            initial_weights=config['training']['loss_weights']
        )
        
        # 确保参数类型正确
        learning_rate = float(config['training']['learning_rate'])
        weight_decay = float(config['training']['weight_decay'])
        gradient_clip_norm = float(config['training']['gradient_clip_norm'])
        
        self.optimizer = MultiTaskOptimizer(
            model_params=model.parameters(),
            lr=learning_rate,
            weight_decay=weight_decay,
            gradient_clip_norm=gradient_clip_norm
        )
        
        self.scheduler = MultiTaskScheduler(
            optimizer=self.optimizer,
            scheduler_type=config['training']['scheduler_type'],
            max_epochs=config['training']['epochs'],
            warmup_epochs=config['training']['warmup_epochs']
        )
        
        self.tracker = MultiTaskTracker()
        
        print("Multi-task Core initialized")
        print(f"  Learning rate: {config['training']['learning_rate']}")
        print(f"  Loss weights: {config['training']['loss_weights']}")
        
    def training_step(self, epoch: int, losses: List[torch.Tensor], 
                     loss_names: List[str]) -> Dict:
        """
        Perform one training step
        
        Args:
            epoch: Current epoch
            losses: List of task losses
            loss_names: Names of tasks
            
        Returns:
            step_info: Training step information
        """
        # Balance losses
        total_loss, loss_info = self.loss_balancer(losses)
        
        # Optimization step
        opt_stats = self.optimizer.step(total_loss, self.model)
        
        # Scheduler step
        lr = self.scheduler.step(epoch)
        
        # Create loss dictionary with proper names
        named_losses = dict(zip(loss_names, [l.item() for l in losses]))
        named_losses.update(loss_info)
        
        # Update tracker
        self.tracker.update(epoch, named_losses, lr, opt_stats['gradient_norm'])
        
        # Compile step info
        step_info = {
            'total_loss': total_loss.item(),
            'learning_rate': lr,
            'gradient_norm': opt_stats['gradient_norm'],
            **named_losses
        }
        
        return step_info
    
    def get_training_metrics(self) -> Dict:
        """Get current training metrics"""
        return self.tracker.get_metrics()
    
    def should_early_stop(self) -> bool:
        """Check if training should early stop"""
        early_stop_config = self.config['training'].get('early_stopping', {})
        patience = early_stop_config.get('patience', 20)
        min_delta = early_stop_config.get('min_delta', 1e-6)
        
        # 确保参数类型正确
        patience = int(patience)
        min_delta = float(min_delta)  # 将字符串或科学计数法转换为浮点数
        
        return self.tracker.should_early_stop(patience, min_delta)
    
    def save_checkpoint(self, epoch: int, save_path: str, additional_info: Dict = None):
        """Save training checkpoint"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.optimizer.state_dict(),
            'loss_balancer_state_dict': self.loss_balancer.state_dict(),
            'tracker_state': {
                'epoch_losses': self.tracker.epoch_losses,
                'task_losses': self.tracker.task_losses,
                'learning_rates': self.tracker.learning_rates,
                'gradient_norms': self.tracker.gradient_norms
            },
            'config': self.config
        }
        
        if additional_info:
            checkpoint.update(additional_info)
        
        torch.save(checkpoint, save_path)
        print(f"Checkpoint saved to {save_path}")
    
    def load_checkpoint(self, checkpoint_path: str) -> int:
        """Load training checkpoint"""
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.optimizer.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.loss_balancer.load_state_dict(checkpoint['loss_balancer_state_dict'])
        
        # Restore tracker state
        tracker_state = checkpoint['tracker_state']
        self.tracker.epoch_losses = tracker_state['epoch_losses']
        self.tracker.task_losses = tracker_state['task_losses']
        self.tracker.learning_rates = tracker_state['learning_rates']
        self.tracker.gradient_norms = tracker_state['gradient_norms']
        
        epoch = checkpoint['epoch']
        print(f"Checkpoint loaded from {checkpoint_path}, resuming from epoch {epoch}")
        
        return epoch


def test_multitask_core():
    """
    Test the multi-task core functionality
    """
    # Create dummy model and config
    dummy_model = nn.Linear(10, 1)
    
    config = {
        'training': {
            'learning_rate': 1e-4,
            'weight_decay': 1e-5,
            'gradient_clip_norm': 1.0,
            'scheduler_type': 'cosine',
            'epochs': 100,
            'warmup_epochs': 10,
            'loss_weights': [1.0, 1.0, 0.5, 0.1, 0.5],
            'early_stopping': {
                'patience': 20,
                'min_delta': 1e-6
            }
        }
    }
    
    # Create multi-task core
    core = MultiTaskCore(dummy_model, config)
    
    # Simulate training steps
    for epoch in range(5):
        # Create dummy losses
        losses = [
            torch.tensor(1.0 - epoch * 0.1),  # Decreasing loss
            torch.tensor(0.8 - epoch * 0.05), 
            torch.tensor(0.5 - epoch * 0.02),
            torch.tensor(0.1 + epoch * 0.01),  # Slight increase (KL)
            torch.tensor(0.3 - epoch * 0.03)
        ]
        
        loss_names = ['albedo_recon', 'shading_recon', 'pointcloud_recon', 
                     'kl_divergence', 'crossmodal_consistency']
        
        step_info = core.training_step(epoch, losses, loss_names)
        
        print(f"Epoch {epoch}:")
        for key, value in step_info.items():
            print(f"  {key}: {value:.6f}")
    
    # Get metrics
    metrics = core.get_training_metrics()
    print(f"\nTraining Metrics:")
    for key, value in metrics.items():
        print(f"  {key}: {value:.6f}")
    
    print("Multi-task core test completed successfully!")


if __name__ == "__main__":
    test_multitask_core()