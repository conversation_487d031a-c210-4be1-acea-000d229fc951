#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VAE Loss Functions for 3D Point Cloud Training
Implements reconstruction loss (Chamfer Distance) and KL divergence loss
with adaptive beta scheduling for balanced training
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Tuple, Optional


def chamfer_distance(pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
    """
    Compute Chamfer Distance between two point sets
    
    Args:
        pred: [B, N, 3] predicted points
        target: [B, M, 3] target points
        
    Returns:
        chamfer_dist: [B] chamfer distance for each sample in batch
    """
    # pred: [B, N, 3], target: [B, M, 3]
    B, N, _ = pred.shape
    M = target.shape[1]
    
    # Use repeat instead of expand for gradient safety
    pred_expanded = pred.unsqueeze(2).repeat(1, 1, M, 1)  # [B, N, M, 3]
    target_expanded = target.unsqueeze(1).repeat(1, N, 1, 1)  # [B, N, M, 3]
    
    # Compute squared distances
    distances = torch.sum((pred_expanded - target_expanded) ** 2, dim=3)  # [B, N, M]
    
    # Find minimum distances
    dist_pred_to_target = torch.min(distances, dim=2)[0]  # [B, N]
    dist_target_to_pred = torch.min(distances, dim=1)[0]  # [B, M]
    
    # Chamfer distance is the sum of both directions
    chamfer_dist = torch.mean(dist_pred_to_target, dim=1) + torch.mean(dist_target_to_pred, dim=1)  # [B]
    
    return chamfer_dist


def efficient_chamfer_distance(pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
    """
    Memory-efficient implementation of Chamfer Distance
    Processes data in smaller chunks to reduce memory usage
    
    Args:
        pred: [B, N, 3] predicted points
        target: [B, M, 3] target points
        
    Returns:
        chamfer_dist: [B] chamfer distance for each sample in batch
    """
    B, N, _ = pred.shape
    M = target.shape[1]
    
    # Chunk size to control memory usage
    chunk_size = min(1000, N, M)
    
    # Initialize distance arrays (clone to avoid gradient issues)
    min_pred_to_target = torch.full((B, N), float('inf'), device=pred.device, requires_grad=False)
    min_target_to_pred = torch.full((B, M), float('inf'), device=pred.device, requires_grad=False)
    
    # Compute distances in chunks
    for i in range(0, N, chunk_size):
        end_i = min(i + chunk_size, N)
        pred_chunk = pred[:, i:end_i]  # [B, chunk_i, 3]
        
        for j in range(0, M, chunk_size):
            end_j = min(j + chunk_size, M)
            target_chunk = target[:, j:end_j]  # [B, chunk_j, 3]
            
            # Compute distances for this chunk pair
            pred_exp = pred_chunk.unsqueeze(2)  # [B, chunk_i, 1, 3]
            target_exp = target_chunk.unsqueeze(1)  # [B, 1, chunk_j, 3]
            
            chunk_distances = torch.sum((pred_exp - target_exp) ** 2, dim=3)  # [B, chunk_i, chunk_j]
            
            # Update minimum distances (use clone to avoid in-place operations)
            chunk_min_pred = torch.min(chunk_distances, dim=2)[0]  # [B, chunk_i]
            chunk_min_target = torch.min(chunk_distances, dim=1)[0]  # [B, chunk_j]
            
            min_pred_to_target[:, i:end_i] = torch.min(
                min_pred_to_target[:, i:end_i].clone(), chunk_min_pred
            )
            min_target_to_pred[:, j:end_j] = torch.min(
                min_target_to_pred[:, j:end_j].clone(), chunk_min_target
            )
    
    # Compute final chamfer distance
    chamfer_dist = torch.mean(min_pred_to_target, dim=1) + torch.mean(min_target_to_pred, dim=1)
    
    return chamfer_dist


def kl_divergence_loss(mu: torch.Tensor, logvar: torch.Tensor) -> torch.Tensor:
    """
    Compute KL divergence loss between learned distribution and standard normal
    
    Args:
        mu: [B, latent_dim] mean of learned distribution
        logvar: [B, latent_dim] log variance of learned distribution
        
    Returns:
        kl_loss: [B] KL divergence for each sample in batch
    """
    # KL(q(z|x) || p(z)) where p(z) = N(0, I)
    kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp(), dim=1)
    return kl_loss


class VAELoss(nn.Module):
    """
    Complete VAE loss function combining reconstruction and KL divergence losses
    """
    def __init__(self, 
                 reconstruction_loss: str = 'chamfer',
                 beta: float = 1.0,
                 use_efficient_chamfer: bool = True):
        """
        Args:
            reconstruction_loss: Type of reconstruction loss ('chamfer', 'mse')
            beta: Weight for KL divergence loss (beta-VAE)
            use_efficient_chamfer: Whether to use memory-efficient chamfer distance
        """
        super(VAELoss, self).__init__()
        
        self.reconstruction_loss = reconstruction_loss
        self.beta = beta
        self.use_efficient_chamfer = use_efficient_chamfer
        
        if reconstruction_loss == 'mse':
            self.mse_loss = nn.MSELoss(reduction='none')
    
    def forward(self, 
                pred: torch.Tensor, 
                target: torch.Tensor, 
                mu: torch.Tensor, 
                logvar: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Compute VAE loss
        
        Args:
            pred: [B, N, 3] predicted point cloud
            target: [B, M, 3] target point cloud  
            mu: [B, latent_dim] mean of latent distribution
            logvar: [B, latent_dim] log variance of latent distribution
            
        Returns:
            total_loss: Combined loss
            recon_loss: Reconstruction loss
            kl_loss: KL divergence loss
        """
        # Reconstruction loss
        if self.reconstruction_loss == 'chamfer':
            if self.use_efficient_chamfer:
                recon_loss_batch = efficient_chamfer_distance(pred, target)
            else:
                recon_loss_batch = chamfer_distance(pred, target)
        elif self.reconstruction_loss == 'mse':
            # For MSE, we need to ensure same number of points
            if pred.shape[1] != target.shape[1]:
                # Downsample or upsample to match
                min_points = min(pred.shape[1], target.shape[1])
                pred = pred[:, :min_points]
                target = target[:, :min_points]
            
            mse_batch = self.mse_loss(pred, target)  # [B, N, 3]
            recon_loss_batch = torch.mean(mse_batch.view(pred.shape[0], -1), dim=1)  # [B]
        else:
            raise ValueError(f"Unknown reconstruction loss: {self.reconstruction_loss}")
        
        # KL divergence loss
        kl_loss_batch = kl_divergence_loss(mu, logvar)
        
        # Total loss
        total_loss_batch = recon_loss_batch + self.beta * kl_loss_batch
        
        # Return mean losses over batch
        return (torch.mean(total_loss_batch), 
                torch.mean(recon_loss_batch), 
                torch.mean(kl_loss_batch))


class AdaptiveBetaScheduler:
    """
    Adaptive beta scheduler for controlling KL loss weight during training
    """
    def __init__(self, 
                 initial_beta: float = 0.0,
                 final_beta: float = 1.0,
                 warmup_epochs: int = 50,
                 mode: str = 'linear'):
        """
        Args:
            initial_beta: Starting beta value
            final_beta: Final beta value
            warmup_epochs: Number of epochs for warmup
            mode: Scheduling mode ('linear', 'cosine', 'exponential')
        """
        self.initial_beta = initial_beta
        self.final_beta = final_beta
        self.warmup_epochs = warmup_epochs
        self.mode = mode
    
    def get_beta(self, epoch: int) -> float:
        """Get beta value for current epoch"""
        if epoch >= self.warmup_epochs:
            return self.final_beta
        
        progress = epoch / self.warmup_epochs
        
        if self.mode == 'linear':
            beta = self.initial_beta + progress * (self.final_beta - self.initial_beta)
        elif self.mode == 'cosine':
            beta = self.initial_beta + 0.5 * (self.final_beta - self.initial_beta) * (1 - np.cos(np.pi * progress))
        elif self.mode == 'exponential':
            beta = self.initial_beta * (self.final_beta / self.initial_beta) ** progress
        else:
            raise ValueError(f"Unknown scheduling mode: {self.mode}")
        
        return beta
