#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IA-CRF Network - Integrated Anomaly Detection with Causally-consistent Reconstruction and Fusion
Main network architecture for second-stage end-to-end multi-task cooperative training

Author: IA-CRF Project
Date: 2025-08-27
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Optional
import sys
import os

# Add project path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from model.pointcloud_vae import PointCloudVAE
from model.multisource_fusion import EnhancedMultiSourceFusion


class AppearanceEncoder(nn.Module):
    """
    Appearance Encoder for Albedo/Material features
    Processes albedo images to extract material-related features
    """
    
    def __init__(self, input_channels=3, base_width=64, feature_dim=256):
        super(AppearanceEncoder, self).__init__()
        
        self.feature_dim = feature_dim
        
        # Encoder blocks with progressively increasing channels
        self.block1 = self._make_conv_block(input_channels, base_width)
        self.pool1 = nn.MaxPool2d(2, 2)
        
        self.block2 = self._make_conv_block(base_width, base_width*2)
        self.pool2 = nn.MaxPool2d(2, 2)
        
        self.block3 = self._make_conv_block(base_width*2, base_width*4)
        self.pool3 = nn.MaxPool2d(2, 2)
        
        self.block4 = self._make_conv_block(base_width*4, base_width*8)
        self.pool4 = nn.MaxPool2d(2, 2)
        
        # Feature projection
        # Input: 224x224 -> after 4 poolings: 14x14
        self.global_pool = nn.AdaptiveAvgPool2d((14, 14))
        self.feature_proj = nn.Sequential(
            nn.Conv2d(base_width*8, feature_dim, 1),
            nn.BatchNorm2d(feature_dim),
            nn.ReLU(inplace=True)
        )
        
    def _make_conv_block(self, in_channels, out_channels):
        """Create a convolutional block"""
        return nn.Sequential(
            nn.Conv2d(in_channels, out_channels, 3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, 3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x):
        """
        Forward pass
        
        Args:
            x: Albedo image [B, 3, H, W]
            
        Returns:
            features: Material features [B, feature_dim, 14, 14]
        """
        # Encoder pathway
        x1 = self.block1(x)      # [B, 64, 224, 224]
        x = self.pool1(x1)       # [B, 64, 112, 112]
        
        x2 = self.block2(x)      # [B, 128, 112, 112]
        x = self.pool2(x2)       # [B, 128, 56, 56]
        
        x3 = self.block3(x)      # [B, 256, 56, 56]
        x = self.pool3(x3)       # [B, 256, 28, 28]
        
        x4 = self.block4(x)      # [B, 512, 28, 28]
        x = self.pool4(x4)       # [B, 512, 14, 14]
        
        # Global feature extraction
        x = self.global_pool(x)   # [B, 512, 14, 14]
        features = self.feature_proj(x)  # [B, feature_dim, 14, 14]
        
        return features


class GeometryLightingEncoder(nn.Module):
    """
    Geometry/Lighting Encoder for Shading features
    Processes shading images to extract shape and lighting-related features
    """
    
    def __init__(self, input_channels=3, base_width=64, feature_dim=256):
        super(GeometryLightingEncoder, self).__init__()
        
        self.feature_dim = feature_dim
        
        # Similar architecture to appearance encoder but with different initialization
        self.block1 = self._make_conv_block(input_channels, base_width)
        self.pool1 = nn.MaxPool2d(2, 2)
        
        self.block2 = self._make_conv_block(base_width, base_width*2)
        self.pool2 = nn.MaxPool2d(2, 2)
        
        self.block3 = self._make_conv_block(base_width*2, base_width*4)
        self.pool3 = nn.MaxPool2d(2, 2)
        
        self.block4 = self._make_conv_block(base_width*4, base_width*8)
        self.pool4 = nn.MaxPool2d(2, 2)
        
        # Feature projection
        self.global_pool = nn.AdaptiveAvgPool2d((14, 14))
        self.feature_proj = nn.Sequential(
            nn.Conv2d(base_width*8, feature_dim, 1),
            nn.BatchNorm2d(feature_dim),
            nn.ReLU(inplace=True)
        )
        
    def _make_conv_block(self, in_channels, out_channels):
        """Create a convolutional block"""
        return nn.Sequential(
            nn.Conv2d(in_channels, out_channels, 3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, 3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, x):
        """
        Forward pass
        
        Args:
            x: Shading image [B, 3, H, W]
            
        Returns:
            features: Geometry/lighting features [B, feature_dim, 14, 14]
        """
        # Encoder pathway  
        x1 = self.block1(x)      # [B, 64, 224, 224]
        x = self.pool1(x1)       # [B, 64, 112, 112]
        
        x2 = self.block2(x)      # [B, 128, 112, 112]
        x = self.pool2(x2)       # [B, 128, 56, 56]
        
        x3 = self.block3(x)      # [B, 256, 56, 56]
        x = self.pool3(x3)       # [B, 256, 28, 28]
        
        x4 = self.block4(x)      # [B, 512, 28, 28]
        x = self.pool4(x4)       # [B, 512, 14, 14]
        
        # Global feature extraction
        x = self.global_pool(x)   # [B, 512, 14, 14]
        features = self.feature_proj(x)  # [B, feature_dim, 14, 14]
        
        return features


class ReconstructionDecoder(nn.Module):
    """
    Reconstruction Decoder for 2D image reconstruction
    Supports both albedo and shading reconstruction
    """
    
    def __init__(self, feature_dim=256, output_channels=3, base_width=64):
        super(ReconstructionDecoder, self).__init__()
        
        # Feature expansion
        self.expand = nn.Sequential(
            nn.Conv2d(feature_dim, base_width*8, 1),
            nn.BatchNorm2d(base_width*8),
            nn.ReLU(inplace=True)
        )
        
        # Decoder blocks with upsampling
        self.up1 = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
        self.decoder1 = self._make_decoder_block(base_width*8, base_width*4)
        
        self.up2 = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
        self.decoder2 = self._make_decoder_block(base_width*4, base_width*2)
        
        self.up3 = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
        self.decoder3 = self._make_decoder_block(base_width*2, base_width)
        
        self.up4 = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
        self.decoder4 = self._make_decoder_block(base_width, base_width)
        
        # Final output layer
        self.final = nn.Sequential(
            nn.Conv2d(base_width, output_channels, 3, padding=1),
            nn.Sigmoid()  # Assuming normalized image output
        )
        
    def _make_decoder_block(self, in_channels, out_channels):
        """Create a decoder block"""
        return nn.Sequential(
            nn.Conv2d(in_channels, out_channels, 3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels, out_channels, 3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(inplace=True)
        )
    
    def forward(self, features):
        """
        Forward pass
        
        Args:
            features: Input features [B, feature_dim, 14, 14]
            
        Returns:
            reconstructed: Reconstructed image [B, output_channels, 224, 224]
        """
        x = self.expand(features)    # [B, 512, 14, 14]
        
        x = self.up1(x)              # [B, 512, 28, 28]
        x = self.decoder1(x)         # [B, 256, 28, 28]
        
        x = self.up2(x)              # [B, 256, 56, 56]
        x = self.decoder2(x)         # [B, 128, 56, 56]
        
        x = self.up3(x)              # [B, 128, 112, 112]
        x = self.decoder3(x)         # [B, 64, 112, 112]
        
        x = self.up4(x)              # [B, 64, 224, 224]
        x = self.decoder4(x)         # [B, 64, 224, 224]
        
        reconstructed = self.final(x)  # [B, output_channels, 224, 224]
        
        return reconstructed


class CrossModalMLP(nn.Module):
    """
    Cross-modal Feature Mapping MLP for strong correlation verification
    Implements bidirectional feature prediction between shading and 3D features
    """
    
    def __init__(self, shading_feature_dim=256, vae_latent_dim=256, hidden_dim=512):
        super(CrossModalMLP, self).__init__()
        
        # Shading features need to be flattened: [B, 256, 14, 14] -> [B, 256*196]
        self.shading_input_dim = shading_feature_dim * 14 * 14
        self.vae_latent_dim = vae_latent_dim
        
        # Shading to 3D VAE latent
        self.shading_to_3d = nn.Sequential(
            nn.Linear(self.shading_input_dim, hidden_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, hidden_dim//2),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim//2, vae_latent_dim)
        )
        
        # 3D VAE latent to Shading features
        self.vae_to_shading = nn.Sequential(
            nn.Linear(vae_latent_dim, hidden_dim//2),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim//2, hidden_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(hidden_dim, self.shading_input_dim)
        )
        
    def forward(self, shading_features, vae_latent):
        """
        Bidirectional feature mapping
        
        Args:
            shading_features: [B, feature_dim, 14, 14]
            vae_latent: [B, latent_dim]
            
        Returns:
            predicted_vae_latent: [B, latent_dim]
            predicted_shading_features: [B, feature_dim, 14, 14]
        """
        B, C, H, W = shading_features.shape
        
        # Flatten shading features
        shading_flat = shading_features.view(B, -1)  # [B, C*H*W]
        
        # Bidirectional prediction
        predicted_vae_latent = self.shading_to_3d(shading_flat)  # [B, latent_dim]
        predicted_shading_flat = self.vae_to_shading(vae_latent)  # [B, C*H*W]
        
        # Reshape predicted shading features back
        predicted_shading_features = predicted_shading_flat.view(B, C, H, W)
        
        return predicted_vae_latent, predicted_shading_features


class IACRFNetwork(nn.Module):
    """
    Integrated IA-CRF Network for Multi-task Cooperative Training
    Combines material, geometry, and 3D VAE branches for end-to-end training
    """
    
    def __init__(self, config: Dict):
        super(IACRFNetwork, self).__init__()
        
        self.config = config
        model_config = config['model']
        
        # Feature dimensions
        self.albedo_feature_dim = model_config['albedo_encoder']['feature_dim']
        self.shading_feature_dim = model_config['shading_encoder']['feature_dim'] 
        self.vae_latent_dim = model_config['vae_branch']['latent_dim']
        
        # 2D Encoders (randomly initialized)
        self.appearance_encoder = AppearanceEncoder(
            input_channels=3,
            base_width=model_config['albedo_encoder']['base_width'],
            feature_dim=self.albedo_feature_dim
        )
        
        self.geometry_lighting_encoder = GeometryLightingEncoder(
            input_channels=3,
            base_width=model_config['shading_encoder']['base_width'],
            feature_dim=self.shading_feature_dim
        )
        
        # 3D VAE Branch (load from stage 1 checkpoint)
        self.vae_3d = PointCloudVAE(
            input_dim=model_config['vae_branch']['input_dim'],
            latent_dim=self.vae_latent_dim,
            feature_dim=model_config['vae_branch']['feature_dim'],
            output_points=model_config['vae_branch']['output_points']
        )
        
        # Reconstruction Decoders (randomly initialized)
        self.albedo_decoder = ReconstructionDecoder(
            feature_dim=self.albedo_feature_dim,
            output_channels=3,
            base_width=model_config['albedo_decoder']['base_width']
        )
        
        self.shading_decoder = ReconstructionDecoder(
            feature_dim=self.shading_feature_dim,
            output_channels=3,
            base_width=model_config['shading_decoder']['base_width']
        )
        
        # Cross-modal Mapping MLP (randomly initialized)
        self.cross_modal_mlp = CrossModalMLP(
            shading_feature_dim=self.shading_feature_dim,
            vae_latent_dim=self.vae_latent_dim,
            hidden_dim=model_config['cross_modal']['hidden_dim']
        )
        
        # Multi-source Feature Fusion Module
        fusion_config = {
            'model': model_config,
            'fusion': {
                'aligned_dim': 256,
                'attention_hidden_dim': 128,
                'output_dim': 256
            }
        }
        self.fusion_module = EnhancedMultiSourceFusion(fusion_config)
        
        print("IA-CRF Network initialized")
        print(f"  Albedo feature dim: {self.albedo_feature_dim}")
        print(f"  Shading feature dim: {self.shading_feature_dim}")
        print(f"  VAE latent dim: {self.vae_latent_dim}")
        
    def load_vae_checkpoint(self, checkpoint_path: str):
        """
        加载预训练的3D VAE，支持跳过输入层不匹配的权重
        
        Args:
            checkpoint_path: Stage 1 VAE模型路径
        """
        if not os.path.exists(checkpoint_path):
            print(f"Warning: VAE checkpoint not found at {checkpoint_path}")
            print(f"Expected path format: ./checkpoints/vae_{{category}}/best.pth")
            print("Please ensure Stage 1 VAE training is completed first")
            print("Using randomly initialized VAE weights")
            return
            
        try:
            print(f"加载预训练VAE权重: {checkpoint_path}")
            
            # 使用VAE的选择性权重加载功能
            self.vae_3d.load_pretrained_weights(checkpoint_path, strict=False)
            
            #print(f"✅ VAE权重加载完成（跳过不匹配的输入层）")
                
        except Exception as e:
            print(f"⚠️ 加载VAE权重失败: {e}")
            print("使用随机初始化的VAE权重")
    
    def forward(self, batch_data: Dict) -> Dict:
        """
        Forward pass through IA-CRF Network
        
        Args:
            batch_data: Dictionary containing:
                - corrupted_albedo: [B, 3, H, W]
                - corrupted_shading: [B, 3, H, W] 
                - corrupted_pointcloud: [B, N, 7] # 增强几何特征（XYZ + Normal + Curvature）
                
        Returns:
            results: Dictionary containing all outputs
        """
        # Extract inputs
        corrupted_albedo = batch_data['corrupted_albedo']
        corrupted_shading = batch_data['corrupted_shading']
        corrupted_pointcloud = batch_data['corrupted_pointcloud']
        
        # Parallel encoding
        albedo_features = self.appearance_encoder(corrupted_albedo)      # [B, 256, 14, 14]
        shading_features = self.geometry_lighting_encoder(corrupted_shading)  # [B, 256, 14, 14]
        
        # 3D VAE encoding
        vae_results = self.vae_3d(corrupted_pointcloud)
        vae_latent = vae_results['latent_code']                         # [B, 128]
        reconstructed_3d = vae_results['reconstructed']                 # [B, N, 3]
        mu = vae_results['mu']                                          # [B, 128]
        logvar = vae_results['logvar']                                  # [B, 128]
        
        # Multi-task reconstruction
        reconstructed_albedo = self.albedo_decoder(albedo_features)     # [B, 3, 224, 224]
        reconstructed_shading = self.shading_decoder(shading_features)  # [B, 3, 224, 224]
        
        # Cross-modal prediction  
        predicted_vae_latent, predicted_shading_features = self.cross_modal_mlp(
            shading_features, vae_latent
        )
        
        # Multi-source feature fusion
        fusion_results = self.fusion_module(
            albedo_features, shading_features, vae_latent
        )
        
        # Compile results
        results = {
            # Encoded features
            'albedo_features': albedo_features,
            'shading_features': shading_features,
            'vae_latent': vae_latent,
            'vae_mu': mu,
            'vae_logvar': logvar,
            
            # Reconstructions
            'reconstructed_albedo': reconstructed_albedo,
            'reconstructed_shading': reconstructed_shading,
            'reconstructed_3d': reconstructed_3d,
            
            # Cross-modal predictions
            'predicted_vae_latent': predicted_vae_latent,
            'predicted_shading_features': predicted_shading_features,
            
            # Multi-source fusion results
            'fusion_results': fusion_results,
            'final_anomaly_score': fusion_results.get('final_anomaly_score', None)
        }
        
        return results


def test_iacrf_network():
    """
    Test the IA-CRF Network architecture
    """
    # Test configuration
    config = {
        'model': {
            'albedo_encoder': {
                'base_width': 64,
                'feature_dim': 256
            },
            'shading_encoder': {
                'base_width': 64,
                'feature_dim': 256
            },
            'vae_branch': {
                'input_dim': 3,
                'latent_dim': 128,
                'feature_dim': 256,
                'output_points': 2048
            },
            'albedo_decoder': {
                'base_width': 64
            },
            'shading_decoder': {
                'base_width': 64
            },
            'cross_modal': {
                'hidden_dim': 512
            }
        }
    }
    
    # Create network
    network = IACRFNetwork(config)
    
    # Create test data
    batch_size = 2
    test_data = {
        'corrupted_albedo': torch.randn(batch_size, 3, 224, 224),
        'corrupted_shading': torch.randn(batch_size, 3, 224, 224),
        'corrupted_pointcloud': torch.randn(batch_size, 2048, 3)
    }
    
    # Forward pass
    with torch.no_grad():
        results = network(test_data)
    
    # Print results
    print("IA-CRF Network Test Results:")
    for key, value in results.items():
        if isinstance(value, torch.Tensor):
            print(f"  {key}: {value.shape}")
    
    print("Test completed successfully!")
    
    return network, results


if __name__ == "__main__":
    test_iacrf_network()