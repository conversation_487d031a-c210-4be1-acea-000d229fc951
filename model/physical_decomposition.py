import os
import sys
import numpy as np
import torch
import torch.nn as nn
import open3d as o3d
import tifffile as tiff
import cv2
from scipy.spatial import cKDTree
from sklearn.decomposition import PCA
import warnings
warnings.filterwarnings('ignore')


class PhysicalDecomposition(nn.Module):
    """
    物理信息提取与分解模块
    负责将输入数据分解为反照率图、着色图和增强型点云等物理分量
    """
    
    def __init__(self, config):
        super(PhysicalDecomposition, self).__init__()
        self.config = config
        
        # 从配置中获取尺度选择参数，默认为1个尺度（性能优先）
        self.num_scales = config.get('num_scales', 1)
        if self.num_scales not in [1, 2, 3]:
            print(f"Warning: Invalid num_scales {self.num_scales}, using 1 scale")
            self.num_scales = 1
        
        # 根据选择的尺度数量设置多尺度参数
        all_scale_params = [
            {'radius': 0.02, 'max_nn': 30},   # 细节尺度
            {'radius': 0.05, 'max_nn': 50},   # 中等尺度  
            {'radius': 0.1, 'max_nn': 100},   # 粗糙尺度
        ]
        
        # 根据num_scales选择对应数量的尺度
        self.multi_scale_params = all_scale_params[:self.num_scales]
        
        print(f"使用{self.num_scales}个尺度进行多尺度特征提取")
        for i, params in enumerate(self.multi_scale_params):
            print(f"尺度{i+1}: radius={params['radius']}, max_nn={params['max_nn']}")
        
        # 注意：用户数据集中已预处理了albedo和shading图像
        # 不需要实时分解，直接从数据集加载
        self.intrinsic_models = None
        print("使用预处理的albedo和shading数据，无需实时分解")
    
    def rgb_intrinsic_decomposition(self, rgb_image):
        """
        2D本征分解：RGB -> 反照率图(A_d) + 着色图(S_d)
        输入: rgb_image [B, 3, H, W] 或 numpy array [H, W, 3]
        输出: albedo_map, shading_map 
        """
        if self.intrinsic_models is not None:
            # 使用专业的本征分解模型
            return self._professional_intrinsic_decomposition(rgb_image)
        else:
            # 使用简单的CNN分解（备用方案）
            return self._simple_cnn_decomposition(rgb_image)
    
    def _professional_intrinsic_decomposition(self, rgb_image):
        """
        使用专业的本征分解库进行分解
        """
        # 处理输入格式
        if isinstance(rgb_image, torch.Tensor):
            if len(rgb_image.shape) == 4:  # [B, 3, H, W]
                batch_size = rgb_image.shape[0]
                albedo_list = []
                shading_list = []
                
                for i in range(batch_size):
                    # 转换为 numpy 格式 [H, W, 3]
                    img_np = rgb_image[i].permute(1, 2, 0).cpu().numpy()
                    img_np = np.clip(img_np, 0, 1)  # 确保在[0,1]范围
                    
                    # 运行本征分解管道
                    try:
                        results = run_pipeline(self.intrinsic_models, img_np, device='cuda')
                        
                        # 提取反照率和着色
                        albedo = view(results['hr_alb'])  # gamma校正的反照率
                        shading = 1 - invert(results['dif_shd'])  # 音调映射的散射着色
                        
                        # 转换为 tensor格式 [3, H, W]
                        albedo_tensor = torch.from_numpy(albedo).permute(2, 0, 1).float()
                        shading_tensor = torch.from_numpy(shading).permute(2, 0, 1).float()
                        
                        albedo_list.append(albedo_tensor)
                        shading_list.append(shading_tensor)
                        
                    except Exception as e:
                        print(f"本征分解失败: {e}, 使用简单分解")
                        # 如果失败，使用简单分解
                        return self._simple_cnn_decomposition(rgb_image)
                
                # 合并批次
                albedo_batch = torch.stack(albedo_list, dim=0)
                shading_batch = torch.stack(shading_list, dim=0)
                
                return albedo_batch, shading_batch
                
            elif len(rgb_image.shape) == 3:  # [3, H, W]
                # 单张图像，添加batch维度
                return self._professional_intrinsic_decomposition(rgb_image.unsqueeze(0))
        
        elif isinstance(rgb_image, np.ndarray):
            if len(rgb_image.shape) == 3:  # [H, W, 3]
                # numpy格式输入
                img_np = np.clip(rgb_image, 0, 1)
                
                try:
                    results = run_pipeline(self.intrinsic_models, img_np, device='cuda')
                    
                    albedo = view(results['hr_alb'])
                    shading = 1 - invert(results['dif_shd'])
                    
                    # 转换为 tensor并添加batch维度
                    albedo_tensor = torch.from_numpy(albedo).permute(2, 0, 1).unsqueeze(0).float()
                    shading_tensor = torch.from_numpy(shading).permute(2, 0, 1).unsqueeze(0).float()
                    
                    return albedo_tensor, shading_tensor
                    
                except Exception as e:
                    print(f"本征分解失败: {e}, 使用简单分解")
                    # 转换为tensor后使用简单分解
                    rgb_tensor = torch.from_numpy(rgb_image).permute(2, 0, 1).unsqueeze(0).float()
                    return self._simple_cnn_decomposition(rgb_tensor)
        
        # 如果格式不支持，使用简单分解
        return self._simple_cnn_decomposition(rgb_image)
    
    def _simple_cnn_decomposition(self, rgb_image):
        """
        简单的CNN分解（备用方案）
        """
        # 确保输入是tensor格式
        if isinstance(rgb_image, np.ndarray):
            if len(rgb_image.shape) == 3:  # [H, W, 3]
                rgb_image = torch.from_numpy(rgb_image).permute(2, 0, 1).unsqueeze(0).float()
            elif len(rgb_image.shape) == 4:  # [B, H, W, 3]
                rgb_image = torch.from_numpy(rgb_image).permute(0, 3, 1, 2).float()
        
        # 简单的CNN分解
        albedo = self.albedo_net(rgb_image)
        shading = self.shading_net(rgb_image)
        
        # 去除梯度信息，避免后续保存时出现梯度错误
        return albedo.detach(), shading.detach()
    
    def load_and_preprocess_pointcloud(self, tiff_path, target_size=(224, 224)):
        """
        从.tiff文件加载3D点云并预处理到指定尺寸
        """
        # 读取tiff文件
        xyz_data = tiff.imread(tiff_path)
        
        # Resize到目标尺寸
        xyz_resized = self._resize_organized_pc(xyz_data, target_size)
        
        return xyz_resized
    
    def _resize_organized_pc(self, organized_pc, target_size=(224, 224)):
        """
        将有组织点云resize到目标尺寸
        """
        torch_organized_pc = torch.tensor(organized_pc).permute(2, 0, 1).unsqueeze(dim=0).contiguous().float()
        torch_resized_organized_pc = torch.nn.functional.interpolate(
            torch_organized_pc, 
            size=target_size,
            mode='nearest'
        )
        return torch_resized_organized_pc.squeeze(dim=0).permute(1, 2, 0).contiguous().numpy()
    
    def extract_3d_physical_features(self, organized_pointcloud):
        """
        3D物理特征提取：计算多尺度法线、曲率等几何特征
        输入: organized_pointcloud [H, W, 3] numpy array
        输出: enhanced_features [H*W, D] 其中D包含位置+多尺度法线+多尺度曲率特征
        """
        H, W, _ = organized_pointcloud.shape
        
        # 步骤一：数据结构转换与空间索引构建
        print("步骤一：构建KD树空间索引...")
        pcd, valid_mask, point_coords = self._build_kdtree_index(organized_pointcloud)
        
        if pcd is None:
            # 如果没有有效点，返回零特征
            # 特征维度: 3(xyz) + 尺度数*4(3法线+1曲率)
            feature_dim = 3 + len(self.multi_scale_params) * 4
            return np.zeros((H*W, feature_dim))
        
        # 步骤二：多尺度表面法线估计
        print("步骤二：多尺度表面法线估计...")
        multi_scale_normals = self._estimate_multiscale_normals(pcd, point_coords)
        
        # 步骤三：多尺度表面曲率估计
        print("步骤三：多尺度表面曲率估计...")
        multi_scale_curvatures = self._estimate_multiscale_curvatures(point_coords, pcd)
        
        # 步骤四：特征矩阵构建
        print("步骤四：构建增强特征矩阵...")
        enhanced_features = self._build_feature_matrix(
            organized_pointcloud, valid_mask, point_coords, 
            multi_scale_normals, multi_scale_curvatures, H, W
        )
        
        return enhanced_features
    
    def _build_kdtree_index(self, organized_pointcloud):
        """
        步骤一：将Numpy数组转换为Open3D格式并构建KD树
        """
        H, W, _ = organized_pointcloud.shape
        
        # 将有组织点云转换为无组织点云
        points = organized_pointcloud.reshape(-1, 3)
        
        # 过滤掉无效点（通常Z=0的点）
        valid_mask = (points[:, 2] > 1e-6) & (np.abs(points).sum(axis=1) > 1e-6)
        valid_points = points[valid_mask]
        
        if len(valid_points) < 10:
            print("警告：有效点数量过少，跳过处理")
            return None, valid_mask, None
        
        # 创建Open3D点云对象
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(valid_points)
        
        return pcd, valid_mask, valid_points
    
    def _estimate_multiscale_normals(self, pcd, point_coords):
        """
        步骤二：多尺度表面法线估计
        """
        multi_scale_normals = []
        
        for scale_params in self.multi_scale_params:
            # 使用Open3D估计法线
            pcd_copy = o3d.geometry.PointCloud(pcd)
            pcd_copy.estimate_normals(
                search_param=o3d.geometry.KDTreeSearchParamHybrid(
                    radius=scale_params['radius'], 
                    max_nn=scale_params['max_nn']
                )
            )
            
            # 法线方向一致性处理
            pcd_copy.orient_normals_consistent_tangent_plane(100)
            
            normals = np.asarray(pcd_copy.normals)
            multi_scale_normals.append(normals)
        
        return multi_scale_normals
    
    def _estimate_multiscale_curvatures(self, point_coords, pcd):
        """
        步骤三：多尺度表面曲率估计（只计算一个曲率特征：最小特征值/所有特征值总和）
        """
        multi_scale_curvatures = []
        
        # 构建KDTree用于邻域搜索
        kdtree = cKDTree(point_coords)
        
        for scale_params in self.multi_scale_params:
            curvatures = []
            
            for i, point in enumerate(point_coords):
                # 在指定半径内搜索邻近点
                neighbors = kdtree.query_ball_point(point, r=scale_params['radius'])
                
                if len(neighbors) < 6:  # 需要足够的邻近点
                    curvatures.append(0.0)  # 只保留一个曲率特征
                    continue
                
                # 获取邻近点坐标
                neighbor_points = point_coords[neighbors]
                
                # 使用PCA计算曲率
                curvature = self._compute_curvature_pca(neighbor_points)
                
                curvatures.append(curvature)
            
            multi_scale_curvatures.append(np.array(curvatures))
        
        return multi_scale_curvatures
    
    def _compute_curvature_pca(self, neighbor_points):
        """
        使用PCA方法计算点的曲率特征（只返回一个曲率特征：最小特征值/所有特征值总和）
        """
        if len(neighbor_points) < 3:
            return 0.0
        
        # 中心化点云
        centroid = np.mean(neighbor_points, axis=0)
        centered_points = neighbor_points - centroid
        
        # PCA分析
        try:
            pca = PCA(n_components=3)
            pca.fit(centered_points)
            eigenvalues = pca.explained_variance_
            
            # 确保特征值按降序排列
            eigenvalues = np.sort(eigenvalues)[::-1]
            
            # 避免除零错误
            eigenvalues = np.maximum(eigenvalues, 1e-10)
            
            # 计算曲率特征：最小特征值/所有特征值总和
            eigenvalue_sum = np.sum(eigenvalues)
            curvature = eigenvalues[2] / eigenvalue_sum if eigenvalue_sum > 1e-8 else 0.0
            
            return curvature
        
        except:
            return 0.0
    
    def _build_feature_matrix(self, original_pc, valid_mask, valid_points, 
                             multi_scale_normals, multi_scale_curvatures, H, W):
        """
        步骤四：构建最终的增强特征矩阵
        """
        total_points = H * W
        
        # 计算特征维度
        # 3 (xyz坐标) + 3*尺度数 (法线) + 1*尺度数 (曲率特征)
        feature_dim = 3 + len(self.multi_scale_params) * 4  # 3 + 尺度数 * (3法线 + 1曲率)
        
        # 初始化特征矩阵
        enhanced_features = np.zeros((total_points, feature_dim))
        
        # 填充原始坐标
        enhanced_features[:, :3] = original_pc.reshape(-1, 3)
        
        if valid_points is not None:
            # 当前特征索引
            feat_idx = 3
            
            # 填充多尺度法线特征
            for normals in multi_scale_normals:
                enhanced_features[valid_mask, feat_idx:feat_idx+3] = normals
                feat_idx += 3
            
            # 填充多尺度曲率特征（每个尺度1个曲率）
            for curvatures in multi_scale_curvatures:
                enhanced_features[valid_mask, feat_idx] = curvatures.flatten()
                feat_idx += 1
        
        print(f"增强特征矩阵构建完成: {enhanced_features.shape}")
        print(f"特征维度细节: 3(xyz) + {len(self.multi_scale_params)}*3(法线) + {len(self.multi_scale_params)}*1(曲率) = {feature_dim}")
        return enhanced_features
    
    def save_enhanced_features(self, enhanced_features, save_path):
        """
        保存增强特征到本地
        """
        np.save(save_path, enhanced_features)
        print(f"增强特征已保存到: {save_path}")
    
    def save_intrinsic_decomposition(self, albedo, shading, save_dir, filename_prefix=""):
        """
        保存本征分解结果（反照率图和着色图）到本地
        """
        os.makedirs(save_dir, exist_ok=True)
        
        def save_image(img_array, save_path):
            if isinstance(img_array, torch.Tensor):
                # 移除梯度信息并转换为numpy
                img_array = img_array.detach().cpu().numpy()
            
            # 如果有batch维度，取第一个
            if len(img_array.shape) == 4:
                img_array = img_array[0]
            
            # 如果是[C, H, W]格式，转换为[H, W, C]
            if len(img_array.shape) == 3 and img_array.shape[0] == 3:
                img_array = img_array.transpose(1, 2, 0)
            
            # 确保数值在[0,1]范围内
            img_array = np.clip(img_array, 0, 1)
            
            # 转换为uint8并保存
            img_uint8 = (img_array * 255).astype(np.uint8)
            from PIL import Image
            Image.fromarray(img_uint8).save(save_path)
        
        # 保存反照率图
        albedo_path = os.path.join(save_dir, f"{filename_prefix}albedo.png")
        save_image(albedo, albedo_path)
        
        # 保存着色图
        shading_path = os.path.join(save_dir, f"{filename_prefix}shading.png")
        save_image(shading, shading_path)
        
        print(f"本征分解结果已保存到: {save_dir}")
        print(f"反照率图: {albedo_path}")
        print(f"着色图: {shading_path}")
    
    def forward(self, rgb_image, pointcloud_path=None, organized_pointcloud=None):
        """
        主前向传播逻辑
        输入: 
            - rgb_image: [B, 3, H, W] 或者 numpy array [H, W, 3]
            - pointcloud_path: .tiff文件路径 或者
            - organized_pointcloud: 直接的有组织点云数据 [H, W, 3]
        输出: 
            - albedo_map: 反照率图
            - shading_map: 着色图  
            - enhanced_pointcloud: 增强型点云特征
        """
        # 处理RGB输入
        if isinstance(rgb_image, np.ndarray):
            if len(rgb_image.shape) == 3:  # [H, W, 3]
                rgb_image = torch.from_numpy(rgb_image).permute(2, 0, 1).unsqueeze(0).float()
            elif len(rgb_image.shape) == 4:  # [B, H, W, 3]
                rgb_image = torch.from_numpy(rgb_image).permute(0, 3, 1, 2).float()
        
        # RGB本征分解
        albedo_map, shading_map = self.rgb_intrinsic_decomposition(rgb_image)
        
        # 3D点云特征提取
        if pointcloud_path is not None:
            # 从文件加载
            organized_pc = self.load_and_preprocess_pointcloud(pointcloud_path)
        elif organized_pointcloud is not None:
            # 直接使用输入的点云
            organized_pc = organized_pointcloud
        else:
            raise ValueError("必须提供pointcloud_path或organized_pointcloud参数")
        
        # 提取3D物理特征
        enhanced_pointcloud = self.extract_3d_physical_features(organized_pc)
        
        return {
            'albedo': albedo_map,
            'shading': shading_map,
            'enhanced_pointcloud': enhanced_pointcloud,
            'original_pointcloud': organized_pc
        }


def test_physical_decomposition():
    """
    测试物理信息提取模块
    """
    # 配置参数
    config = {
        'multi_scale_params': [
            {'radius': 0.02, 'max_nn': 30},
            {'radius': 0.05, 'max_nn': 50}, 
            {'radius': 0.1, 'max_nn': 100},
        ]
    }
    
    # 创建模块实例
    decomposer = PhysicalDecomposition(config)
    
    # 创建测试数据
    rgb_image = torch.randn(1, 3, 224, 224)
    
    # 创建模拟的有组织点云数据
    H, W = 224, 224
    organized_pc = np.random.rand(H, W, 3) * 2 - 1  # [-1, 1]范围的点云
    # 将部分点设为无效（模拟真实情况）
    mask = np.random.rand(H, W) > 0.2
    organized_pc[~mask] = 0
    
    # 测试前向传播
    print("开始测试物理信息提取...")
    results = decomposer(rgb_image, organized_pointcloud=organized_pc)
    
    print("测试完成！")
    print(f"反照率图形状: {results['albedo'].shape}")
    print(f"着色图形状: {results['shading'].shape}")
    print(f"增强点云特征形状: {results['enhanced_pointcloud'].shape}")
    
    # 保存特征
    save_path = "test_enhanced_features.npy"
    decomposer.save_enhanced_features(results['enhanced_pointcloud'], save_path)
    
    return results


if __name__ == "__main__":
    test_physical_decomposition()