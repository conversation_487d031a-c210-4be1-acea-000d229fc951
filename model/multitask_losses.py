#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Multi-task Loss Functions for IA-CRF Network
Implements comprehensive loss functions for end-to-end training

Author: IA-CRF Project  
Date: 2025-08-27
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Optional, List
import numpy as np


class ReconstructionLoss(nn.Module):
    """
    Reconstruction loss for 2D images (albedo and shading)
    Combines L1 loss with perceptual loss
    """
    
    def __init__(self, l1_weight=1.0, ssim_weight=0.3, perceptual_weight=0.1):
        super(ReconstructionLoss, self).__init__()
        
        self.l1_weight = l1_weight
        self.ssim_weight = ssim_weight
        self.perceptual_weight = perceptual_weight
        
        # L1 Loss
        self.l1_loss = nn.L1Loss()
        
        # SSIM Loss
        self.ssim_loss = SSIMLoss()
        
        # Simple perceptual loss using pre-trained features
        if perceptual_weight > 0:
            self.perceptual_loss = PerceptualLoss()
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> <PERSON><PERSON>[torch.Tensor, Dict]:
        """
        Compute reconstruction loss
        
        Args:
            pred: Predicted image [B, C, H, W]
            target: Target image [B, C, H, W]
            
        Returns:
            loss: Total reconstruction loss
            loss_dict: Individual loss components
        """
        # L1 Loss
        l1 = self.l1_loss(pred, target)
        
        # SSIM Loss
        ssim = self.ssim_loss(pred, target)
        
        # Total loss
        total_loss = self.l1_weight * l1 + self.ssim_weight * ssim
        
        loss_dict = {
            'l1_loss': l1.item(),
            'ssim_loss': ssim.item()
        }
        
        # Perceptual loss (optional)
        if self.perceptual_weight > 0 and hasattr(self, 'perceptual_loss'):
            perceptual = self.perceptual_loss(pred, target)
            total_loss += self.perceptual_weight * perceptual
            loss_dict['perceptual_loss'] = perceptual.item()
        
        return total_loss, loss_dict


class SSIMLoss(nn.Module):
    """
    Structural Similarity Index Loss
    """
    
    def __init__(self, window_size=11, reduction='mean'):
        super(SSIMLoss, self).__init__()
        self.window_size = window_size
        self.reduction = reduction
        
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Compute SSIM loss (1 - SSIM)
        
        Args:
            pred: Predicted image [B, C, H, W]
            target: Target image [B, C, H, W]
            
        Returns:
            ssim_loss: 1 - SSIM value
        """
        ssim_value = self._ssim(pred, target)
        return 1 - ssim_value
    
    def _ssim(self, img1: torch.Tensor, img2: torch.Tensor) -> torch.Tensor:
        """Compute SSIM between two images"""
        # Convert to grayscale if RGB
        if img1.size(1) == 3:
            img1 = 0.299 * img1[:, 0:1] + 0.587 * img1[:, 1:2] + 0.114 * img1[:, 2:3]
            img2 = 0.299 * img2[:, 0:1] + 0.587 * img2[:, 1:2] + 0.114 * img2[:, 2:3]
        
        # Compute means
        mu1 = F.avg_pool2d(img1, self.window_size, stride=1, padding=self.window_size//2)
        mu2 = F.avg_pool2d(img2, self.window_size, stride=1, padding=self.window_size//2)
        
        mu1_sq = mu1.pow(2)
        mu2_sq = mu2.pow(2)
        mu1_mu2 = mu1 * mu2
        
        # Compute variances and covariance
        sigma1_sq = F.avg_pool2d(img1 * img1, self.window_size, stride=1, padding=self.window_size//2) - mu1_sq
        sigma2_sq = F.avg_pool2d(img2 * img2, self.window_size, stride=1, padding=self.window_size//2) - mu2_sq
        sigma12 = F.avg_pool2d(img1 * img2, self.window_size, stride=1, padding=self.window_size//2) - mu1_mu2
        
        # SSIM constants
        C1 = 0.01 ** 2
        C2 = 0.03 ** 2
        
        # Compute SSIM
        ssim_map = ((2 * mu1_mu2 + C1) * (2 * sigma12 + C2)) / ((mu1_sq + mu2_sq + C1) * (sigma1_sq + sigma2_sq + C2))
        
        if self.reduction == 'mean':
            return ssim_map.mean()
        else:
            return ssim_map


class PerceptualLoss(nn.Module):
    """
    Simple perceptual loss using VGG features
    """
    
    def __init__(self):
        super(PerceptualLoss, self).__init__()
        
        # Use a simple conv layer as feature extractor to avoid VGG dependency
        self.feature_extractor = nn.Sequential(
            nn.Conv2d(3, 64, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(64, 128, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.MaxPool2d(2),
            nn.Conv2d(128, 256, 3, padding=1),
            nn.ReLU(inplace=True)
        )
        
        # Freeze parameters
        for param in self.feature_extractor.parameters():
            param.requires_grad = False
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Compute perceptual loss using feature differences
        """
        pred_features = self.feature_extractor(pred)
        target_features = self.feature_extractor(target)
        
        return F.mse_loss(pred_features, target_features)


class ChamferDistance(nn.Module):
    """
    Chamfer Distance for 3D point cloud reconstruction
    Memory-efficient implementation
    """
    
    def __init__(self, use_efficient=True):
        super(ChamferDistance, self).__init__()
        self.use_efficient = use_efficient
    
    def forward(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """
        Compute Chamfer distance between two point clouds
        
        Args:
            pred: Predicted point cloud [B, N, 3]
            target: Target point cloud [B, M, 3]
            
        Returns:
            chamfer_loss: Chamfer distance
        """
        if self.use_efficient:
            return self._chamfer_distance_efficient(pred, target)
        else:
            return self._chamfer_distance_standard(pred, target)
    
    def _chamfer_distance_efficient(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """Memory-efficient Chamfer distance computation"""
        B, N, _ = pred.shape
        B, M, _ = target.shape
        
        # Compute distances in chunks to save memory
        chunk_size = min(1000, N, M)
        
        dist_pred_to_target = []
        dist_target_to_pred = []
        
        # Pred to target
        for i in range(0, N, chunk_size):
            end_i = min(i + chunk_size, N)
            pred_chunk = pred[:, i:end_i]  # [B, chunk, 3]
            
            # Compute distances to all target points
            distances = torch.cdist(pred_chunk, target)  # [B, chunk, M]
            min_distances, _ = torch.min(distances, dim=2)  # [B, chunk]
            dist_pred_to_target.append(min_distances)
        
        # Target to pred
        for i in range(0, M, chunk_size):
            end_i = min(i + chunk_size, M)
            target_chunk = target[:, i:end_i]  # [B, chunk, 3]
            
            # Compute distances to all pred points
            distances = torch.cdist(target_chunk, pred)  # [B, chunk, N]
            min_distances, _ = torch.min(distances, dim=2)  # [B, chunk]
            dist_target_to_pred.append(min_distances)
        
        # Concatenate and compute means
        dist_pred_to_target = torch.cat(dist_pred_to_target, dim=1)
        dist_target_to_pred = torch.cat(dist_target_to_pred, dim=1)
        
        chamfer_loss = torch.mean(dist_pred_to_target) + torch.mean(dist_target_to_pred)
        
        return chamfer_loss
    
    def _chamfer_distance_standard(self, pred: torch.Tensor, target: torch.Tensor) -> torch.Tensor:
        """Standard Chamfer distance computation"""
        # Expand dimensions for broadcasting
        pred_expanded = pred.unsqueeze(2)      # [B, N, 1, 3]
        target_expanded = target.unsqueeze(1)  # [B, 1, M, 3]
        
        # Compute all pairwise distances
        distances = torch.sum((pred_expanded - target_expanded) ** 2, dim=3)  # [B, N, M]
        
        # Find minimum distances
        dist_pred_to_target = torch.min(distances, dim=2)[0]  # [B, N]
        dist_target_to_pred = torch.min(distances, dim=1)[0]  # [B, M]
        
        # Compute Chamfer distance
        chamfer_loss = torch.mean(dist_pred_to_target) + torch.mean(dist_target_to_pred)
        
        return chamfer_loss


class KLDivergenceLoss(nn.Module):
    """
    KL Divergence loss for VAE latent space regularization
    """
    
    def __init__(self):
        super(KLDivergenceLoss, self).__init__()
    
    def forward(self, mu: torch.Tensor, logvar: torch.Tensor) -> torch.Tensor:
        """
        Compute KL divergence loss
        
        Args:
            mu: Mean of latent distribution [B, latent_dim]
            logvar: Log variance of latent distribution [B, latent_dim]
            
        Returns:
            kl_loss: KL divergence loss
        """
        kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())
        kl_loss = kl_loss / mu.size(0)  # Normalize by batch size
        
        return kl_loss


class CrossModalConsistencyLoss(nn.Module):
    """
    Cross-modal consistency loss for enforcing physical consistency
    between shading and 3D geometry features
    """
    
    def __init__(self, loss_type='cosine'):
        super(CrossModalConsistencyLoss, self).__init__()
        
        self.loss_type = loss_type
        
        if loss_type == 'cosine':
            self.similarity = nn.CosineSimilarity(dim=1)
        elif loss_type == 'mse':
            self.mse_loss = nn.MSELoss()
        else:
            raise ValueError(f"Unsupported loss type: {loss_type}")
    
    def forward(self, predicted_features: torch.Tensor, 
                target_features: torch.Tensor) -> torch.Tensor:
        """
        Compute cross-modal consistency loss
        
        Args:
            predicted_features: Features predicted from one modality
            target_features: True features from target modality
            
        Returns:
            consistency_loss: Cross-modal consistency loss
        """
        if self.loss_type == 'cosine':
            # Cosine similarity loss (maximize similarity)
            cos_sim = self.similarity(predicted_features, target_features)
            consistency_loss = 1 - cos_sim.mean()
        elif self.loss_type == 'mse':
            # MSE loss
            consistency_loss = self.mse_loss(predicted_features, target_features)
        
        return consistency_loss


class IACRFMultiTaskLoss(nn.Module):
    """
    Comprehensive multi-task loss for IA-CRF Network
    Combines all individual losses with proper weighting
    """
    
    def __init__(self, config: Dict):
        super(IACRFMultiTaskLoss, self).__init__()
        
        loss_config = config['loss']
        
        # Initialize individual loss functions
        self.albedo_recon_loss = ReconstructionLoss(
            l1_weight=loss_config['albedo_recon']['l1_weight'],
            ssim_weight=loss_config['albedo_recon']['ssim_weight'],
            perceptual_weight=loss_config['albedo_recon'].get('perceptual_weight', 0.0)
        )
        
        self.shading_recon_loss = ReconstructionLoss(
            l1_weight=loss_config['shading_recon']['l1_weight'],
            ssim_weight=loss_config['shading_recon']['ssim_weight'],
            perceptual_weight=loss_config['shading_recon'].get('perceptual_weight', 0.0)
        )
        
        self.pointcloud_recon_loss = ChamferDistance(
            use_efficient=loss_config['pointcloud_recon']['use_efficient']
        )
        
        self.kl_loss = KLDivergenceLoss()
        
        self.crossmodal_loss = CrossModalConsistencyLoss(
            loss_type=loss_config['crossmodal']['loss_type']
        )
        
        # Loss weights
        self.loss_weights = loss_config['weights']
        
        print("IA-CRF Multi-task Loss initialized")
        print(f"  Loss weights: {self.loss_weights}")
    
    def forward(self, predictions: Dict, targets: Dict) -> Tuple[List[torch.Tensor], List[str], Dict]:
        """
        Compute all multi-task losses
        
        Args:
            predictions: Dictionary of model predictions
            targets: Dictionary of target values
            
        Returns:
            losses: List of individual losses
            loss_names: Names of losses
            loss_details: Detailed loss information
        """
        losses = []
        loss_names = []
        loss_details = {}
        
        # 检查关键输入
        required_pred_keys = ['reconstructed_albedo', 'reconstructed_shading', 'reconstructed_3d', 'vae_mu', 'vae_logvar']
        required_target_keys = ['clean_albedo', 'clean_shading', 'clean_pointcloud']
        
        for key in required_pred_keys:
            if key not in predictions:
                print(f"ERROR: Missing prediction key: {key}")
                return [torch.tensor(0.0)], ['error'], {'error': 'missing_predictions'}
            elif predictions[key] is None:
                print(f"ERROR: Prediction {key} is None")
                return [torch.tensor(0.0)], ['error'], {'error': 'none_predictions'}
                
        for key in required_target_keys:
            if key not in targets:
                print(f"ERROR: Missing target key: {key}")
                return [torch.tensor(0.0)], ['error'], {'error': 'missing_targets'}
            elif targets[key] is None:
                print(f"ERROR: Target {key} is None")
                return [torch.tensor(0.0)], ['error'], {'error': 'none_targets'}
        
        # 1. Albedo reconstruction loss
        try:
            pred_albedo = predictions['reconstructed_albedo']
            target_albedo = targets['clean_albedo']
            
            albedo_loss, albedo_details = self.albedo_recon_loss(pred_albedo, target_albedo)
            losses.append(albedo_loss)
            loss_names.append('albedo_recon')
            loss_details.update({f'albedo_{k}': v for k, v in albedo_details.items()})
        except Exception as e:
            print(f"ERROR in albedo loss: {e}")
            losses.append(torch.tensor(0.0))
            loss_names.append('albedo_recon')
        
        # 2. Shading reconstruction loss
        try:
            pred_shading = predictions['reconstructed_shading']
            target_shading = targets['clean_shading']
            
            shading_loss, shading_details = self.shading_recon_loss(pred_shading, target_shading)
            losses.append(shading_loss)
            loss_names.append('shading_recon')
            loss_details.update({f'shading_{k}': v for k, v in shading_details.items()})
        except Exception as e:
            print(f"ERROR in shading loss: {e}")
            losses.append(torch.tensor(0.0))
            loss_names.append('shading_recon')
        
        # 3. Point cloud reconstruction loss (Chamfer distance)
        try:
            pred_3d = predictions['reconstructed_3d']  # [B, N, 3] VAE输出的XYZ坐标
            target_enhanced = targets['clean_pointcloud']  # [B, M, 7] 增强几何特征
            
            # 从7维增强特征中提取XYZ坐标（前3维）用于Chamfer距离计算
            target_3d = target_enhanced[:, :, :3]  # [B, M, 3]
            
            pointcloud_loss = self.pointcloud_recon_loss(pred_3d, target_3d)
            losses.append(pointcloud_loss)
            loss_names.append('pointcloud_recon')
            loss_details['chamfer_distance'] = pointcloud_loss.item()
        except Exception as e:
            print(f"ERROR in pointcloud loss: {e}")
            losses.append(torch.tensor(0.0))
            loss_names.append('pointcloud_recon')
        
        # 4. KL divergence loss
        try:
            mu = predictions['vae_mu']
            logvar = predictions['vae_logvar']
            
            kl_loss = self.kl_loss(mu, logvar)
            losses.append(kl_loss)
            loss_names.append('kl_divergence')
            loss_details['kl_divergence'] = kl_loss.item()
        except Exception as e:
            print(f"ERROR in KL loss: {e}")
            losses.append(torch.tensor(0.0))
            loss_names.append('kl_divergence')
        
        # 5. Cross-modal consistency losses (bidirectional)
        try:
            if ('shading_features' in predictions and 'predicted_shading_features' in predictions and
                'predicted_vae_latent' in predictions and 'vae_latent' in predictions):
                
                # Shading -> VAE latent consistency
                shading_features_flat = predictions['shading_features'].view(
                    predictions['shading_features'].size(0), -1
                )
                vae_to_shading_consistency = self.crossmodal_loss(
                    predictions['predicted_shading_features'].view(predictions['predicted_shading_features'].size(0), -1),
                    shading_features_flat
                )
                
                # VAE latent -> Shading features consistency  
                shading_to_vae_consistency = self.crossmodal_loss(
                    predictions['predicted_vae_latent'],
                    predictions['vae_latent']
                )
                
                crossmodal_loss = (vae_to_shading_consistency + shading_to_vae_consistency) / 2
                losses.append(crossmodal_loss)
                loss_names.append('crossmodal_consistency')
                loss_details['crossmodal_consistency'] = crossmodal_loss.item()
                loss_details['vae_to_shading_consistency'] = vae_to_shading_consistency.item()
                loss_details['shading_to_vae_consistency'] = shading_to_vae_consistency.item()
            else:
                losses.append(torch.tensor(0.0))
                loss_names.append('crossmodal_consistency')
        except Exception as e:
            print(f"ERROR in crossmodal loss: {e}")
            losses.append(torch.tensor(0.0))
            loss_names.append('crossmodal_consistency')
        
        return losses, loss_names, loss_details


def test_multitask_loss():
    """
    Test the multi-task loss functions
    """
    # Test configuration
    config = {
        'loss': {
            'albedo_recon': {
                'l1_weight': 1.0,
                'ssim_weight': 0.3,
                'perceptual_weight': 0.1
            },
            'shading_recon': {
                'l1_weight': 1.0,
                'ssim_weight': 0.3,
                'perceptual_weight': 0.1
            },
            'pointcloud_recon': {
                'use_efficient': True
            },
            'crossmodal': {
                'loss_type': 'cosine'
            },
            'weights': [1.0, 1.0, 0.5, 0.1, 0.5]
        }
    }
    
    # Create loss function
    loss_fn = IACRFMultiTaskLoss(config)
    
    # Create test data
    batch_size = 2
    
    predictions = {
        'reconstructed_albedo': torch.rand(batch_size, 3, 224, 224),
        'reconstructed_shading': torch.rand(batch_size, 3, 224, 224),
        'reconstructed_3d': torch.rand(batch_size, 2048, 3),
        'vae_mu': torch.randn(batch_size, 128),
        'vae_logvar': torch.randn(batch_size, 128),
        'shading_features': torch.rand(batch_size, 256, 14, 14),
        'vae_latent': torch.randn(batch_size, 128),
        'predicted_shading_features': torch.rand(batch_size, 256, 14, 14),
        'predicted_vae_latent': torch.randn(batch_size, 128)
    }
    
    targets = {
        'clean_albedo': torch.rand(batch_size, 3, 224, 224),
        'clean_shading': torch.rand(batch_size, 3, 224, 224),
        'clean_pointcloud': torch.rand(batch_size, 2048, 3)
    }
    
    # Compute losses
    losses, loss_names, loss_details = loss_fn(predictions, targets)
    
    print("Multi-task Loss Test Results:")
    for i, (loss, name) in enumerate(zip(losses, loss_names)):
        print(f"  {name}: {loss.item():.6f}")
    
    print("\nDetailed Loss Components:")
    for key, value in loss_details.items():
        print(f"  {key}: {value:.6f}")
    
    print("Test completed successfully!")


if __name__ == "__main__":
    test_multitask_loss()