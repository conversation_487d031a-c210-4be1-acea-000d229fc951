#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
3D Point Cloud VAE Model
Implements a Variational Autoencoder for 3D point cloud data with:
- PointNet++ encoder for extracting geometric features
- FoldingNet decoder for point cloud reconstruction
- Designed for learning internal geometric priors of specific object categories
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Tuple, Optional


class PointNetFeatureExtractor(nn.Module):
    """
    Basic PointNet feature extractor for point cloud processing
    """
    def __init__(self, input_dim: int = 3, feature_dim: int = 1024):
        super(PointNetFeatureExtractor, self).__init__()
        
        self.conv1 = nn.Conv1d(input_dim, 64, 1)
        self.conv2 = nn.Conv1d(64, 128, 1)
        self.conv3 = nn.Conv1d(128, feature_dim, 1)
        
        self.bn1 = nn.BatchNorm1d(64)
        self.bn2 = nn.BatchNorm1d(128)
        self.bn3 = nn.BatchNorm1d(feature_dim)
        
    def forward(self, x):
        """
        Args:
            x: [B, N, 3] point cloud
        Returns:
            features: [B, feature_dim] global features
        """
        # x: [B, N, 3] -> [B, 3, N]
        x = x.transpose(2, 1)
        
        x = F.relu(self.bn1(self.conv1(x)))
        x = F.relu(self.bn2(self.conv2(x)))
        x = self.bn3(self.conv3(x))
        
        # Global max pooling
        x = torch.max(x, 2)[0]  # [B, feature_dim]
        
        return x


class PointNet3DEncoder(nn.Module):
    """
    3D Point Cloud Encoder using PointNet++ architecture
    Outputs mean and log_variance for VAE latent distribution
    """
    def __init__(self, 
                 input_dim: int = 3,
                 feature_dim: int = 1024,
                 latent_dim: int = 256):
        super(PointNet3DEncoder, self).__init__()
        
        self.feature_extractor = PointNetFeatureExtractor(input_dim, feature_dim)
        
        # Latent distribution parameters
        self.fc_mu = nn.Linear(feature_dim, latent_dim)
        self.fc_logvar = nn.Linear(feature_dim, latent_dim)
        
        self.latent_dim = latent_dim
        
    def forward(self, x):
        """
        Args:
            x: [B, N, 3] point cloud
        Returns:
            mu: [B, latent_dim] mean of latent distribution
            logvar: [B, latent_dim] log variance of latent distribution
        """
        features = self.feature_extractor(x)  # [B, feature_dim]
        
        mu = self.fc_mu(features)
        logvar = self.fc_logvar(features)
        
        return mu, logvar


class FoldingNetDecoder(nn.Module):
    """
    FoldingNet-style decoder for point cloud reconstruction
    Folds a 2D grid into 3D point cloud based on latent code
    """
    def __init__(self, 
                 latent_dim: int = 256,
                 output_points: int = 2048):
        super(FoldingNetDecoder, self).__init__()
        
        self.latent_dim = latent_dim
        self.output_points = output_points
        
        # Create 2D grid
        self.register_buffer('grid', self._create_grid())
        
        # Folding layers
        self.fold1 = nn.Sequential(
            nn.Linear(latent_dim + 2, 512),
            nn.ReLU(),
            nn.Linear(512, 512),
            nn.ReLU(),
            nn.Linear(512, 3)
        )
        
        self.fold2 = nn.Sequential(
            nn.Linear(latent_dim + 3, 512),
            nn.ReLU(),
            nn.Linear(512, 512),
            nn.ReLU(),
            nn.Linear(512, 3)
        )
        
    def _create_grid(self):
        """Create 2D grid for folding"""
        # Calculate grid size to get close to output_points
        grid_side = int(np.sqrt(self.output_points))
        
        # Create square grid
        x = np.linspace(-1, 1, grid_side)
        y = np.linspace(-1, 1, grid_side)
        grid_x, grid_y = np.meshgrid(x, y)
        
        grid = np.stack([grid_x.flatten(), grid_y.flatten()], axis=1)
        
        # If we have fewer points than needed, duplicate some points
        if len(grid) < self.output_points:
            shortage = self.output_points - len(grid)
            # Randomly duplicate some existing points
            indices = np.random.choice(len(grid), shortage, replace=True)
            extra_points = grid[indices]
            grid = np.concatenate([grid, extra_points], axis=0)
        
        # Ensure exact number of points
        grid = grid[:self.output_points]
        
        return torch.FloatTensor(grid)
    
    def forward(self, z):
        """
        Args:
            z: [B, latent_dim] latent code
        Returns:
            points: [B, output_points, 3] reconstructed point cloud
        """
        batch_size = z.size(0)
        
        # Expand latent code for each grid point (use repeat instead of expand)
        z_expanded = z.unsqueeze(1).repeat(1, self.output_points, 1)  # [B, N, latent_dim]
        
        # Expand grid for each batch (use repeat instead of expand)
        grid_expanded = self.grid.unsqueeze(0).repeat(batch_size, 1, 1)  # [B, N, 2]
        
        # First folding
        fold1_input = torch.cat([z_expanded, grid_expanded], dim=2)  # [B, N, latent_dim+2]
        fold1_output = self.fold1(fold1_input)  # [B, N, 3]
        
        # Second folding
        fold2_input = torch.cat([z_expanded, fold1_output], dim=2)  # [B, N, latent_dim+3]
        points = self.fold2(fold2_input)  # [B, N, 3]
        
        return points


class PointCloudVAE(nn.Module):
    """
    Complete 3D Point Cloud Variational Autoencoder
    Combines PointNet++ encoder with FoldingNet decoder
    Supports both 3D (XYZ) and enhanced features (XYZ+Normal+Curvature)
    """
    def __init__(self,
                 input_dim: int = 3,
                 latent_dim: int = 256,
                 feature_dim: int = 1024,
                 output_points: int = 2048):
        super(PointCloudVAE, self).__init__()
        
        self.encoder = PointNet3DEncoder(input_dim, feature_dim, latent_dim)
        self.decoder = FoldingNetDecoder(latent_dim, output_points=output_points)
        
        self.latent_dim = latent_dim
        self.input_dim = input_dim
        
    def load_pretrained_weights(self, pretrained_path: str, strict: bool = False):
        """
        加载预训练权重，跳过输入层不匹配的权重
        
        Args:
            pretrained_path: 预训练模型路径
            strict: 是否严格匹配所有权重
        """
        #print(f"正在从 {pretrained_path} 加载预训练权重...")
        
        # 加载预训练权重
        try:
            checkpoint = torch.load(pretrained_path, map_location='cpu')
            if 'model_state_dict' in checkpoint:
                pretrained_dict = checkpoint['model_state_dict']
            else:
                pretrained_dict = checkpoint
        except Exception as e:
            print(f"⚠️ 无法加载预训练权重: {e}")
            return
        
        # 获取当前模型的状态字典
        model_dict = self.state_dict()
        
        # 筛选匹配的权重
        matched_dict = {}
        skipped_keys = []
        
        for key, value in pretrained_dict.items():
            if key in model_dict:
                # 检查形状是否匹配
                print(model_dict[key].shape) # 64 * 7 *1
                print(value.shape) # 64 * 3 * 1
                if model_dict[key].shape == value.shape:
                    matched_dict[key] = value
                else:
                    skipped_keys.append(f"{key}: {value.shape} -> {model_dict[key].shape}")
            else:
                skipped_keys.append(f"{key}: 不存在于当前模型")
        
        # 加载匹配的权重
        model_dict.update(matched_dict)
        self.load_state_dict(model_dict)
        
        print(f"✅ 成功加载 {len(matched_dict)} 个匹配的权重参数")
        if skipped_keys:
            print(f"⚠️ 跳过 {len(skipped_keys)} 个不匹配的权重参数:")
            for key in skipped_keys[:5]:  # 只显示前5个
                print(f"  - {key}")
            if len(skipped_keys) > 5:
                print(f"  ... 和其他 {len(skipped_keys) - 5} 个参数")
        
    def reparameterize(self, mu, logvar):
        """
        Reparameterization trick for VAE
        """
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std
    
    def forward(self, x):
        """
        Args:
            x: [B, N, 3] input point cloud
        Returns:
            results: Dictionary containing:
                - reconstructed: [B, output_points, 3] reconstructed point cloud
                - mu: [B, latent_dim] mean of latent distribution
                - logvar: [B, latent_dim] log variance of latent distribution
                - latent_code: [B, latent_dim] sampled latent code
        """
        # Encode
        mu, logvar = self.encoder(x)
        
        # Reparameterize
        z = self.reparameterize(mu, logvar)
        
        # Decode
        reconstructed = self.decoder(z)
        
        # 返回字典格式，与IACRFNetwork期望的格式一致
        return {
            'reconstructed': reconstructed,
            'mu': mu,
            'logvar': logvar,
            'latent_code': z
        }
    
    def encode(self, x):
        """Encode point cloud to latent distribution parameters"""
        return self.encoder(x)
    
    def decode(self, z):
        """Decode latent code to point cloud"""
        return self.decoder(z)
    
    def sample(self, num_samples: int, device: torch.device):
        """Sample new point clouds from prior distribution"""
        z = torch.randn(num_samples, self.latent_dim, device=device)
        return self.decode(z)


def test_pointcloud_vae():
    """Test function for the PointCloud VAE model"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Create model
    model = PointCloudVAE(
        input_dim=3,
        latent_dim=256,
        feature_dim=1024,
        output_points=2048
    ).to(device)
    
    # Create dummy input
    batch_size = 4
    num_points = 2048
    input_points = torch.randn(batch_size, num_points, 3).to(device)
    
    print(f"Model created on {device}")
    print(f"Input shape: {input_points.shape}")
    
    # Forward pass
    with torch.no_grad():
        results = model(input_points)
        reconstructed = results['reconstructed']
        mu = results['mu']
        logvar = results['logvar']
        
    print(f"Reconstructed shape: {reconstructed.shape}")
    print(f"Latent mu shape: {mu.shape}")
    print(f"Latent logvar shape: {logvar.shape}")
    
    # Test sampling
    with torch.no_grad():
        samples = model.sample(2, device)
    print(f"Sampled shape: {samples.shape}")
    
    print("PointCloud VAE test passed!")


if __name__ == "__main__":
    test_pointcloud_vae()