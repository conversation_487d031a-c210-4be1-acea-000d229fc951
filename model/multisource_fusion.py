#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Multi-source Feature Fusion Module for IA-CRF Network
Handles fusion of features from different modalities and sources

Author: IA-CRF Project
Date: 2025-08-27
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional


class AttentionFusion(nn.Module):
    """
    Attention-based feature fusion mechanism
    Learns to weight different feature sources dynamically
    """
    
    def __init__(self, feature_dim: int, num_sources: int, hidden_dim: int = 128):
        super(AttentionFusion, self).__init__()
        
        self.feature_dim = feature_dim
        self.num_sources = num_sources
        
        # Attention network
        self.attention_net = nn.Sequential(
            nn.Linear(feature_dim * num_sources, hidden_dim),
            nn.ReLU(inplace=True),
            nn.Linear(hidden_dim, num_sources),
            nn.Softmax(dim=1)
        )
        
        # Feature projection
        self.feature_proj = nn.ModuleList([
            nn.Linear(feature_dim, feature_dim) for _ in range(num_sources)
        ])
        
    def forward(self, features: List[torch.Tensor]) -> torch.Tensor:
        """
        Fuse features using attention mechanism
        
        Args:
            features: List of feature tensors [B, feature_dim]
            
        Returns:
            fused_features: Attention-weighted fused features [B, feature_dim]
        """
        if len(features) != self.num_sources:
            raise ValueError(f"Expected {self.num_sources} features, got {len(features)}")
        
        batch_size = features[0].size(0)
        
        # Project features
        projected_features = []
        for i, feat in enumerate(features):
            projected = self.feature_proj[i](feat)
            projected_features.append(projected)
        
        # Concatenate for attention computation
        concat_features = torch.cat(projected_features, dim=1)  # [B, feature_dim * num_sources]
        
        # Compute attention weights
        attention_weights = self.attention_net(concat_features)  # [B, num_sources]
        
        # Apply attention weights
        fused_features = torch.zeros_like(projected_features[0])
        for i, feat in enumerate(projected_features):
            weight = attention_weights[:, i:i+1]  # [B, 1]
            fused_features += weight * feat
        
        return fused_features


class SpatialFeatureFusion(nn.Module):
    """
    Spatial feature fusion for 2D feature maps
    Handles features with spatial dimensions
    """
    
    def __init__(self, feature_dim: int, spatial_size: Tuple[int, int] = (14, 14)):
        super(SpatialFeatureFusion, self).__init__()
        
        self.feature_dim = feature_dim
        self.spatial_size = spatial_size
        
        # Spatial attention
        self.spatial_attention = nn.Sequential(
            nn.Conv2d(feature_dim * 2, feature_dim, 3, padding=1),
            nn.BatchNorm2d(feature_dim),
            nn.ReLU(inplace=True),
            nn.Conv2d(feature_dim, 1, 1),
            nn.Sigmoid()
        )
        
        # Channel attention
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(feature_dim * 2, feature_dim // 4, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(feature_dim // 4, feature_dim, 1),
            nn.Sigmoid()
        )
        
        # Feature fusion
        self.fusion_conv = nn.Sequential(
            nn.Conv2d(feature_dim * 2, feature_dim, 3, padding=1),
            nn.BatchNorm2d(feature_dim),
            nn.ReLU(inplace=True),
            nn.Conv2d(feature_dim, feature_dim, 1)
        )
        
    def forward(self, feat1: torch.Tensor, feat2: torch.Tensor) -> torch.Tensor:
        """
        Fuse two spatial feature maps
        
        Args:
            feat1: First feature map [B, C, H, W]
            feat2: Second feature map [B, C, H, W]
            
        Returns:
            fused_features: Spatially fused features [B, C, H, W]
        """
        # Concatenate features
        concat_features = torch.cat([feat1, feat2], dim=1)  # [B, 2C, H, W]
        
        # Spatial attention
        spatial_att = self.spatial_attention(concat_features)  # [B, 1, H, W]
        
        # Channel attention
        channel_att = self.channel_attention(concat_features)  # [B, C, 1, 1]
        
        # Apply attention to original features
        att_feat1 = feat1 * spatial_att * channel_att
        att_feat2 = feat2 * spatial_att * channel_att
        
        # Fusion
        attended_concat = torch.cat([att_feat1, att_feat2], dim=1)
        fused_features = self.fusion_conv(attended_concat)
        
        # Residual connection
        fused_features = fused_features + feat1 + feat2
        
        return fused_features


class CrossModalAlignment(nn.Module):
    """
    Cross-modal feature alignment module
    Aligns features from different modalities to a common space
    """
    
    def __init__(self, modality_dims: Dict[str, int], aligned_dim: int = 256):
        super(CrossModalAlignment, self).__init__()
        
        self.modality_dims = modality_dims
        self.aligned_dim = aligned_dim
        
        # Alignment networks for each modality
        self.alignment_nets = nn.ModuleDict()
        for modality, dim in modality_dims.items():
            self.alignment_nets[modality] = nn.Sequential(
                nn.Linear(dim, aligned_dim * 2),
                nn.ReLU(inplace=True),
                nn.Dropout(0.2),
                nn.Linear(aligned_dim * 2, aligned_dim),
                nn.LayerNorm(aligned_dim)
            )
        
        # Cross-modal consistency projector
        self.consistency_proj = nn.Sequential(
            nn.Linear(aligned_dim, aligned_dim),
            nn.ReLU(inplace=True),
            nn.Linear(aligned_dim, aligned_dim)
        )
        
    def forward(self, modality_features: Dict[str, torch.Tensor]) -> Dict[str, torch.Tensor]:
        """
        Align features from different modalities
        
        Args:
            modality_features: Dictionary of modality features
            
        Returns:
            aligned_features: Aligned features in common space
        """
        aligned_features = {}
        
        for modality, features in modality_features.items():
            if modality in self.alignment_nets:
                # Flatten spatial features if needed
                if len(features.shape) > 2:
                    B = features.size(0)
                    features = features.view(B, -1)
                
                # Align to common space
                aligned = self.alignment_nets[modality](features)
                
                # Apply consistency projection
                aligned = self.consistency_proj(aligned)
                
                aligned_features[modality] = aligned
        
        return aligned_features


class MultiSourceFusionModule(nn.Module):
    """
    Comprehensive multi-source feature fusion module
    Integrates multiple fusion strategies for optimal performance
    """
    
    def __init__(self, config: Dict):
        super(MultiSourceFusionModule, self).__init__()
        
        self.config = config
        fusion_config = config.get('fusion', {})
        
        # Feature dimensions
        self.albedo_dim = config['model']['albedo_encoder']['feature_dim'] #256
        self.shading_dim = config['model']['shading_encoder']['feature_dim']
        self.vae_latent_dim = config['model']['vae_branch']['latent_dim']
        
        # Cross-modal alignment
        modality_dims = {
            'albedo': self.albedo_dim * 14 * 14,  # Flattened spatial features 256 * 14 * 14 = 50176
            'shading': self.shading_dim * 14 * 14, #50176
            'geometry': self.vae_latent_dim #256
        }
        
        self.cross_modal_alignment = CrossModalAlignment(
            modality_dims=modality_dims,
            aligned_dim=fusion_config.get('aligned_dim', 256)
        )
        
        # Spatial fusion for 2D features
        self.spatial_fusion = SpatialFeatureFusion(
            feature_dim=max(self.albedo_dim, self.shading_dim),
            spatial_size=(14, 14)
        )
        
        # Attention fusion for aligned features
        self.attention_fusion = AttentionFusion(
            feature_dim=fusion_config.get('aligned_dim', 256),
            num_sources=3,  # albedo, shading, geometry
            hidden_dim=fusion_config.get('attention_hidden_dim', 128)
        )
        
        # Final projection
        self.final_projection = nn.Sequential(
            nn.Linear(fusion_config.get('aligned_dim', 256), 512),
            nn.ReLU(inplace=True),
            nn.Dropout(0.3),
            nn.Linear(512, fusion_config.get('output_dim', 256))
        )
        
        print("Multi-source Fusion Module initialized")
        
    def forward(self, albedo_features: torch.Tensor, 
                shading_features: torch.Tensor, 
                vae_latent: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Comprehensive multi-source feature fusion
        
        Args:
            albedo_features: Albedo features [B, C, H, W]
            shading_features: Shading features [B, C, H, W]
            vae_latent: VAE latent features [B, latent_dim]
            
        Returns:
            fusion_results: Dictionary containing fusion results
        """
        batch_size = albedo_features.size(0)
        
        # 1. Spatial fusion of 2D features
        # Ensure both features have same dimensions for spatial fusion
        if albedo_features.size(1) != shading_features.size(1):
            # Project to common dimension
            target_dim = max(albedo_features.size(1), shading_features.size(1))
            if albedo_features.size(1) < target_dim:
                albedo_features = F.interpolate(
                    albedo_features, size=(target_dim, 14, 14), mode='nearest'
                )
            if shading_features.size(1) < target_dim:
                shading_features = F.interpolate(
                    shading_features, size=(target_dim, 14, 14), mode='nearest'
                )
        
        spatial_fused = self.spatial_fusion(albedo_features, shading_features)
        
        # 2. Cross-modal alignment
        modality_features = {
            'albedo': albedo_features,
            'shading': shading_features,
            'geometry': vae_latent
        }
        
        aligned_features = self.cross_modal_alignment(modality_features)
        
        # 3. Attention-based fusion
        aligned_list = [
            aligned_features['albedo'],
            aligned_features['shading'],
            aligned_features['geometry']
        ]
        
        attention_fused = self.attention_fusion(aligned_list)
        
        # 4. Final projection
        final_features = self.final_projection(attention_fused)
        
        # Compile results
        fusion_results = {
            'spatial_fused': spatial_fused,
            'aligned_features': aligned_features,
            'attention_fused': attention_fused,
            'final_features': final_features,
            'fusion_weights': self.attention_fusion.attention_net(
                torch.cat(aligned_list, dim=1)
            )
        }
        
        return fusion_results


class AnomalyScoreComputation(nn.Module):
    """
    Anomaly score computation from fused features
    Computes multiple anomaly signals for final detection
    """
    
    def __init__(self, feature_dim: int = 256):
        super(AnomalyScoreComputation, self).__init__()
        
        self.feature_dim = feature_dim
        
        # Reconstruction quality assessment
        self.recon_quality_net = nn.Sequential(
            nn.Linear(feature_dim, 128),
            nn.ReLU(inplace=True),
            nn.Linear(128, 64),
            nn.ReLU(inplace=True),
            nn.Linear(64, 1),
            nn.Sigmoid()
        )
        
        # Cross-modal consistency assessment
        self.consistency_net = nn.Sequential(
            nn.Linear(feature_dim, 128),
            nn.ReLU(inplace=True),
            nn.Linear(128, 64),
            nn.ReLU(inplace=True),
            nn.Linear(64, 1),
            nn.Sigmoid()
        )
        
        # Geometric plausibility assessment
        self.geometry_net = nn.Sequential(
            nn.Linear(feature_dim, 128),
            nn.ReLU(inplace=True),
            nn.Linear(128, 64),
            nn.ReLU(inplace=True),
            nn.Linear(64, 1),
            nn.Sigmoid()
        )
        
        # Material consistency assessment
        self.material_net = nn.Sequential(
            nn.Linear(feature_dim, 128),
            nn.ReLU(inplace=True),
            nn.Linear(128, 64),
            nn.ReLU(inplace=True),
            nn.Linear(64, 1),
            nn.Sigmoid()
        )
        
        # Final anomaly score fusion
        self.score_fusion = nn.Sequential(
            nn.Linear(4, 8),  # 4 different anomaly signals
            nn.ReLU(inplace=True),
            nn.Linear(8, 1),
            nn.Sigmoid()
        )
        
    def forward(self, fused_features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Compute anomaly scores from fused features
        
        Args:
            fused_features: Fused features [B, feature_dim]
            
        Returns:
            anomaly_scores: Dictionary of anomaly scores
        """
        # Compute individual anomaly signals
        recon_quality = self.recon_quality_net(fused_features)      # [B, 1]
        consistency = self.consistency_net(fused_features)          # [B, 1]
        geometry_score = self.geometry_net(fused_features)          # [B, 1]
        material_score = self.material_net(fused_features)          # [B, 1]
        
        # Invert quality scores to anomaly scores (lower quality = higher anomaly)
        recon_anomaly = 1 - recon_quality
        consistency_anomaly = 1 - consistency
        geometry_anomaly = 1 - geometry_score
        material_anomaly = 1 - material_score
        
        # Fuse individual scores
        individual_scores = torch.cat([
            recon_anomaly, consistency_anomaly, 
            geometry_anomaly, material_anomaly
        ], dim=1)  # [B, 4]
        
        final_anomaly_score = self.score_fusion(individual_scores)
        
        anomaly_scores = {
            'reconstruction_anomaly': recon_anomaly,
            'consistency_anomaly': consistency_anomaly,
            'geometry_anomaly': geometry_anomaly,
            'material_anomaly': material_anomaly,
            'final_anomaly_score': final_anomaly_score,
            'individual_scores': individual_scores
        }
        
        return anomaly_scores


class EnhancedMultiSourceFusion(nn.Module):
    """
    Enhanced multi-source fusion with anomaly detection capability
    Complete fusion pipeline from features to anomaly scores
    """
    
    def __init__(self, config: Dict):
        super(EnhancedMultiSourceFusion, self).__init__()
        
        self.config = config
        
        # Core fusion module
        self.fusion_module = MultiSourceFusionModule(config) 
        
        # Anomaly score computation
        fusion_config = config.get('fusion', {})
        output_dim = fusion_config.get('output_dim', 256)
        self.anomaly_scorer = AnomalyScoreComputation(output_dim)
        
        print("Enhanced Multi-source Fusion initialized")
        
    def forward(self, albedo_features: torch.Tensor,
                shading_features: torch.Tensor,
                vae_latent: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Complete fusion and anomaly detection pipeline
        
        Args:
            albedo_features: Albedo features [B, C, H, W]
            shading_features: Shading features [B, C, H, W]
            vae_latent: VAE latent features [B, latent_dim]
            
        Returns:
            complete_results: Fusion results and anomaly scores
        """
        # Feature fusion
        fusion_results = self.fusion_module(albedo_features, shading_features, vae_latent)
        
        # Anomaly score computation
        anomaly_scores = self.anomaly_scorer(fusion_results['final_features'])
        
        # Combine results
        complete_results = {
            **fusion_results,
            **anomaly_scores
        }
        
        return complete_results