#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MVTec 3D-AD数据集预处理脚本
集成本征图像分解和3D几何特征提取
"""

import os
import sys
import glob
from tqdm import tqdm
import numpy as np
import torch
from PIL import Image
import argparse
import yaml

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from model.physical_decomposition import PhysicalDecomposition

def mvtec3d_classes():
    """MVTec 3D-AD数据集类别列表"""
    return ["bagel", "cable_gland", "carrot", "cookie", "dowel", 
            "foam", "peach", "potato", "rope", "tire"]

class MVTec3DPreprocessor:
    """
    MVTec 3D-AD数据集预处理器
    功能：
    1. 本征图像分解（Albedo + Shading）
    2. 3D几何特征提取（多尺度法线+曲率）
    3. 批量处理和保存
    """
    
    def __init__(self, config):
        self.config = config
        
        # 初始化物理信息提取模块
        print("初始化物理信息提取模块...")
        self.physical_decomposer = PhysicalDecomposition(config)
        print("物理信息提取模块初始化完成!")
        
    def process_single_sample(self, rgb_path, tiff_path, save_dir, sample_name):
        """
        处理单个样本
        """
        try:
            # 加载RGB图像
            rgb_image = Image.open(rgb_path).convert('RGB')
            rgb_array = np.array(rgb_image) / 255.0  # 归一化到[0,1]
            
            # 加载点云数据
            organized_pc = self.physical_decomposer.load_and_preprocess_pointcloud(tiff_path)
            
            # 运行物理信息提取
            results = self.physical_decomposer(
                rgb_image=rgb_array, 
                organized_pointcloud=organized_pc
            )
            
            # 创建保存目录
            sample_save_dir = os.path.join(save_dir, sample_name)
            os.makedirs(sample_save_dir, exist_ok=True)
            
            # 保存本征分解结果
            self.physical_decomposer.save_intrinsic_decomposition(
                results['albedo'], 
                results['shading'],
                sample_save_dir,
                filename_prefix=f"{sample_name}_"
            )
            
            # 保存增强的3D特征
            enhanced_features_path = os.path.join(sample_save_dir, f"{sample_name}_enhanced_features.npy")
            self.physical_decomposer.save_enhanced_features(
                results['enhanced_pointcloud'], 
                enhanced_features_path
            )
            
            # 保存原始点云（用于对比）
            original_pc_path = os.path.join(sample_save_dir, f"{sample_name}_original_pointcloud.npy")
            np.save(original_pc_path, results['original_pointcloud'])
            
            return True, None
            
        except Exception as e:
            return False, str(e)
    
    def process_category(self, dataset_path, category, phases=['train', 'test']):
        """
        处理指定类别的所有数据
        """
        print(f"\n开始处理类别: {category}")
        
        category_path = os.path.join(dataset_path, category)
        if not os.path.exists(category_path):
            print(f"类别路径不存在: {category_path}")
            return
        
        for phase in phases:
            phase_path = os.path.join(category_path, phase)
            if not os.path.exists(phase_path):
                print(f"阶段路径不存在: {phase_path}")
                continue
                
            print(f"\n处理阶段: {phase}")
            
            # 遍历异常类型（good, defect_type1, defect_type2, ...）
            for anomaly_type in os.listdir(phase_path):
                anomaly_path = os.path.join(phase_path, anomaly_type)
                if not os.path.isdir(anomaly_path):
                    continue
                    
                rgb_dir = os.path.join(anomaly_path, 'rgb')
                xyz_dir = os.path.join(anomaly_path, 'xyz')
                
                if not (os.path.exists(rgb_dir) and os.path.exists(xyz_dir)):
                    print(f"跳过 {anomaly_path}: RGB或XYZ目录缺失")
                    continue
                
                # 获取所有图像路径
                rgb_paths = sorted(glob.glob(os.path.join(rgb_dir, '*.png')))
                tiff_paths = sorted(glob.glob(os.path.join(xyz_dir, '*.tiff')))
                
                if len(rgb_paths) != len(tiff_paths):
                    print(f"警告: {anomaly_path} 中RGB和点云文件数量不匹配")
                    continue
                
                print(f"\n处理: {category}/{phase}/{anomaly_type} ({len(rgb_paths)} 个样本)")
                
                # 创建保存目录
                save_dir = os.path.join(self.config['output_dir'], category, phase, anomaly_type)
                os.makedirs(save_dir, exist_ok=True)
                
                # 处理每个样本
                success_count = 0
                for rgb_path, tiff_path in tqdm(zip(rgb_paths, tiff_paths), 
                                              desc=f"{category}/{phase}/{anomaly_type}"):
                    
                    sample_name = os.path.splitext(os.path.basename(rgb_path))[0]
                    
                    success, error = self.process_single_sample(
                        rgb_path, tiff_path, save_dir, sample_name
                    )
                    
                    if success:
                        success_count += 1
                    else:
                        print(f"处理失败 {sample_name}: {error}")
                
                print(f"完成 {category}/{phase}/{anomaly_type}: {success_count}/{len(rgb_paths)} 成功")
    
    def process_dataset(self, dataset_path, categories=None, phases=['train', 'test']):
        """
        处理整个数据集
        """
        if categories is None:
            categories = mvtec3d_classes()
        
        print(f"开始处理MVTec 3D-AD数据集")
        print(f"数据集路径: {dataset_path}")
        print(f"输出路径: {self.config['output_dir']}")
        print(f"处理类别: {categories}")
        print(f"处理阶段: {phases}")
        
        for category in categories:
            try:
                self.process_category(dataset_path, category, phases)
            except Exception as e:
                print(f"处理类别 {category} 时出错: {e}")
                continue
        
        print("\n" + "=" * 60)
        print("MVTec 3D-AD数据集预处理完成!")
        print(f"结果保存在: {self.config['output_dir']}")
        print("=" * 60)


def load_config(config_path):
    """加载配置文件"""
    if os.path.exists(config_path):
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    else:
        # 默认配置
        return {
            'num_scales': 3,  # 默认使用3个尺度
            'multi_scale_params': [
                {'radius': 0.02, 'max_nn': 30},
                {'radius': 0.05, 'max_nn': 50},
                {'radius': 0.1, 'max_nn': 100},
            ],
            'output_dir': './preprocessed_mvtec3d'
        }


def main():
    parser = argparse.ArgumentParser(description='MVTec 3D-AD数据集预处理')
    parser.add_argument('--dataset_path', type=str, required=True,
                      help='MVTec 3D-AD数据集根目录路径')
    parser.add_argument('--output_dir', type=str, default='./preprocessed_mvtec3d',
                      help='预处理结果输出目录')
    parser.add_argument('--config', type=str, default='./configs/preprocess_config.yaml',
                      help='配置文件路径')
    parser.add_argument('--categories', nargs='+', default=None,
                      help='要处理的类别列表，默认处理所有类别')
    parser.add_argument('--phases', nargs='+', default=['train', 'test'],
                      help='要处理的阶段列表')
    parser.add_argument('--single_category', type=str, default=None,
                      help='仅处理单个类别（用于测试）')
    parser.add_argument('--num_scales', type=int, default=None, choices=[1, 2, 3],
                      help='选择使用的尺度数量：1、2或3个尺度')
    
    args = parser.parse_args()
    
    # 检查数据集路径
    if not os.path.exists(args.dataset_path):
        print(f"错误: 数据集路径不存在: {args.dataset_path}")
        return
    
    # 加载配置
    config = load_config(args.config)
    config['output_dir'] = args.output_dir
    
    # 如果命令行指定了尺度数量，覆盖配置文件
    if args.num_scales is not None:
        config['num_scales'] = args.num_scales
        print(f"使用命令行指定的尺度数量: {args.num_scales}")
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 创建预处理器
    preprocessor = MVTec3DPreprocessor(config)
    
    # 确定要处理的类别
    if args.single_category:
        categories = [args.single_category]
    elif args.categories:
        categories = args.categories
    else:
        categories = mvtec3d_classes()
    
    # 开始预处理
    preprocessor.process_dataset(
        dataset_path=args.dataset_path,
        categories=categories,
        phases=args.phases
    )


if __name__ == "__main__":
    main()