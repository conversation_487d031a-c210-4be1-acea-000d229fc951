"""
材质异常生成器
基于EasyNet的方法，通过外部纹理图像和Perlin噪声生成Albedo纹理材质异常

Author: IA-CRF Project
Date: 2025-08-26
"""

import os
import glob
import cv2
import numpy as np
import torch
from typing import Dict, List, Tuple, Optional, Union
from pathlib import Path
import imgaug.augmenters as iaa
from PIL import Image

from ..utils.perlin_noise import EasyNetPerlinGenerator


class TextureAugmenters:
    """
    纹理图像增强器集合
    基于EasyNet的图像增强策略
    """
    
    def __init__(self):
        """初始化增强器列表"""
        self.augmenters = [
            iaa.GammaContrast((0.5, 2.0), per_channel=True),
            iaa.MultiplyAndAddToBrightness(mul=(0.8, 1.2), add=(-30, 30)),
            iaa.pillike.EnhanceSharpness(),
            iaa.AddToHueAndSaturation((-50, 50), per_channel=True),
            iaa.Solarize(0.5, threshold=(32, 128)),
            iaa.<PERSON>erize(),
            iaa.Invert(),
            iaa.pillike.Autocontrast(),
            iaa.pillike.Equalize(),
            iaa.Affine(rotate=(-45, 45))
        ]
    
    def get_random_augmenter(self, num_augmentations: int = 3) -> iaa.Sequential:
        """
        获取随机组合的增强器
        
        Args:
            num_augmentations: 随机选择的增强数量
            
        Returns:
            增强器序列
        """
        aug_indices = np.random.choice(
            len(self.augmenters), 
            num_augmentations, 
            replace=False
        )
        
        selected_augmenters = [self.augmenters[i] for i in aug_indices]
        return iaa.Sequential(selected_augmenters)


class AlbedoMaterialAnomalyGenerator:
    """
    Albedo材质异常生成器
    基于EasyNet的方法，使用外部纹理图像和Perlin噪声生成材质异常
    """
    
    def __init__(
        self,
        anomaly_source_path: str,
        image_size: Tuple[int, int] = (224, 224),
        supported_formats: List[str] = ['.jpg', '.jpeg', '.png', '.bmp'],
        seed: Optional[int] = None
    ):
        """
        初始化材质异常生成器
        
        Args:
            anomaly_source_path: 异常源纹理图像路径（如DTD数据集）
            image_size: 图像尺寸 (height, width)
            supported_formats: 支持的图像格式
            seed: 随机种子
        """
        self.anomaly_source_path = Path(anomaly_source_path)
        self.image_size = image_size
        self.supported_formats = supported_formats
        
        # 初始化组件
        self.perlin_generator = EasyNetPerlinGenerator(seed=seed)
        self.texture_augmenters = TextureAugmenters()
        
        # 加载异常源图像路径
        self.anomaly_source_paths = self._load_anomaly_source_paths()
        
        if len(self.anomaly_source_paths) == 0:
            raise ValueError(f"No valid texture images found in {anomaly_source_path}")
        
        print(f"Loaded {len(self.anomaly_source_paths)} texture images from {anomaly_source_path}")
    
    def _load_anomaly_source_paths(self) -> List[str]:
        """
        加载异常源图像路径
        
        Returns:
            图像路径列表
        """
        paths = []
        
        for fmt in self.supported_formats:
            # 递归搜索所有子目录中的图像
            pattern = str(self.anomaly_source_path / "**" / f"*{fmt}")
            paths.extend(glob.glob(pattern, recursive=True))
        
        return sorted(paths)
    
    def load_and_preprocess_texture(self, texture_path: str) -> np.ndarray:
        """
        加载并预处理纹理图像
        
        Args:
            texture_path: 纹理图像路径
            
        Returns:
            预处理后的纹理图像 [0, 255]
        """
        # 加载图像
        texture_img = cv2.imread(texture_path, cv2.IMREAD_COLOR)
        if texture_img is None:
            raise ValueError(f"Failed to load texture image: {texture_path}")
        
        # 调整尺寸
        texture_img = cv2.resize(
            texture_img, 
            (self.image_size[1], self.image_size[0])
        )
        
        return texture_img
    
    def augment_texture(self, texture_img: np.ndarray) -> np.ndarray:
        """
        增强纹理图像
        
        Args:
            texture_img: 输入纹理图像 [0, 255]
            
        Returns:
            增强后的纹理图像 [0, 255]
        """
        augmenter = self.texture_augmenters.get_random_augmenter()
        augmented_img = augmenter(image=texture_img)
        return augmented_img
    
    def generate_material_anomaly(
        self,
        albedo_image: np.ndarray,
        depth_map: np.ndarray,
        texture_path: Optional[str] = None,
        anomaly_probability: float = 0.5,
        beta_range: Tuple[float, float] = (0.0, 0.8),
        perlin_params: Optional[Dict] = None
    ) -> Dict[str, np.ndarray]:
        """
        生成材质异常
        基于EasyNet的augment_image方法
        
        Args:
            albedo_image: 原始Albedo图像 [0, 1]
            depth_map: 深度图 [0, 1]
            texture_path: 指定的纹理图像路径，None时随机选择
            anomaly_probability: 生成异常的概率
            beta_range: Beta混合参数范围
            perlin_params: Perlin噪声参数
            
        Returns:
            包含以下键的字典:
            - 'augmented_albedo': 增强后的Albedo图像 [0, 1]
            - 'anomaly_mask': 异常掩码 [0, 1]
            - 'has_anomaly': 是否包含异常 [0, 1]
            - 'texture_source': 使用的纹理图像路径
        """
        # 设置默认Perlin参数
        if perlin_params is None:
            perlin_params = {
                'min_perlin_scale': 0,
                'perlin_scale': 6,
                'threshold': 0.5,
                'depth_threshold': 0.001,
                'rotation_range': (-90, 90)
            }
        
        # 决定是否生成异常
        no_anomaly = np.random.random()
        if no_anomaly > anomaly_probability:
            # 不生成异常，返回原始图像
            return {
                'augmented_albedo': albedo_image.astype(np.float32),
                'anomaly_mask': np.zeros((*self.image_size, 1), dtype=np.float32),
                'has_anomaly': np.array([0.0], dtype=np.float32),
                'texture_source': None
            }
        
        # 选择纹理图像
        if texture_path is None:
            texture_path = np.random.choice(self.anomaly_source_paths)
        
        # 加载并增强纹理图像
        texture_img = self.load_and_preprocess_texture(texture_path)
        augmented_texture = self.augment_texture(texture_img)
        
        # 归一化纹理图像到[0, 1]
        texture_normalized = augmented_texture.astype(np.float32) / 255.0
        
        # 生成Perlin噪声掩码
        perlin_noise, anomaly_mask = self.perlin_generator.generate_easynet_mask(
            self.image_size,
            depth_map,
            **perlin_params
        )
        
        # 应用纹理到掩码区域
        texture_masked = texture_normalized * anomaly_mask
        
        # 生成Beta混合参数
        beta = np.random.uniform(beta_range[0], beta_range[1])
        
        # 生成增强后的Albedo图像（基于EasyNet公式）
        # augmented_image = image * (1 - mask_zzz) + (1 - beta) * img_thr + beta * image * (mask_zzz)
        augmented_albedo = (
            albedo_image * (1 - anomaly_mask) +
            (1 - beta) * texture_masked +
            beta * albedo_image * anomaly_mask
        )
        
        # 检查是否真的有异常
        mask_single = anomaly_mask[:, :, 0] if len(anomaly_mask.shape) == 3 else anomaly_mask
        has_anomaly = 1.0 if np.sum(mask_single) > 0 else 0.0
        
        return {
            'augmented_albedo': augmented_albedo.astype(np.float32),
            'anomaly_mask': mask_single.astype(np.float32),
            'has_anomaly': np.array([has_anomaly], dtype=np.float32),
            'texture_source': texture_path,
            'beta': beta,
            'perlin_noise': perlin_noise
        }
    
    def generate_batch_anomalies(
        self,
        albedo_batch: np.ndarray,
        depth_batch: np.ndarray,
        anomaly_probability: float = 0.5,
        **kwargs
    ) -> Dict[str, np.ndarray]:
        """
        批量生成材质异常
        
        Args:
            albedo_batch: Albedo图像批次 [B, H, W, C]
            depth_batch: 深度图批次 [B, H, W, C]
            anomaly_probability: 异常生成概率
            **kwargs: 其他参数传递给generate_material_anomaly
            
        Returns:
            批次结果字典
        """
        batch_size = albedo_batch.shape[0]
        
        # 初始化批次结果
        augmented_batch = np.zeros_like(albedo_batch)
        mask_batch = np.zeros((batch_size, *self.image_size))
        has_anomaly_batch = np.zeros((batch_size, 1))
        texture_sources = []
        
        for i in range(batch_size):
            result = self.generate_material_anomaly(
                albedo_batch[i],
                depth_batch[i],
                anomaly_probability=anomaly_probability,
                **kwargs
            )
            
            augmented_batch[i] = result['augmented_albedo']
            mask_batch[i] = result['anomaly_mask']
            has_anomaly_batch[i] = result['has_anomaly']
            texture_sources.append(result['texture_source'])
        
        return {
            'augmented_albedo': augmented_batch,
            'anomaly_mask': mask_batch,
            'has_anomaly': has_anomaly_batch,
            'texture_sources': texture_sources
        }
    
    def save_anomaly_sample(
        self,
        result: Dict[str, np.ndarray],
        save_dir: str,
        sample_name: str
    ):
        """
        保存异常样本
        
        Args:
            result: generate_material_anomaly的结果
            save_dir: 保存目录
            sample_name: 样本名称
        """
        save_dir = Path(save_dir)
        save_dir.mkdir(parents=True, exist_ok=True)
        
        # 保存增强后的Albedo图像
        augmented_albedo = (result['augmented_albedo'] * 255).astype(np.uint8)
        if len(augmented_albedo.shape) == 3:
            augmented_albedo = cv2.cvtColor(augmented_albedo, cv2.COLOR_RGB2BGR)
        cv2.imwrite(str(save_dir / f"{sample_name}_augmented_albedo.png"), augmented_albedo)
        
        # 保存异常掩码
        mask = (result['anomaly_mask'] * 255).astype(np.uint8)
        cv2.imwrite(str(save_dir / f"{sample_name}_anomaly_mask.png"), mask)
        
        # 保存元信息
        meta_info = {
            'has_anomaly': result['has_anomaly'][0],
            'texture_source': result['texture_source'],
            'beta': result.get('beta', None)
        }
        
        import json
        with open(save_dir / f"{sample_name}_meta.json", 'w') as f:
            json.dump(meta_info, f, indent=2)


class DTDTextureAnomalyGenerator(AlbedoMaterialAnomalyGenerator):
    """
    DTD（Describable Textures Dataset）纹理异常生成器
    专门用于处理DTD数据集格式的纹理图像
    """
    
    def __init__(self, dtd_dataset_path: str, **kwargs):
        """
        初始化DTD纹理异常生成器
        
        Args:
            dtd_dataset_path: DTD数据集路径
            **kwargs: 其他参数传递给父类
        """
        super().__init__(dtd_dataset_path, **kwargs)
        
        # DTD数据集特定的类别
        self.dtd_categories = self._get_dtd_categories()
        print(f"Found {len(self.dtd_categories)} DTD texture categories")
    
    def _get_dtd_categories(self) -> List[str]:
        """
        获取DTD数据集的纹理类别
        
        Returns:
            类别名称列表
        """
        categories = []
        if self.anomaly_source_path.exists():
            for item in self.anomaly_source_path.iterdir():
                if item.is_dir():
                    categories.append(item.name)
        return sorted(categories)
    
    def generate_category_specific_anomaly(
        self,
        albedo_image: np.ndarray,
        depth_map: np.ndarray,
        texture_category: str,
        **kwargs
    ) -> Dict[str, np.ndarray]:
        """
        生成特定类别的纹理异常
        
        Args:
            albedo_image: 原始Albedo图像
            depth_map: 深度图
            texture_category: DTD纹理类别
            **kwargs: 其他参数
            
        Returns:
            异常生成结果
        """
        if texture_category not in self.dtd_categories:
            raise ValueError(f"Unknown DTD category: {texture_category}")
        
        # 获取该类别的所有纹理图像
        category_path = self.anomaly_source_path / texture_category
        category_textures = []
        
        for fmt in self.supported_formats:
            pattern = str(category_path / f"*{fmt}")
            category_textures.extend(glob.glob(pattern))
        
        if not category_textures:
            raise ValueError(f"No textures found in category: {texture_category}")
        
        # 随机选择该类别的一个纹理
        texture_path = np.random.choice(category_textures)
        
        return self.generate_material_anomaly(
            albedo_image, depth_map, texture_path=texture_path, **kwargs
        )


if __name__ == "__main__":
    # 测试代码
    print("Testing AlbedoMaterialAnomalyGenerator...")
    
    # 创建测试数据
    albedo_test = np.random.rand(224, 224, 3).astype(np.float32)
    depth_test = np.ones((224, 224, 3)) * 0.5
    
    # 如果有DTD数据集，可以测试
    try:
        # 替换为实际的DTD数据集路径
        dtd_path = "/raid/liulinna/projects/EasyNet/datasets/dtd/images/"
        generator = DTDTextureAnomalyGenerator(dtd_path)
        
        result = generator.generate_material_anomaly(albedo_test, depth_test)
        print(f"Generated anomaly: {result['has_anomaly'][0]}")
        print(f"Anomaly coverage: {np.sum(result['anomaly_mask']) / result['anomaly_mask'].size * 100:.2f}%")


        
    except Exception as e:
        print(f"DTD test failed (expected if DTD dataset not available): {e}")
        
        # 创建简单的测试生成器
        print("Creating simple test generator...")
        test_dir = Path("test_textures")
        test_dir.mkdir(exist_ok=True)
        
        # 创建测试纹理图像
        test_texture = np.random.randint(0, 256, (224, 224, 3), dtype=np.uint8)
        cv2.imwrite(str(test_dir / "test_texture.jpg"), test_texture)
        
        generator = AlbedoMaterialAnomalyGenerator(str(test_dir))
        result = generator.generate_material_anomaly(albedo_test, depth_test)
        
        print(f"Generated anomaly: {result['has_anomaly'][0]}")
        print(f"Anomaly coverage: {np.sum(result['anomaly_mask']) / result['anomaly_mask'].size * 100:.2f}%")


# 为了向后兼容，提供别名
AlbedoAnomalyGenerator = AlbedoMaterialAnomalyGenerator