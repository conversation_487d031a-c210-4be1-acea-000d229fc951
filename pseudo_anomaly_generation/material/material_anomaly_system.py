"""
材质异常生成主接口
基于EasyNet方法的Albedo纹理材质异常生成系统

Author: IA-CRF Project
Date: 2025-08-26
"""

import os
import json
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Union
import cv2
from tqdm import tqdm

from .albedo_anomaly_generator import AlbedoMaterialAnomalyGenerator, DTDTextureAnomalyGenerator
from ..configs.material_anomaly_config import get_config, validate_config


class MaterialAnomalyGenerationSystem:
    """
    材质异常生成系统
    提供完整的Albedo纹理材质异常生成功能
    """
    
    def __init__(
        self,
        config: Optional[Dict] = None,
        preset: str = "easynet_default",
        dtd_dataset_path: Optional[str] = None
    ):
        """
        初始化材质异常生成系统
        
        Args:
            config: 自定义配置，None时使用预设配置
            preset: 预设配置名称
            dtd_dataset_path: DTD数据集路径
        """
        # 加载配置
        if config is None:
            config = get_config(preset)
        
        if not validate_config(config):
            raise ValueError("Invalid configuration")
        
        self.config = config
        self.preset = preset
        
        # 初始化生成器
        if dtd_dataset_path is not None:
            self.generator = DTDTextureAnomalyGenerator(
                dtd_dataset_path=dtd_dataset_path,
                image_size=tuple(config["base"]["image_size"]),
                seed=config["base"]["random_seed"]
            )
            self.use_dtd = True
        else:
            # 如果没有DTD数据集，需要用户提供纹理图像路径
            self.generator = None
            self.use_dtd = False
            print("Warning: No DTD dataset provided. Call set_texture_source() before generation.")
        
        # 统计信息
        self.statistics = {
            "total_generated": 0,
            "successful_anomalies": 0,
            "failed_generations": 0,
            "average_anomaly_coverage": 0.0,
            "texture_usage_count": {}
        }
    
    def set_texture_source(self, texture_source_path: str):
        """
        设置纹理源路径
        
        Args:
            texture_source_path: 纹理图像源路径
        """
        self.generator = AlbedoMaterialAnomalyGenerator(
            anomaly_source_path=texture_source_path,
            image_size=tuple(self.config["base"]["image_size"]),
            seed=self.config["base"]["random_seed"]
        )
        self.use_dtd = False
    
    def generate_single_anomaly(
        self,
        albedo_image: np.ndarray,
        depth_map: np.ndarray,
        category: Optional[str] = None,
        custom_params: Optional[Dict] = None
    ) -> Dict[str, np.ndarray]:
        """
        生成单个材质异常
        
        Args:
            albedo_image: 原始Albedo图像 [0, 1]
            depth_map: 深度图 [0, 1]
            category: 产品类别（用于类别特定参数）
            custom_params: 自定义参数
            
        Returns:
            异常生成结果
        """
        if self.generator is None:
            raise ValueError("Texture source not set. Call set_texture_source() first.")
        
        # 获取生成参数
        gen_params = self._get_generation_params(category, custom_params)
        
        try:
            # 生成异常
            result = self.generator.generate_material_anomaly(
                albedo_image=albedo_image,
                depth_map=depth_map,
                **gen_params
            )
            
            # 更新统计信息
            self._update_statistics(result)
            
            return result
            
        except Exception as e:
            self.statistics["failed_generations"] += 1
            raise RuntimeError(f"Failed to generate anomaly: {e}")
    
    def generate_batch_anomalies(
        self,
        albedo_batch: np.ndarray,
        depth_batch: np.ndarray,
        categories: Optional[List[str]] = None,
        custom_params: Optional[Dict] = None
    ) -> Dict[str, np.ndarray]:
        """
        批量生成材质异常
        
        Args:
            albedo_batch: Albedo图像批次 [B, H, W, C]
            depth_batch: 深度图批次 [B, H, W, C]
            categories: 产品类别列表
            custom_params: 自定义参数
            
        Returns:
            批次异常生成结果
        """
        if self.generator is None:
            raise ValueError("Texture source not set. Call set_texture_source() first.")
        
        batch_size = albedo_batch.shape[0]
        
        # 如果没有提供类别，使用None
        if categories is None:
            categories = [None] * batch_size
        elif len(categories) != batch_size:
            raise ValueError("Categories length must match batch size")
        
        # 批量生成
        results = []
        for i in range(batch_size):
            gen_params = self._get_generation_params(categories[i], custom_params)
            
            result = self.generator.generate_material_anomaly(
                albedo_image=albedo_batch[i],
                depth_map=depth_batch[i],
                **gen_params
            )
            results.append(result)
        
        # 合并结果
        return self._merge_batch_results(results)
    
    def generate_from_dataset(
        self,
        dataset_path: str,
        output_path: str,
        categories: Optional[List[str]] = None,
        max_samples_per_category: Optional[int] = None,
        save_original: bool = False
    ):
        """
        从数据集生成材质异常
        
        Args:
            dataset_path: 数据集路径（包含Albedo和深度图）
            output_path: 输出路径
            categories: 要处理的类别列表
            max_samples_per_category: 每个类别的最大样本数
            save_original: 是否保存原始图像
        """
        dataset_path = Path(dataset_path)
        output_path = Path(output_path)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 获取所有类别
        if categories is None:
            categories = [d.name for d in dataset_path.iterdir() if d.is_dir()]
        
        print(f"Processing {len(categories)} categories...")
        
        for category in categories:
            print(f"\nProcessing category: {category}")
            
            category_path = dataset_path / category
            if not category_path.exists():
                print(f"Category path not found: {category_path}")
                continue
            
            # 创建输出目录
            category_output = output_path / category
            category_output.mkdir(parents=True, exist_ok=True)
            
            # 处理该类别的所有样本
            self._process_category_samples(
                category_path,
                category_output,
                category,
                max_samples_per_category,
                save_original
            )
        
        # 保存统计信息
        self._save_statistics(output_path)
    
    def _get_generation_params(
        self,
        category: Optional[str] = None,
        custom_params: Optional[Dict] = None
    ) -> Dict:
        """
        获取生成参数
        
        Args:
            category: 产品类别
            custom_params: 自定义参数
            
        Returns:
            生成参数字典
        """
        # 基础参数
        params = {
            "anomaly_probability": self.config["base"]["anomaly_probability"],
            "beta_range": self.config["beta_mixing"]["beta_range"],
            "perlin_params": self.config["perlin"]
        }
        
        # 类别特定参数
        if (category is not None and 
            "mvtec3d_specific" in self.config and
            "category_specific_params" in self.config["mvtec3d_specific"] and
            category in self.config["mvtec3d_specific"]["category_specific_params"]):
            
            category_params = self.config["mvtec3d_specific"]["category_specific_params"][category]
            
            # 分离 perlin 相关参数和其他参数
            perlin_related_params = {"perlin_scale", "min_perlin_scale", "threshold", "depth_threshold", "rotation_range"}
            
            for key, value in category_params.items():
                if key in perlin_related_params:
                    # perlin相关参数放入perlin_params
                    params["perlin_params"][key] = value
                else:
                    # 其他参数直接更新
                    params[key] = value
        
        # 自定义参数覆盖
        if custom_params is not None:
            # 分离 perlin 相关参数和其他参数
            perlin_related_params = {"perlin_scale", "min_perlin_scale", "threshold", "depth_threshold", "rotation_range"}
            
            for key, value in custom_params.items():
                if key in perlin_related_params:
                    # perlin相关参数放入perlin_params
                    params["perlin_params"][key] = value
                else:
                    # 其他参数直接更新
                    params[key] = value
        
        return params
    
    def _process_category_samples(
        self,
        category_path: Path,
        output_path: Path,
        category: str,
        max_samples: Optional[int] = None,
        save_original: bool = False
    ):
        """
        处理类别样本
        
        Args:
            category_path: 类别路径
            output_path: 输出路径
            category: 类别名称
            max_samples: 最大样本数
            save_original: 是否保存原始图像
        """
        # 查找Albedo和深度图文件
        albedo_files = list(category_path.glob("**/albedo*.png")) + list(category_path.glob("**/albedo*.jpg"))
        depth_files = list(category_path.glob("**/depth*.png")) + list(category_path.glob("**/depth*.jpg"))
        depth_files += list(category_path.glob("**/xyz*.tiff")) + list(category_path.glob("**/zzz*.png"))
        
        # 匹配Albedo和深度图文件
        sample_pairs = self._match_sample_pairs(albedo_files, depth_files)
        
        if max_samples is not None:
            sample_pairs = sample_pairs[:max_samples]
        
        print(f"Found {len(sample_pairs)} sample pairs for category {category}")
        
        # 处理每个样本对
        for i, (albedo_path, depth_path) in enumerate(tqdm(sample_pairs, desc=f"Processing {category}")):
            try:
                # 加载图像
                albedo_image = self._load_albedo_image(albedo_path)
                depth_map = self._load_depth_image(depth_path)
                
                # 生成异常
                result = self.generate_single_anomaly(
                    albedo_image, depth_map, category=category
                )
                
                # 保存结果
                sample_name = f"{category}_{i:04d}"
                self._save_sample_result(
                    result, output_path, sample_name, 
                    save_original, albedo_image if save_original else None
                )
                
            except Exception as e:
                print(f"Failed to process sample {i}: {e}")
                continue
    
    def _match_sample_pairs(
        self,
        albedo_files: List[Path],
        depth_files: List[Path]
    ) -> List[Tuple[Path, Path]]:
        """
        匹配Albedo和深度图文件对
        
        Args:
            albedo_files: Albedo文件列表
            depth_files: 深度图文件列表
            
        Returns:
            匹配的文件对列表
        """
        pairs = []
        
        for albedo_file in albedo_files:
            # 尝试找到对应的深度图
            albedo_stem = albedo_file.stem.replace("albedo", "").replace("_", "")
            
            for depth_file in depth_files:
                depth_stem = depth_file.stem.replace("depth", "").replace("xyz", "").replace("zzz", "").replace("_", "")
                
                if albedo_stem == depth_stem or albedo_file.stem in depth_file.stem or depth_file.stem in albedo_file.stem:
                    pairs.append((albedo_file, depth_file))
                    break
        
        return pairs
    
    def _load_albedo_image(self, albedo_path: Path) -> np.ndarray:
        """
        加载Albedo图像
        
        Args:
            albedo_path: Albedo图像路径
            
        Returns:
            Albedo图像数组 [0, 1]
        """
        image = cv2.imread(str(albedo_path), cv2.IMREAD_COLOR)
        if image is None:
            raise ValueError(f"Failed to load albedo image: {albedo_path}")
        
        # 转换为RGB并归一化
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        image = image.astype(np.float32) / 255.0
        
        # 调整尺寸
        target_size = tuple(self.config["base"]["image_size"])
        if image.shape[:2] != target_size:
            image = cv2.resize(image, (target_size[1], target_size[0]))
        
        return image
    
    def _load_depth_image(self, depth_path: Path) -> np.ndarray:
        """
        加载深度图
        
        Args:
            depth_path: 深度图路径
            
        Returns:
            深度图数组 [0, 1]
        """
        if depth_path.suffix.lower() == '.tiff':
            import tifffile as tiff
            depth = tiff.imread(str(depth_path))
            if len(depth.shape) == 3:
                depth = depth[:, :, 2]  # 取Z通道
        else:
            depth = cv2.imread(str(depth_path), cv2.IMREAD_GRAYSCALE)
        
        if depth is None:
            raise ValueError(f"Failed to load depth image: {depth_path}")
        
        # 归一化
        depth = depth.astype(np.float32)
        if depth.max() > 1.0:
            depth = depth / 255.0
        
        # 调整尺寸并转换为三通道
        target_size = tuple(self.config["base"]["image_size"])
        if depth.shape[:2] != target_size:
            depth = cv2.resize(depth, (target_size[1], target_size[0]))
        
        if len(depth.shape) == 2:
            depth = np.stack([depth] * 3, axis=2)
        
        return depth
    
    def _save_sample_result(
        self,
        result: Dict[str, np.ndarray],
        output_path: Path,
        sample_name: str,
        save_original: bool = False,
        original_albedo: Optional[np.ndarray] = None
    ):
        """
        保存样本结果
        
        Args:
            result: 异常生成结果
            output_path: 输出路径
            sample_name: 样本名称
            save_original: 是否保存原始图像
            original_albedo: 原始Albedo图像
        """
        # 保存增强后的Albedo图像
        if self.config["output"]["save_components"]["augmented_albedo"]:
            augmented_albedo = (result['augmented_albedo'] * 255).astype(np.uint8)
            if len(augmented_albedo.shape) == 3:
                augmented_albedo = cv2.cvtColor(augmented_albedo, cv2.COLOR_RGB2BGR)
            cv2.imwrite(str(output_path / f"{sample_name}_augmented_albedo.png"), augmented_albedo)
        
        # 保存异常掩码
        if self.config["output"]["save_components"]["anomaly_mask"]:
            mask = (result['anomaly_mask'] * 255).astype(np.uint8)
            cv2.imwrite(str(output_path / f"{sample_name}_anomaly_mask.png"), mask)
        
        # 保存原始Albedo图像
        if save_original and original_albedo is not None and self.config["output"]["save_components"]["original_albedo"]:
            original_albedo_uint8 = (original_albedo * 255).astype(np.uint8)
            if len(original_albedo_uint8.shape) == 3:
                original_albedo_uint8 = cv2.cvtColor(original_albedo_uint8, cv2.COLOR_RGB2BGR)
            cv2.imwrite(str(output_path / f"{sample_name}_original_albedo.png"), original_albedo_uint8)
        
        # 保存元数据
        if self.config["output"]["save_components"]["metadata"]:
            metadata = {
                'has_anomaly': float(result['has_anomaly'][0]),
                'texture_source': result['texture_source'],
                'beta': result.get('beta', None),
                'anomaly_coverage': float(np.sum(result['anomaly_mask']) / result['anomaly_mask'].size)
            }
            
            with open(output_path / f"{sample_name}_metadata.json", 'w') as f:
                json.dump(metadata, f, indent=2)
    
    def _update_statistics(self, result: Dict[str, np.ndarray]):
        """
        更新统计信息
        
        Args:
            result: 异常生成结果
        """
        self.statistics["total_generated"] += 1
        
        if result['has_anomaly'][0] > 0:
            self.statistics["successful_anomalies"] += 1
            
            # 计算异常覆盖率
            coverage = np.sum(result['anomaly_mask']) / result['anomaly_mask'].size
            current_avg = self.statistics["average_anomaly_coverage"]
            current_count = self.statistics["successful_anomalies"]
            self.statistics["average_anomaly_coverage"] = (
                (current_avg * (current_count - 1) + coverage) / current_count
            )
        
        # 纹理使用统计
        texture_source = result.get('texture_source')
        if texture_source:
            texture_name = Path(texture_source).name
            self.statistics["texture_usage_count"][texture_name] = (
                self.statistics["texture_usage_count"].get(texture_name, 0) + 1
            )
    
    def _merge_batch_results(self, results: List[Dict[str, np.ndarray]]) -> Dict[str, np.ndarray]:
        """
        合并批次结果
        
        Args:
            results: 结果列表
            
        Returns:
            合并后的批次结果
        """
        batch_size = len(results)
        first_result = results[0]
        
        # 初始化批次数组
        augmented_batch = np.zeros((batch_size, *first_result['augmented_albedo'].shape))
        mask_batch = np.zeros((batch_size, *first_result['anomaly_mask'].shape))
        has_anomaly_batch = np.zeros((batch_size, 1))
        texture_sources = []
        
        # 填充批次数据
        for i, result in enumerate(results):
            augmented_batch[i] = result['augmented_albedo']
            mask_batch[i] = result['anomaly_mask']
            has_anomaly_batch[i] = result['has_anomaly']
            texture_sources.append(result['texture_source'])
        
        return {
            'augmented_albedo': augmented_batch,
            'anomaly_mask': mask_batch,
            'has_anomaly': has_anomaly_batch,
            'texture_sources': texture_sources
        }
    
    def _save_statistics(self, output_path: Path):
        """
        保存统计信息
        
        Args:
            output_path: 输出路径
        """
        stats_file = output_path / "generation_statistics.json"
        
        # 计算最终统计
        success_rate = 0.0
        if self.statistics["total_generated"] > 0:
            success_rate = self.statistics["successful_anomalies"] / self.statistics["total_generated"]
        
        final_stats = {
            **self.statistics,
            "success_rate": success_rate,
            "config_preset": self.preset,
            "use_dtd_dataset": self.use_dtd
        }
        
        with open(stats_file, 'w') as f:
            json.dump(final_stats, f, indent=2)
        
        print(f"\nGeneration Statistics:")
        print(f"Total generated: {self.statistics['total_generated']}")
        print(f"Successful anomalies: {self.statistics['successful_anomalies']}")
        print(f"Success rate: {success_rate:.2%}")
        print(f"Average anomaly coverage: {self.statistics['average_anomaly_coverage']:.2%}")
        print(f"Statistics saved to: {stats_file}")
    
    def get_statistics(self) -> Dict:
        """
        获取当前统计信息
        
        Returns:
            统计信息字典
        """
        return self.statistics.copy()


if __name__ == "__main__":
    # 测试代码
    print("Testing MaterialAnomalyGenerationSystem...")
    
    # 创建测试系统
    try:
        # 如果有DTD数据集，使用DTD生成器
        system = MaterialAnomalyGenerationSystem(
            preset="easynet_default",
            dtd_dataset_path="/path/to/dtd/dataset"  # 替换为实际路径
        )
    except:
        # 使用普通生成器
        system = MaterialAnomalyGenerationSystem(preset="easynet_default")
        
        # 创建测试纹理目录
        test_texture_dir = Path("test_textures")
        test_texture_dir.mkdir(exist_ok=True)
        
        # 创建测试纹理
        test_texture = np.random.randint(0, 256, (224, 224, 3), dtype=np.uint8)
        cv2.imwrite(str(test_texture_dir / "test_texture.jpg"), test_texture)
        
        system.set_texture_source(str(test_texture_dir))
    
    # 创建测试数据
    albedo_test = np.random.rand(224, 224, 3).astype(np.float32)
    depth_test = np.ones((224, 224, 3)) * 0.5
    
    # 生成单个异常
    result = system.generate_single_anomaly(albedo_test, depth_test, category="bagel")
    print(f"Generated anomaly: {result['has_anomaly'][0]}")
    
    # 获取统计信息
    stats = system.get_statistics()
    print(f"Statistics: {stats}")
    
    print("Test completed successfully!")