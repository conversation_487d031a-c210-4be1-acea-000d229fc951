"""
Geometric Anomaly Generation System
Main entry point for geometric anomaly generation workflow
Provides unified interface for all geometric anomaly generation functionality

Author: IA-CRF Project
Date: 2025-08-27
"""

import numpy as np
import os
from pathlib import Path
from typing import Dict, List, Optional, Union, Tuple
import json

from .geometric_anomaly_generator import GeometricAnomalyGenerator, convert_numpy_types
from ..configs.geometric_anomaly_config import get_config, get_category_config, validate_config


class GeometricAnomalyGenerationSystem:
    """
    Geometric Anomaly Generation System
    Complete system for generating geometric anomalies with physically consistent transformations
    """
    
    def __init__(self, preset: str = "moderate_deformation", custom_config: Dict = None):
        """
        Initialize geometric anomaly generation system
        
        Args:
            preset: Configuration preset name
            custom_config: Optional custom configuration to override preset
        """
        self.preset = preset
        
        # Load configuration
        self.config = get_config(preset)
        if custom_config:
            self._update_config(self.config, custom_config)
        
        # Validate configuration
        if not validate_config(self.config):
            raise ValueError("Invalid configuration")
        
        # Initialize generator
        self.generator = GeometricAnomalyGenerator(self.config)
        
        # Set random seed if specified
        if self.config["base"]["random_seed"] is not None:
            np.random.seed(self.config["base"]["random_seed"])
        
        print(f"GeometricAnomalyGenerationSystem initialized with preset: {preset}")
    
    def _update_config(self, base_config: Dict, updates: Dict) -> None:
        """
        Recursively update configuration with custom parameters
        
        Args:
            base_config: Base configuration dictionary
            updates: Updates to apply
        """
        for key, value in updates.items():
            if key in base_config and isinstance(base_config[key], dict) and isinstance(value, dict):
                self._update_config(base_config[key], value)
            else:
                base_config[key] = value
    
    def set_category_specific_config(self, category: str) -> None:
        """
        Set category-specific configuration for MVTec 3D-AD categories
        
        Args:
            category: Category name (e.g., 'bagel', 'cable_gland', etc.)
        """
        self.config = get_category_config(category, self.preset)
        self.generator = GeometricAnomalyGenerator(self.config)
        print(f"Updated configuration for category: {category}")
    
    def load_geometric_features(self, features_path: str) -> np.ndarray:
        """
        Load geometric features from file
        
        Args:
            features_path: Path to .npy file containing 7D geometric features
            
        Returns:
            Geometric features array [H*W, 7]
        """
        return self.generator.load_geometric_features(features_path)
    
    def generate_single_anomaly(
        self,
        geometric_features: np.ndarray,
        original_shading: Optional[np.ndarray] = None,
        custom_params: Optional[Dict] = None
    ) -> Dict:
        """
        Generate a single geometric anomaly
        
        Args:
            geometric_features: Pre-computed geometric features [H*W, 7]
            original_shading: Optional original shading image [H, W, 3]
            custom_params: Optional custom parameters for generation
            
        Returns:
            Dictionary containing anomaly generation results
        """
        return self.generator.generate_single_geometric_anomaly(
            geometric_features,
            original_shading,
            custom_params
        )
    
    def generate_from_features_file(
        self,
        features_file_path: str,
        shading_file_path: Optional[str] = None,
        custom_params: Optional[Dict] = None
    ) -> Dict:
        """
        Generate geometric anomaly from features file
        
        Args:
            features_file_path: Path to .npy file containing geometric features
            shading_file_path: Optional path to shading image file
            custom_params: Optional custom parameters for generation
            
        Returns:
            Dictionary containing anomaly generation results
        """
        # Load geometric features
        features = self.load_geometric_features(features_file_path)
        
        # Load shading if provided
        shading = None
        if shading_file_path and os.path.exists(shading_file_path):
            import cv2
            shading = cv2.imread(shading_file_path, cv2.IMREAD_COLOR)
            if shading is not None:
                shading = cv2.cvtColor(shading, cv2.COLOR_BGR2RGB).astype(np.float32) / 255.0
        
        # Generate anomaly
        return self.generate_single_anomaly(features, shading, custom_params)
    
    def generate_batch_anomalies(
        self,
        features_batch: List[np.ndarray],
        shading_batch: Optional[List[np.ndarray]] = None,
        custom_params_batch: Optional[List[Dict]] = None
    ) -> List[Dict]:
        """
        Generate geometric anomalies for a batch of data
        
        Args:
            features_batch: List of geometric features arrays [H*W, 7]
            shading_batch: Optional list of original shading images
            custom_params_batch: Optional list of custom parameters for each sample
            
        Returns:
            List of anomaly generation results
        """
        return self.generator.generate_batch_geometric_anomalies(
            features_batch,
            shading_batch,
            custom_params_batch
        )
    
    def process_mvtec3d_sample(
        self,
        sample_path: str,
        category: str,
        output_dir: str,
        sample_name: Optional[str] = None
    ) -> Dict:
        """
        Process a single MVTec 3D-AD sample
        
        Args:
            sample_path: Path to sample directory or features file
            category: MVTec 3D-AD category name
            output_dir: Output directory for results
            sample_name: Optional custom sample name
            
        Returns:
            Dictionary containing processing results
        """
        # Set category-specific configuration
        self.set_category_specific_config(category)
        
        # Determine sample name
        if sample_name is None:
            sample_name = Path(sample_path).stem
        
        # Load features
        if sample_path.endswith('.npy'):
            features_file = sample_path
        else:
            # Look for features in the sample directory
            features_file = os.path.join(sample_path, "features_224x224", f"{sample_name}.npy")
            if not os.path.exists(features_file):
                raise FileNotFoundError(f"Features file not found: {features_file}")
        
        features = self.load_geometric_features(features_file)
        
        # Look for corresponding shading/albedo image
        shading = None
        possible_shading_paths = [
            os.path.join(os.path.dirname(sample_path), "rgb", "albedo", f"{sample_name}.png"),
            os.path.join(os.path.dirname(sample_path), "rgb", f"{sample_name}.png"),
            os.path.join(sample_path, f"{sample_name}.png")
        ]
        
        for shading_path in possible_shading_paths:
            if os.path.exists(shading_path):
                import cv2
                shading = cv2.imread(shading_path, cv2.IMREAD_COLOR)
                if shading is not None:
                    shading = cv2.cvtColor(shading, cv2.COLOR_BGR2RGB).astype(np.float32) / 255.0
                    break
        
        # Generate anomaly
        result = self.generate_single_anomaly(features, shading)
        
        # Save results
        self.save_anomaly_results(result, output_dir, sample_name)
        
        # Add processing metadata
        result['processing_info'] = {
            'sample_path': sample_path,
            'category': category,
            'output_dir': output_dir,
            'sample_name': sample_name,
            'features_file': features_file,
            'shading_available': shading is not None
        }
        
        return result
    
    def batch_process_mvtec3d_category(
        self,
        dataset_path: str,
        category: str,
        output_dir: str,
        phase: str = "train",
        anomaly_type: str = "good",
        max_samples: Optional[int] = None
    ) -> List[Dict]:
        """
        Batch process MVTec 3D-AD category
        
        Args:
            dataset_path: Path to MVTec 3D-AD dataset
            category: Category name
            output_dir: Output directory
            phase: Phase (train/test/validation)
            anomaly_type: Anomaly type (good/defect_type)
            max_samples: Maximum number of samples to process
            
        Returns:
            List of processing results
        """
        # Set category-specific configuration
        self.set_category_specific_config(category)
        
        # Find sample files
        features_dir = os.path.join(dataset_path, category, phase, anomaly_type, "features_224x224")
        if not os.path.exists(features_dir):
            raise FileNotFoundError(f"Features directory not found: {features_dir}")
        
        feature_files = sorted([f for f in os.listdir(features_dir) if f.endswith('.npy')])
        if max_samples:
            feature_files = feature_files[:max_samples]
        
        print(f"Processing {len(feature_files)} samples from {category}/{phase}/{anomaly_type}")
        
        results = []
        for i, feature_file in enumerate(feature_files):
            sample_name = Path(feature_file).stem
            features_path = os.path.join(features_dir, feature_file)
            
            print(f"Processing sample {i+1}/{len(feature_files)}: {sample_name}")
            
            try:
                # Process sample
                result = self.process_mvtec3d_sample(
                    features_path,
                    category,
                    os.path.join(output_dir, category, phase, anomaly_type),
                    sample_name
                )
                
                result['batch_info'] = {
                    'batch_index': i,
                    'total_samples': len(feature_files),
                    'category': category,
                    'phase': phase,
                    'anomaly_type': anomaly_type
                }
                
                results.append(result)
                
            except Exception as e:
                print(f"Error processing {sample_name}: {e}")
                error_result = {
                    'sample_name': sample_name,
                    'error': str(e),
                    'has_anomaly': np.array([0.0]),
                    'batch_info': {
                        'batch_index': i,
                        'total_samples': len(feature_files),
                        'category': category,
                        'phase': phase,
                        'anomaly_type': anomaly_type
                    }
                }
                results.append(error_result)
        
        # Save batch summary
        self._save_batch_summary(results, output_dir, category, phase, anomaly_type)
        
        return results
    
    def save_anomaly_results(
        self,
        results: Dict,
        save_dir: str,
        sample_name: str,
        save_pointclouds: bool = True,
        save_shading: bool = True,
        save_metadata: bool = True
    ) -> None:
        """
        Save anomaly generation results
        
        Args:
            results: Results from generate_single_anomaly
            save_dir: Save directory
            sample_name: Sample name for file naming
            save_pointclouds: Whether to save point cloud data
            save_shading: Whether to save shading images
            save_metadata: Whether to save metadata
        """
        self.generator.save_anomaly_results(
            results,
            save_dir,
            sample_name,
            save_pointclouds,
            save_shading,
            save_metadata
        )
    
    def visualize_results(
        self,
        results: Dict,
        save_path: Optional[str] = None
    ) -> None:
        """
        Visualize anomaly generation results
        
        Args:
            results: Results from generate_single_anomaly
            save_path: Optional save path for visualization
        """
        self.generator.visualize_complete_pipeline(results, save_path)
    
    def _save_batch_summary(
        self,
        results: List[Dict],
        output_dir: str,
        category: str,
        phase: str,
        anomaly_type: str
    ) -> None:
        """
        Save batch processing summary
        
        Args:
            results: List of processing results
            output_dir: Output directory
            category: Category name
            phase: Phase name
            anomaly_type: Anomaly type
        """
        summary_dir = os.path.join(output_dir, category, phase, anomaly_type)
        os.makedirs(summary_dir, exist_ok=True)
        
        # Calculate statistics
        total_samples = len(results)
        successful_anomalies = sum([1 for r in results if r.get('has_anomaly', [0])[0] > 0])
        errors = sum([1 for r in results if 'error' in r])
        
        summary = {
            'batch_info': {
                'category': category,
                'phase': phase,
                'anomaly_type': anomaly_type,
                'total_samples': total_samples,
                'successful_anomalies': successful_anomalies,
                'errors': errors,
                'success_rate': successful_anomalies / total_samples if total_samples > 0 else 0
            },
            'configuration': self.config,
            'sample_summaries': []
        }
        
        # Add sample summaries
        for result in results:
            sample_summary = {
                'sample_name': result.get('processing_info', {}).get('sample_name', 'unknown'),
                'has_anomaly': bool(result.get('has_anomaly', [0])[0] > 0),
                'anomaly_type': result.get('anomaly_type', 'unknown'),
                'error': result.get('error', None)
            }
            
            # Add generation statistics if available
            if 'generation_params' in result:
                sample_summary['generation_stats'] = result['generation_params']
            
            summary['sample_summaries'].append(sample_summary)
        
        # Convert NumPy types before saving
        summary = convert_numpy_types(summary)
        
        # Save summary
        summary_path = os.path.join(summary_dir, f"batch_summary_{category}_{phase}_{anomaly_type}.json")
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)
        
        print(f"Batch summary saved to: {summary_path}")
        print(f"Batch statistics: {successful_anomalies}/{total_samples} successful anomalies, {errors} errors")
    
    def get_config_summary(self) -> Dict:
        """
        Get summary of current configuration
        
        Returns:
            Configuration summary dictionary
        """
        return {
            'preset': self.preset,
            'anomaly_probability': self.config['base']['anomaly_probability'],
            'num_patches_range': self.config['base']['num_patches_range'],
            'displacement_strength_range': self.config['displacement']['displacement_strength_range'],
            'patch_coverage_ratio': self.config['base']['patch_coverage_ratio'],
            'patch_size_range': [
                self.config['patch_selection']['min_patch_size'],
                self.config['patch_selection']['max_patch_size']
            ]
        }


def test_geometric_anomaly_system():
    """
    Test the complete GeometricAnomalyGenerationSystem
    """
    # Initialize system
    system = GeometricAnomalyGenerationSystem("moderate_deformation")
    
    print("Configuration summary:")
    config_summary = system.get_config_summary()
    for key, value in config_summary.items():
        print(f"  {key}: {value}")
    
    # Create test data using the existing test function
    from .geometric_anomaly_generator import test_geometric_anomaly_generator
    
    # This will create test features and run the full pipeline
    print("\nRunning complete system test...")
    results = test_geometric_anomaly_generator()
    
    print("\nTest completed successfully!")
    return results


if __name__ == "__main__":
    test_geometric_anomaly_system()