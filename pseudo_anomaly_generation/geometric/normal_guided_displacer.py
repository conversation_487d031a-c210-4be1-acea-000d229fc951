"""
Normal-Guided 3D Point Cloud Displacement
Implements normal vector extraction, displacement direction and intensity randomization,
decay weight calculation, and geometric deformation application

Author: IA-CRF Project
Date: 2025-08-27
"""

import numpy as np
import open3d as o3d
from scipy.spatial import cKDTree
from typing import Tuple, List, Optional, Dict
import cv2


class NormalGuidedDisplacer:
    """
    Normal-Guided 3D Point Cloud Displacement
    Implements geometric deformation based on surface normals with controlled displacement
    """
    
    def __init__(self, config: Dict = None):
        """
        Initialize normal-guided displacer
        
        Args:
            config: Configuration parameter dictionary
        """
        self.config = config or {}
        
        # Default parameters
        self.displacement_strength_range = self.config.get('displacement_strength_range', [0.005, 0.02])
        self.center_weight = self.config.get('center_weight', 1.0)        # Center displacement weight
        self.edge_weight = self.config.get('edge_weight', 0.1)            # Edge displacement weight
        self.normal_estimation_radius = self.config.get('normal_estimation_radius', 0.02)
        self.displacement_direction_noise = self.config.get('displacement_direction_noise', 0.1) # 位移方向噪声
        self.min_displacement_points = self.config.get('min_displacement_points', 10)
        
        # print(f"NormalGuidedDisplacer initialized with displacement range: {self.displacement_strength_range}")
    
    def extract_normals_from_features(self, geometric_features: np.ndarray) -> np.ndarray:
        """
        Extract normal vectors from 7D geometric features

        Args:
            geometric_features: Geometric features [N, 7] or [H, W, 7] where format is [x,y,z,nx,ny,nz,curvature]

        Returns:
            Normal vectors [N, 3] (flattened format)
        """
        # 处理不同的输入格式
        if len(geometric_features.shape) == 3:
            # 输入格式为 [H, W, 7]
            if geometric_features.shape[2] < 6:
                raise ValueError("Geometric features must have at least 6 dimensions (x,y,z,nx,ny,nz)")
            # 提取法线并展平
            normals = geometric_features[:, :, 3:6].reshape(-1, 3).copy()
        elif len(geometric_features.shape) == 2:
            # 输入格式为 [N, 7]
            if geometric_features.shape[1] < 6:
                raise ValueError("Geometric features must have at least 6 dimensions (x,y,z,nx,ny,nz)")
            # 直接提取法线
            normals = geometric_features[:, 3:6].copy()
        else:
            raise ValueError(f"Unsupported geometric features shape: {geometric_features.shape}")

        # Normalize normals to unit vectors
        norms = np.linalg.norm(normals, axis=1, keepdims=True)
        valid_mask = norms.flatten() > 1e-6
        normals[valid_mask] = normals[valid_mask] / norms[valid_mask]

        return normals
    
    def estimate_normals_from_pointcloud(self, organized_pointcloud: np.ndarray) -> np.ndarray:
        """
        Estimate normal vectors directly from organized point cloud
        
        Args:
            organized_pointcloud: Organized point cloud [H, W, 3]
            
        Returns:
            Normal vectors [H*W, 3]
        """
        H, W, _ = organized_pointcloud.shape
        
        # Convert to unorganized point cloud
        points = organized_pointcloud.reshape(-1, 3)
        valid_mask = np.abs(points).sum(axis=1) > 1e-6
        
        if np.sum(valid_mask) < self.min_displacement_points:
            # Warning: Not enough valid points for normal estimation
            return np.zeros((H*W, 3))
        
        # Create Open3D point cloud
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points[valid_mask])
        
        # Estimate normals
        pcd.estimate_normals(
            search_param=o3d.geometry.KDTreeSearchParamHybrid(
                radius=self.normal_estimation_radius, 
                max_nn=30
            )
        )
        
        # Ensure normal consistency
        pcd.orient_normals_consistent_tangent_plane(100)
        
        # Fill normals back to full array
        normals = np.zeros((H*W, 3))
        normals[valid_mask] = np.asarray(pcd.normals)
        
        return normals
    
    def calculate_displacement_weights(
        self, 
        patch_mask: np.ndarray, 
        seed_point: Tuple[int, int]
    ) -> np.ndarray:
        """
        Calculate displacement weights with center strong and edge weak pattern
        
        Args:
            patch_mask: Binary patch mask [H, W]
            seed_point: Seed point coordinates (row, col)
            
        Returns:
            Weight map [H, W] with values from edge_weight to center_weight
        """
        H, W = patch_mask.shape
        seed_row, seed_col = seed_point
        
        # Create distance map from seed point
        y_coords, x_coords = np.ogrid[:H, :W]
        distance_map = np.sqrt((x_coords - seed_col)**2 + (y_coords - seed_row)**2)
        
        # Get patch region
        patch_points = np.where(patch_mask)
        if len(patch_points[0]) == 0:
            return np.zeros((H, W))
        
        # Calculate max distance within patch
        patch_distances = distance_map[patch_mask]
        max_distance = np.max(patch_distances)
        
        if max_distance == 0:
            # Single point patch
            weights = np.zeros((H, W))
            weights[patch_mask] = self.center_weight
            return weights
        
        # Initialize weights
        weights = np.zeros((H, W))
        
        # Calculate weights using inverse distance (center=1.0, edge=edge_weight)
        normalized_distances = distance_map[patch_mask] / max_distance
        patch_weights = self.edge_weight + (self.center_weight - self.edge_weight) * (1 - normalized_distances)
        weights[patch_mask] = patch_weights
        
        return weights
    
    def randomize_displacement_direction(
        self, 
        normals: np.ndarray, 
        displacement_strength: float
    ) -> np.ndarray:
        """
        Randomize displacement direction and strength
        
        Args:
            normals: Normal vectors [N, 3]
            displacement_strength: Base displacement strength
            
        Returns:
            Displacement vectors [N, 3]
        """
        N = normals.shape[0]
        
        # Base displacement along normals
        displacements = normals * displacement_strength
        
        # Add random component perpendicular to normals
        if self.displacement_direction_noise > 0:
            # Generate random vectors
            random_vectors = np.random.randn(N, 3)
            
            # Make them perpendicular to normals
            dot_products = np.sum(random_vectors * normals, axis=1, keepdims=True)
            perpendicular_vectors = random_vectors - dot_products * normals
            
            # Normalize and scale
            norms = np.linalg.norm(perpendicular_vectors, axis=1, keepdims=True)
            valid_mask = norms.flatten() > 1e-6
            
            if np.any(valid_mask):
                perpendicular_vectors[valid_mask] = (
                    perpendicular_vectors[valid_mask] / norms[valid_mask] * 
                    displacement_strength * self.displacement_direction_noise
                )
                displacements += perpendicular_vectors
        
        # Add random strength variation (±10%) - 降低随机变化幅度使位移更平缓
        strength_variation = 1.0 + 0.1 * (np.random.rand(N, 1) - 0.5)
        displacements *= strength_variation
        
        return displacements
    
    def apply_geometric_deformation(
        self,
        organized_pointcloud: np.ndarray,
        patch_info: Dict,
        geometric_features: Optional[np.ndarray] = None,
        displacement_strength: Optional[float] = None
    ) -> Tuple[np.ndarray, Dict]:
        """
        Apply geometric deformation to selected patch
        
        Args:
            organized_pointcloud: Original organized point cloud [H, W, 3]
            patch_info: Patch information from LocalPatchSelector
            geometric_features: Optional pre-computed geometric features [H*W, 7]
            displacement_strength: Optional custom displacement strength
            
        Returns:
            Tuple of:
            - Deformed point cloud [H, W, 3]
            - Deformation information dictionary
        """
        H, W, _ = organized_pointcloud.shape
        patch_mask = patch_info['patch_mask']
        seed_point = patch_info['seed_point']
        
        # Determine displacement strength
        if displacement_strength is None:
            displacement_strength = np.random.uniform(*self.displacement_strength_range)
        
        # Extract or estimate normals
        if geometric_features is not None:
            normals = self.extract_normals_from_features(geometric_features)
            normals = normals.reshape(H, W, 3)
        else:
            normals_flat = self.estimate_normals_from_pointcloud(organized_pointcloud)
            normals = normals_flat.reshape(H, W, 3)
        
        # Calculate displacement weights
        weights = self.calculate_displacement_weights(patch_mask, seed_point)
        
        # Get patch points and their normals
        patch_points_mask = patch_mask
        patch_normals = normals[patch_points_mask]
        patch_weights = weights[patch_points_mask]
        
        # Generate displacement vectors
        displacements = self.randomize_displacement_direction(
            patch_normals, 
            displacement_strength
        )
        
        # Apply weight scaling
        weighted_displacements = displacements * patch_weights[:, np.newaxis]
        
        # Apply deformation
        deformed_pointcloud = organized_pointcloud.copy()
        deformed_pointcloud[patch_points_mask] += weighted_displacements
        
        # Collect deformation information
        deformation_info = {
            'displacement_strength': displacement_strength,
            'num_displaced_points': np.sum(patch_points_mask),
            'max_displacement': np.max(np.linalg.norm(weighted_displacements, axis=1)),
            'mean_displacement': np.mean(np.linalg.norm(weighted_displacements, axis=1)),
            'displacement_direction_noise': self.displacement_direction_noise,
            'weights_range': [np.min(patch_weights), np.max(patch_weights)],
            'deformed_region_bounds': self._get_patch_bounds(patch_mask)
        }
        
        return deformed_pointcloud, deformation_info
    
    def apply_multiple_deformations(
        self,
        organized_pointcloud: np.ndarray,
        patches_info: List[Dict],
        geometric_features: Optional[np.ndarray] = None,
        displacement_strengths: Optional[List[float]] = None
    ) -> Tuple[np.ndarray, List[Dict]]:
        """
        Apply geometric deformations to multiple patches
        
        Args:
            organized_pointcloud: Original organized point cloud [H, W, 3]
            patches_info: List of patch information from LocalPatchSelector
            geometric_features: Optional pre-computed geometric features [H*W, 7]
            displacement_strengths: Optional custom displacement strengths for each patch
            
        Returns:
            Tuple of:
            - Deformed point cloud [H, W, 3]
            - List of deformation information dictionaries
        """
        if not patches_info:
            return organized_pointcloud.copy(), []
        
        deformed_pointcloud = organized_pointcloud.copy()
        deformation_infos = []
        
        # Prepare displacement strengths
        if displacement_strengths is None:
            displacement_strengths = [None] * len(patches_info)
        
        for i, (patch_info, displacement_strength) in enumerate(zip(patches_info, displacement_strengths)):
        # Applying deformation
            
            # Apply deformation to current state
            deformed_pointcloud, deformation_info = self.apply_geometric_deformation(
                deformed_pointcloud,
                patch_info,
                geometric_features,
                displacement_strength
            )
            
            deformation_info['patch_index'] = i
            deformation_infos.append(deformation_info)
            
            # Displaced points
        
        return deformed_pointcloud, deformation_infos
    
    def _get_patch_bounds(self, patch_mask: np.ndarray) -> Dict:
        """
        Get bounding box of patch region
        
        Args:
            patch_mask: Binary patch mask [H, W]
            
        Returns:
            Dictionary with min/max row/col bounds
        """
        patch_points = np.where(patch_mask)
        if len(patch_points[0]) == 0:
            return {'min_row': 0, 'max_row': 0, 'min_col': 0, 'max_col': 0}
        
        return {
            'min_row': int(np.min(patch_points[0])),
            'max_row': int(np.max(patch_points[0])),
            'min_col': int(np.min(patch_points[1])),
            'max_col': int(np.max(patch_points[1]))
        }
    
    def visualize_deformation(
        self,
        original_pointcloud: np.ndarray,
        deformed_pointcloud: np.ndarray,
        patches_info: List[Dict],
        deformation_infos: List[Dict],
        save_path: Optional[str] = None
    ):
        """
        Visualize geometric deformation results
        
        Args:
            original_pointcloud: Original point cloud [H, W, 3]
            deformed_pointcloud: Deformed point cloud [H, W, 3]
            patches_info: List of patch information
            deformation_infos: List of deformation information
            save_path: Optional save path for visualization
        """
        import matplotlib.pyplot as plt
        
        # Calculate displacement magnitude
        displacement = deformed_pointcloud - original_pointcloud
        displacement_magnitude = np.linalg.norm(displacement, axis=2)
        
        # Create visualization
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # Original depth
        orig_depth = original_pointcloud[:, :, 2]
        axes[0, 0].imshow(orig_depth, cmap='viridis')
        axes[0, 0].set_title('Original Depth Map')
        axes[0, 0].axis('off')
        
        # Deformed depth
        def_depth = deformed_pointcloud[:, :, 2]
        axes[0, 1].imshow(def_depth, cmap='viridis')
        axes[0, 1].set_title('Deformed Depth Map')
        axes[0, 1].axis('off')
        
        # Displacement magnitude
        im2 = axes[0, 2].imshow(displacement_magnitude, cmap='hot')
        axes[0, 2].set_title('Displacement Magnitude')
        axes[0, 2].axis('off')
        plt.colorbar(im2, ax=axes[0, 2], fraction=0.046, pad=0.04)
        
        # Patches overlay
        combined_mask = np.zeros_like(orig_depth)
        for i, patch_info in enumerate(patches_info):
            combined_mask += (i + 1) * patch_info['patch_mask']
        
        axes[1, 0].imshow(orig_depth, cmap='viridis', alpha=0.7)
        axes[1, 0].imshow(combined_mask, cmap='tab10', alpha=0.5)
        axes[1, 0].set_title('Deformation Patches')
        axes[1, 0].axis('off')
        
        # Displacement statistics
        if deformation_infos:
            displacements = [info['mean_displacement'] for info in deformation_infos]
            max_displacements = [info['max_displacement'] for info in deformation_infos]
            
            x = range(len(displacements))
            axes[1, 1].bar(x, displacements, alpha=0.7, label='Mean Displacement')
            axes[1, 1].bar(x, max_displacements, alpha=0.7, label='Max Displacement')
            axes[1, 1].set_title('Displacement Statistics')
            axes[1, 1].set_xlabel('Patch Index')
            axes[1, 1].set_ylabel('Displacement Magnitude')
            axes[1, 1].legend()
        
        # Summary statistics
        total_displaced = np.sum(displacement_magnitude > 1e-6)
        mean_displacement = np.mean(displacement_magnitude[displacement_magnitude > 1e-6]) if total_displaced > 0 else 0
        max_displacement = np.max(displacement_magnitude)
        
        stats_text = f"Total displaced points: {total_displaced}\n"
        stats_text += f"Mean displacement: {mean_displacement:.4f}\n"
        stats_text += f"Max displacement: {max_displacement:.4f}\n"
        stats_text += f"Number of patches: {len(patches_info)}"
        
        axes[1, 2].text(0.1, 0.5, stats_text, transform=axes[1, 2].transAxes,
                       fontsize=12, verticalalignment='center', fontfamily='monospace')
        axes[1, 2].set_title('Deformation Summary')
        axes[1, 2].axis('off')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Deformation visualization saved to: {save_path}")
        
        plt.show()


def test_normal_guided_displacer():
    """
    Test the NormalGuidedDisplacer
    """
    from patch_selector import LocalPatchSelector
    
    # Create test data
    H, W = 224, 224
    organized_pc = np.random.rand(H, W, 3) * 2 - 1
    
    # Create realistic object shape
    center_x, center_y = W // 2, H // 2
    y_coords, x_coords = np.ogrid[:H, :W]
    distance = np.sqrt((x_coords - center_x)**2 + (y_coords - center_y)**2)
    max_distance = min(W, H) * 0.4
    
    foreground_mask = distance <= max_distance
    organized_pc[~foreground_mask] = 0
    
    # Add some structure to Z values
    organized_pc[:, :, 2] = np.where(foreground_mask, 
                                   0.5 + 0.3 * np.cos(distance / max_distance * np.pi),
                                   0)
    
    # Select patches
    patch_selector = LocalPatchSelector({
        'min_patch_size': 100,
        'max_patch_size': 400,
        'expansion_radius': 0.03
    })
    
    patches = patch_selector.select_random_patches(
        organized_pc,
        num_patches=2,
        patch_coverage_ratio=0.3
    )
    
    if not patches:
        print("No patches generated for testing")
        return
    
    # Initialize displacer
    displacer_config = {
        'displacement_strength_range': [0.01, 0.05],
        'center_weight': 1.0,
        'edge_weight': 0.2,
        'displacement_direction_noise': 0.15
    }
    
    displacer = NormalGuidedDisplacer(displacer_config)
    
    # Apply deformations
    deformed_pc, deformation_infos = displacer.apply_multiple_deformations(
        organized_pc,
        patches
    )
    
    print(f"Applied {len(deformation_infos)} deformations")
    for i, info in enumerate(deformation_infos):
        print(f"Deformation {i+1}: {info['num_displaced_points']} points, "
              f"strength {info['displacement_strength']:.4f}, "
              f"max displacement {info['max_displacement']:.4f}")
    
    # Visualize results
    displacer.visualize_deformation(
        organized_pc, 
        deformed_pc, 
        patches, 
        deformation_infos
    )
    
    return deformed_pc, deformation_infos


if __name__ == "__main__":
    test_normal_guided_displacer()