"""
Geometric Anomaly Generation Module

This module implements 3D geometric anomaly generation functionality, including:
1. 3D Local Patch Selection Algorithm
2. Normal-Guided 3D Point Cloud Displacement  
3. 2D Shading Derivative Transformation

Author: IA-CRF Project
Date: 2025-08-27
"""

from .patch_selector import LocalPatchSelector
from .normal_guided_displacer import NormalGuidedDisplacer
from .shading_derivative_transformer import ShadingDerivativeTransformer
from .geometric_anomaly_generator import GeometricAnomalyGenerator
from .geometric_anomaly_system import GeometricAnomalyGenerationSystem

__all__ = [
    'LocalPatchSelector',
    'NormalGuidedDisplacer', 
    'ShadingDerivativeTransformer',
    'GeometricAnomalyGenerator',
    'GeometricAnomalyGenerationSystem'
]