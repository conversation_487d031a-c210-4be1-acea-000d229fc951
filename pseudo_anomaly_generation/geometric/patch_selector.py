"""
3D Local Patch Selection Algorithm
Implements seed point random selection, iterative KNN expansion, patch size control and boundary smoothing

Author: IA-CRF Project
Date: 2025-08-27
"""

import numpy as np
import open3d as o3d
from scipy.spatial import cKDTree
from typing import Tuple, List, Optional, Dict
import cv2


class LocalPatchSelector:
    """
    3D Local Patch Selector
    Implements seed-based iterative KNN expansion algorithm for selecting local geometric patches
    """
    
    def __init__(self, config: Dict = None):
        """
        Initialize patch selector
        
        Args:
            config: Configuration parameter dictionary
        """
        self.config = config or {}
        
        # Default parameters
        self.min_patch_size = self.config.get('min_patch_size', 50)      # Minimum patch size
        self.max_patch_size = self.config.get('max_patch_size', 500)     # Maximum patch size
        self.expansion_radius = self.config.get('expansion_radius', 0.02) # Expansion radius
        self.max_iterations = self.config.get('max_iterations', 20)      # Maximum iterations   Patch选择和优化过程的最大迭代次数
        self.smoothing_sigma = self.config.get('smoothing_sigma', 2.0)   # Boundary smoothing parameter 平滑参数，使生成的异常区域边界更加自然
        self.depth_threshold = self.config.get('depth_threshold', 0.001) # Depth validi ty threshold 深度有效性阈值
        
        #print(f"LocalPatchSelector initialized with patch size range: [{self.min_patch_size}, {self.max_patch_size}]")
    
    def select_random_patches(
        self, 
        organized_pointcloud: np.ndarray,
        depth_map: Optional[np.ndarray] = None,
        num_patches: int = 1,
        patch_coverage_ratio: float = 0.15
    ) -> List[Dict]:
        """
        Randomly select multiple local patches
        
        Args:
            organized_pointcloud: Organized point cloud [H, W, 3]
            depth_map: Depth map [H, W] or [H, W, 3] for foreground constraint
            num_patches: Number of patches to select
            patch_coverage_ratio: Patch coverage ratio (relative to foreground area)
            
        Returns:
            List of patch information, each containing:
            - patch_mask: Patch mask [H, W]
            - seed_point: Seed point coordinates (row, col)
            - patch_points: Patch point coordinates list
            - patch_size: Patch size
        """
        H, W, _ = organized_pointcloud.shape
        
        # Get foreground mask
        if depth_map is not None:
            if len(depth_map.shape) == 3:
                depth_single = depth_map[:, :, 0]
            else:
                depth_single = depth_map
            foreground_mask = depth_single > self.depth_threshold
        else:
            # Use point cloud Z coordinate as depth
            depth_single = organized_pointcloud[:, :, 2]
            foreground_mask = depth_single > self.depth_threshold
        
        # Get valid seed point candidate positions
        valid_positions = np.where(foreground_mask)
        if len(valid_positions[0]) < self.min_patch_size:
            print(f"Warning: Not enough valid points ({len(valid_positions[0])}) for patch selection")
            return []
        
        # Calculate target patch size
        foreground_area = np.sum(foreground_mask)
        target_patch_size = int(foreground_area * patch_coverage_ratio / num_patches)
        target_patch_size = np.clip(target_patch_size, self.min_patch_size, self.max_patch_size)
        
        patches = []
        used_mask = np.zeros((H, W), dtype=bool)  # Track used areas
        
        for patch_idx in range(num_patches):
            # Selecting patch
            
            # Select seed point (avoid used areas)
            available_mask = foreground_mask & (~used_mask)
            available_positions = np.where(available_mask)
            
            if len(available_positions[0]) < self.min_patch_size:
                # Not enough available points for patch
                break
            
            # Randomly select seed point
            seed_idx = np.random.randint(len(available_positions[0]))
            seed_row = available_positions[0][seed_idx]
            seed_col = available_positions[1][seed_idx]
            
            # Generate patch using iterative KNN expansion
            patch_info = self._generate_patch_knn_expansion(
                organized_pointcloud, 
                (seed_row, seed_col), 
                target_patch_size,
                foreground_mask,
                used_mask
            )
            
            if patch_info is not None:
                # Apply boundary smoothing
                patch_info['patch_mask'] = self._apply_boundary_smoothing(
                    patch_info['patch_mask']
                )
                
                # Update used area
                used_mask |= patch_info['patch_mask']
                patches.append(patch_info)
            else:
                pass  # Failed to generate patch
        
        return patches
    
    def _generate_patch_knn_expansion(
        self,
        organized_pointcloud: np.ndarray,
        seed_point: Tuple[int, int],
        target_size: int,
        foreground_mask: np.ndarray,
        used_mask: np.ndarray
    ) -> Optional[Dict]:
        """
        Generate patch using iterative KNN expansion
        
        Args:
            organized_pointcloud: Organized point cloud [H, W, 3]
            seed_point: Seed point coordinates (row, col)
            target_size: Target patch size
            foreground_mask: Foreground mask
            used_mask: Used area mask
            
        Returns:
            Patch information dictionary or None if failed
        """
        H, W, _ = organized_pointcloud.shape
        seed_row, seed_col = seed_point
        
        # Initialize patch mask
        patch_mask = np.zeros((H, W), dtype=bool)
        patch_mask[seed_row, seed_col] = True
        
        # Build KDTree for 3D points
        points_3d = organized_pointcloud.reshape(-1, 3)
        valid_3d_mask = (np.abs(points_3d).sum(axis=1) > 1e-6) & foreground_mask.flatten()
        
        if np.sum(valid_3d_mask) < self.min_patch_size:
            return None
        
        valid_points = points_3d[valid_3d_mask]
        valid_indices = np.where(valid_3d_mask)[0]
        
        # Create mapping from linear index to 2D coordinates
        linear_to_2d = {}
        for i, linear_idx in enumerate(valid_indices):
            row = linear_idx // W
            col = linear_idx % W
            linear_to_2d[linear_idx] = (row, col)
        
        kdtree = cKDTree(valid_points)
        
        # Get seed point in 3D
        seed_linear_idx = seed_row * W + seed_col
        if seed_linear_idx not in valid_indices:
            return None
        
        seed_3d_idx = np.where(valid_indices == seed_linear_idx)[0][0]
        seed_3d_point = valid_points[seed_3d_idx]
        
        # Iterative expansion
        current_points = [seed_3d_idx]
        visited = set([seed_3d_idx])
        
        for iteration in range(self.max_iterations):
            if len(current_points) >= target_size:
                break
                
            new_points = []
            
            # Expand from current frontier points
            for point_idx in current_points:
                point_3d = valid_points[point_idx]
                
                # Find neighbors within expansion radius
                neighbor_indices = kdtree.query_ball_point(point_3d, r=self.expansion_radius)
                
                for neighbor_idx in neighbor_indices:
                    if neighbor_idx not in visited:
                        # Check if this point should be added
                        neighbor_linear_idx = valid_indices[neighbor_idx]
                        neighbor_row, neighbor_col = linear_to_2d[neighbor_linear_idx]
                        
                        # Skip if already used or outside foreground
                        if (used_mask[neighbor_row, neighbor_col] or 
                            not foreground_mask[neighbor_row, neighbor_col]):
                            continue
                        
                        # Add to patch
                        patch_mask[neighbor_row, neighbor_col] = True
                        new_points.append(neighbor_idx)
                        visited.add(neighbor_idx)
                        
                        if len(visited) >= target_size:
                            break
                
                if len(visited) >= target_size:
                    break
            
            # Update current points for next iteration
            current_points = new_points
            
            # Stop if no new points were added
            if not new_points:
                break
        
        # Check if patch meets minimum size requirement
        patch_size = np.sum(patch_mask)
        if patch_size < self.min_patch_size:
            return None
        
        # Get patch point coordinates
        patch_points = np.where(patch_mask)
        patch_points_list = list(zip(patch_points[0], patch_points[1]))
        
        return {
            'patch_mask': patch_mask,
            'seed_point': seed_point,
            'patch_points': patch_points_list,
            'patch_size': patch_size
        }
    
    def _apply_boundary_smoothing(self, patch_mask: np.ndarray) -> np.ndarray:
        """
        Apply Gaussian smoothing to patch boundaries
        
        Args:
            patch_mask: Binary patch mask [H, W]
            
        Returns:
            Smoothed patch mask [H, W]
        """
        # Convert to float for smoothing
        mask_float = patch_mask.astype(np.float32)
        
        # Apply Gaussian blur
        kernel_size = int(2 * self.smoothing_sigma * 3) | 1  # Ensure odd number
        smoothed = cv2.GaussianBlur(mask_float, (kernel_size, kernel_size), self.smoothing_sigma)
        
        # Apply threshold to get binary mask
        # Use adaptive threshold to maintain patch size
        threshold = 0.5
        result_mask = smoothed > threshold
        
        return result_mask
    
    def visualize_patches(
        self, 
        organized_pointcloud: np.ndarray, 
        patches: List[Dict],
        save_path: Optional[str] = None
    ):
        """
        Visualize selected patches
        
        Args:
            organized_pointcloud: Original point cloud [H, W, 3]
            patches: List of patch information
            save_path: Optional save path for visualization
        """
        import matplotlib.pyplot as plt
        
        H, W, _ = organized_pointcloud.shape
        
        # Create visualization
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # Original depth map
        depth = organized_pointcloud[:, :, 2]
        axes[0, 0].imshow(depth, cmap='viridis')
        axes[0, 0].set_title('Original Depth Map')
        axes[0, 0].axis('off')
        
        # Combined patch mask
        combined_mask = np.zeros((H, W))
        for i, patch in enumerate(patches):
            combined_mask += (i + 1) * patch['patch_mask']
        
        axes[0, 1].imshow(combined_mask, cmap='tab10')
        axes[0, 1].set_title(f'Selected Patches ({len(patches)} patches)')
        axes[0, 1].axis('off')
        
        # Patch sizes
        patch_sizes = [patch['patch_size'] for patch in patches]
        axes[1, 0].bar(range(len(patch_sizes)), patch_sizes)
        axes[1, 0].set_title('Patch Sizes')
        axes[1, 0].set_xlabel('Patch Index')
        axes[1, 0].set_ylabel('Number of Points')
        
        # Seed points overlay
        axes[1, 1].imshow(depth, cmap='viridis', alpha=0.7)
        for i, patch in enumerate(patches):
            seed_row, seed_col = patch['seed_point']
            axes[1, 1].plot(seed_col, seed_row, 'r*', markersize=10, label=f'Seed {i+1}')
        axes[1, 1].set_title('Seed Points')
        axes[1, 1].axis('off')
        axes[1, 1].legend()
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Patch visualization saved to: {save_path}")
        
        plt.show()


def test_patch_selector():
    """
    Test the LocalPatchSelector
    """
    # Create test data
    H, W = 224, 224
    organized_pc = np.random.rand(H, W, 3) * 2 - 1
    
    # Create realistic depth pattern
    center_x, center_y = W // 2, H // 2
    y_coords, x_coords = np.ogrid[:H, :W]
    distance = np.sqrt((x_coords - center_x)**2 + (y_coords - center_y)**2)
    max_distance = min(W, H) * 0.4
    
    # Set foreground region
    foreground_mask = distance <= max_distance
    organized_pc[~foreground_mask] = 0
    
    # Initialize selector
    config = {
        'min_patch_size': 50,
        'max_patch_size': 300,
        'expansion_radius': 0.03,
        'max_iterations': 15,
        'smoothing_sigma': 1.5
    }
    
    selector = LocalPatchSelector(config)
    
    # Select patches
    patches = selector.select_random_patches(
        organized_pc,
        num_patches=3,
        patch_coverage_ratio=0.2
    )
    
    print(f"Generated {len(patches)} patches")
    for i, patch in enumerate(patches):
        print(f"Patch {i+1}: {patch['patch_size']} points, seed at {patch['seed_point']}")
    
    # Visualize results
    selector.visualize_patches(organized_pc, patches)
    
    return patches


if __name__ == "__main__":
    test_patch_selector()