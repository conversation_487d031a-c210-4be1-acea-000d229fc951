"""
2D Shading Derivative Transformation
Implements post-deformation normal recalculation, lighting intensity change computation,
3D-to-2D light mapping, and Gaussian blur smoothing

Author: IA-CRF Project
Date: 2025-08-27
"""

import numpy as np
import open3d as o3d
from scipy.spatial import cKDTree
from scipy.ndimage import gaussian_filter
from typing import Tuple, List, Optional, Dict
import cv2


class ShadingDerivativeTransformer:
    """
    2D Shading Derivative Transformer
    Generates 2D shading changes based on 3D geometric deformations
    """
    
    def __init__(self, config: Dict = None):
        """
        Initialize shading derivative transformer
        
        Args:
            config: Configuration parameter dictionary
        """
        self.config = config or {}
        
        # Default parameters
        self.light_direction = np.array(self.config.get('light_direction', [0, 0, 1]))  # Default top-down lighting
        self.ambient_light = self.config.get('ambient_light', 0.3)                     # Ambient light intensity
        self.diffuse_strength = self.config.get('diffuse_strength', 0.7)               # Diffuse lighting strength
        self.normal_estimation_radius = self.config.get('normal_estimation_radius', 0.02)
        self.gaussian_blur_sigma = self.config.get('gaussian_blur_sigma', 1.0)         # Smoothing parameter
        self.shading_intensity_multiplier = self.config.get('shading_intensity_multiplier', 3.5)  # 温和化增强系数从6.0降低到3.5
        
        # 新增：增强异常区域变化的额外参数（温和化）
        self.anomaly_contrast_boost = self.config.get('anomaly_contrast_boost', 1.8)   # 温和化异常区域对比度增强系数
        self.local_enhancement_factor = self.config.get('local_enhancement_factor', 1.3)  # 温和化局部增强因子
        self.enable_adaptive_scaling = self.config.get('enable_adaptive_scaling', True)  # 自适应缩放开关
        
        # Normalize light direction
        self.light_direction = self.light_direction / np.linalg.norm(self.light_direction)
        
        print(f"ShadingDerivativeTransformer initialized with light direction: {self.light_direction}")
    
    def recalculate_normals_after_deformation(
        self,
        deformed_pointcloud: np.ndarray,
        patch_masks: List[np.ndarray] = None
    ) -> np.ndarray:
        """
        Recalculate surface normals after geometric deformation
        
        Args:
            deformed_pointcloud: Deformed point cloud [H, W, 3]
            patch_masks: Optional list of deformed patch masks for focused recalculation
            
        Returns:
            Updated normal vectors [H, W, 3]
        """
        H, W, _ = deformed_pointcloud.shape
        
        # 首先计算原始法向量作为基线
        original_normals = self._estimate_original_normals_improved(deformed_pointcloud)
        
        # 如果没有patch信息，返回全局重新计算的结果
        if patch_masks is None:
            return original_normals
        
        # 创建综合异常掩码
        combined_mask = np.zeros((H, W), dtype=bool)
        for mask in patch_masks:
            combined_mask |= mask
        
        # 在异常区域及其周围进行精细的法向量重新计算
        if np.sum(combined_mask) > 0:
            # 扩展异常区域以包括边界效应
            from scipy.ndimage import binary_dilation
            expanded_mask = binary_dilation(combined_mask, iterations=5)
            
            # 提取扩展区域的点云进行精细重新计算
            refined_normals = self._recalculate_normals_in_region(
                deformed_pointcloud, expanded_mask
            )
            
            # 更新异常区域的法向量
            result_normals = original_normals.copy()
            result_normals[expanded_mask] = refined_normals[expanded_mask]
            
        # 精细重新计算了像素的法向量
            return result_normals
        
        return original_normals
    
    def _estimate_original_normals_improved(self, pointcloud: np.ndarray) -> np.ndarray:
        """
        改进的法向量估算方法，使用更稳定的梯度计算
        
        Args:
            pointcloud: Point cloud [H, W, 3]
            
        Returns:
            Normal vectors [H, W, 3]
        """
        H, W, _ = pointcloud.shape
        normals = np.zeros((H, W, 3))
        
        # 使用更大的邻域进行更稳定的法向量计算
        for i in range(2, H-2):
            for j in range(2, W-2):
                if np.abs(pointcloud[i, j]).sum() > 1e-6:
                    # 获取中心点和周围点
                    p_center = pointcloud[i, j]
                    
                    # 使用多个方向的梯度进行平均
                    valid_vectors = []
                    
                    # X方向梯度（使用更大间距）
                    p_left = pointcloud[i, j-2] if np.abs(pointcloud[i, j-2]).sum() > 1e-6 else p_center
                    p_right = pointcloud[i, j+2] if np.abs(pointcloud[i, j+2]).sum() > 1e-6 else p_center
                    if not np.allclose(p_left, p_center) and not np.allclose(p_right, p_center):
                        grad_x = (p_right - p_left) / 2.0
                        valid_vectors.append(grad_x)
                    
                    # Y方向梯度
                    p_up = pointcloud[i-2, j] if np.abs(pointcloud[i-2, j]).sum() > 1e-6 else p_center
                    p_down = pointcloud[i+2, j] if np.abs(pointcloud[i+2, j]).sum() > 1e-6 else p_center
                    if not np.allclose(p_up, p_center) and not np.allclose(p_down, p_center):
                        grad_y = (p_down - p_up) / 2.0
                        valid_vectors.append(grad_y)
                    
                    # 如果有足够的有效向量，计算法向量
                    if len(valid_vectors) >= 2:
                        # 使用前两个向量计算法向量
                        normal = np.cross(valid_vectors[0], valid_vectors[1])
                        norm = np.linalg.norm(normal)
                        if norm > 1e-6:
                            normals[i, j] = normal / norm
                        else:
                            # 如果向量平行，使用默认法向量
                            normals[i, j] = [0, 0, 1]
                    else:
                        # 如果没有足够的信息，使用默认法向量
                        normals[i, j] = [0, 0, 1]
        
        return normals
    
    def _recalculate_normals_in_region(
        self, 
        pointcloud: np.ndarray, 
        region_mask: np.ndarray
    ) -> np.ndarray:
        """
        在指定区域内精细重新计算法向量
        
        Args:
            pointcloud: 点云数据 [H, W, 3]
            region_mask: 需要重新计算的区域掩码 [H, W]
            
        Returns:
            更新后的法向量 [H, W, 3]
        """
        H, W, _ = pointcloud.shape
        normals = np.zeros((H, W, 3))
        
        # 获取区域内的有效点
        region_points = []
        region_indices = []
        
        for i in range(H):
            for j in range(W):
                if region_mask[i, j] and np.abs(pointcloud[i, j]).sum() > 1e-6:
                    region_points.append(pointcloud[i, j])
                    region_indices.append((i, j))
        
        if len(region_points) < 10:
            # 如果点太少，使用简单方法
            return self._estimate_original_normals_improved(pointcloud)
        
        # 使用Open3D进行精细的法向量计算
        try:
            region_points = np.array(region_points)
            pcd = o3d.geometry.PointCloud()
            pcd.points = o3d.utility.Vector3dVector(region_points)
            
            # 使用更小的半径进行更精细的法向量估算
            pcd.estimate_normals(
                search_param=o3d.geometry.KDTreeSearchParamHybrid(
                    radius=self.normal_estimation_radius * 0.5,  # 使用更小的半径
                    max_nn=30
                )
            )
            
            # 确保法向量一致性
            pcd.orient_normals_consistent_tangent_plane(50)
            
            # 将法向量分配回原始位置
            estimated_normals = np.asarray(pcd.normals)
            for idx, (i, j) in enumerate(region_indices):
                if idx < len(estimated_normals):
                    normals[i, j] = estimated_normals[idx]
            
            print(f"    Open3D法向量估算: {len(estimated_normals)} 个点")
            
        except Exception as e:
            print(f"    Open3D法向量估算失败: {e}, 使用备用方法")
            normals = self._estimate_original_normals_improved(pointcloud)
        
        return normals
    
    def calculate_lighting_intensity_changes(
        self,
        original_normals: np.ndarray,
        deformed_normals: np.ndarray,
        patch_masks: List[np.ndarray] = None
    ) -> np.ndarray:
        """
        Calculate lighting intensity changes based on normal vector changes
        
        Args:
            original_normals: Original normal vectors [H, W, 3]
            deformed_normals: Deformed normal vectors [H, W, 3]
            patch_masks: Optional patch masks to focus calculations
            
        Returns:
            Lighting intensity change map [H, W]
        """
        H, W, _ = original_normals.shape
        
        # Calculate diffuse lighting for both normal sets
        original_diffuse = self._calculate_diffuse_lighting(original_normals)
        deformed_diffuse = self._calculate_diffuse_lighting(deformed_normals)
        
        # Calculate intensity changes
        intensity_changes = deformed_diffuse - original_diffuse
        
        # 增强策略：在异常区域应用更强的变化
        if patch_masks is not None:
            combined_mask = np.zeros((H, W), dtype=bool)
            for mask in patch_masks:
                combined_mask |= mask
            
            # 首先应用基础强度乘数
            intensity_changes[combined_mask] *= self.shading_intensity_multiplier
            
            # 额外增强：对异常区域应用对比度增强
            if self.enable_adaptive_scaling:
                # 计算异常区域的变化幅度
                anomaly_changes = intensity_changes[combined_mask]
                if len(anomaly_changes) > 0:
                    # 自适应增强：根据变化幅度调整增强系数（更保守）
                    change_magnitude = np.abs(anomaly_changes).mean()
                    if change_magnitude < 0.05:  # 降低阈值，减少过度增强
                        adaptive_boost = self.anomaly_contrast_boost * (0.05 / max(change_magnitude, 1e-6))
                        adaptive_boost = min(adaptive_boost, 4.0)  # 大幅降低最大增强系数
                        intensity_changes[combined_mask] *= adaptive_boost
                    # 应用温和自适应增强
                    else:
                        # 正常情况下应用固定对比度增强
                        intensity_changes[combined_mask] *= self.anomaly_contrast_boost
        # 应用固定对比度增强
            
            # 局部增强：在异常区域中心区域应用更强的变化
            if self.local_enhancement_factor > 1.0:
                from scipy.ndimage import binary_erosion
                # 创建异常区域的内核区域
                core_mask = binary_erosion(combined_mask, iterations=2)
                if np.sum(core_mask) > 0:
                    intensity_changes[core_mask] *= self.local_enhancement_factor
                    # 应用局部核心增强
        
        return intensity_changes
    
    def _calculate_diffuse_lighting(self, normals: np.ndarray) -> np.ndarray:
        """
        Calculate diffuse lighting based on normal vectors
        
        Args:
            normals: Normal vectors [H, W, 3]
            
        Returns:
            Diffuse lighting intensity [H, W]
        """
        # Calculate dot product with light direction
        dot_product = np.sum(normals * self.light_direction[np.newaxis, np.newaxis, :], axis=2)
        
        # Clamp to [0, 1] for diffuse lighting (only front-facing surfaces)
        diffuse = np.maximum(0, dot_product)
        
        # 增强策略：使用非线性变换增加对比度
        # 使用指数函数提高光照对比度
        enhanced_diffuse = np.power(diffuse, 0.7)  # Gamma校正，提高中间色调
        
        # Combine with ambient lighting
        lighting = self.ambient_light + self.diffuse_strength * enhanced_diffuse
        
        # 最终对比度增强：使用S型曲线增加动态范围
        # Sigmoid函数增加中间值的对比度
        contrast_enhanced = 1.0 / (1.0 + np.exp(-12 * (lighting - 0.5)))
        
        # 混合原始和增强结果，避免过度增强
        final_lighting = 0.6 * lighting + 0.4 * contrast_enhanced
        
        # Clamp to [0, 1]
        final_lighting = np.clip(final_lighting, 0, 1)
        
        return final_lighting
    
    def map_3d_to_2d_lighting(
        self,
        intensity_changes: np.ndarray,
        original_shading: Optional[np.ndarray] = None,
        patch_masks: Optional[List[np.ndarray]] = None,
        preserve_global_consistency: bool = True
    ) -> np.ndarray:
        """
        增量式3D光照变化映射到2D阴影图像
        核心理念：在真实光照环境中"注入"局部扰动，而非全局重建
        
        Args:
            intensity_changes: 光照强度变化 [H, W]
            original_shading: 原始阴影图像 [H, W, 3] - 保留真实光照环境
            patch_masks: 异常区域掩码列表，用于精确定位扰动区域
            preserve_global_consistency: 是否保持全局光照一致性
            
        Returns:
            Modified 2D shading image [H, W, 3] - 仅在异常区域有变化
        """
        H, W = intensity_changes.shape
        
        # 必须有原始阴影图作为"底图"，这是真实光照环境的载体
        if original_shading is None:
            print("Warning: No original shading provided, creating synthetic baseline")
            original_shading = self._create_synthetic_shading(H, W)
        else:
            # 调整原始阴影图尺寸以匹配强度变化维度
            original_shading = self._resize_shading_image(original_shading, (H, W))
        
        # 确保原始阴影图格式正确
        if len(original_shading.shape) == 2:
            original_shading = np.stack([original_shading] * 3, axis=2)
        elif original_shading.shape[2] == 1:
            original_shading = np.repeat(original_shading, 3, axis=2)
        
        # 核心理念：以原始阴影图为"底图"，只在异常区域注入变化
        modified_shading = original_shading.copy()  # 保持全局光照一致性
        
        if preserve_global_consistency and patch_masks is not None:
            # === 增量式扰动方法 ===
            # 只在patch区域应用光照变化，保持其他区域完全不变
            
            # 创建综合异常掩码
            combined_anomaly_mask = np.zeros((H, W), dtype=bool)
            for mask in patch_masks:
                combined_anomaly_mask |= mask
            
        # 增量式扰动：只在异常区域注入光照变化
            
            # 创建光照变化掩码：只在异常区域生效
            masked_intensity_changes = intensity_changes.copy()
            masked_intensity_changes[~combined_anomaly_mask] = 0  # 非异常区域变化为0
            
            # 在异常区域注入物理一致的光照扰动
            for channel in range(3):
                # 增强的通道权重差异（增加视觉对比度）
                channel_weight = [1.2, 1.0, 0.8][channel]  # 增强R/B通道差异
                channel_changes = masked_intensity_changes * channel_weight
                
                # 额外增强：在异常区域应用非线性变化放大
                enhanced_changes = self._apply_nonlinear_enhancement(channel_changes, combined_anomaly_mask)
                
                # 关键：只在异常区域叠加变化，其他区域保持原始值
                # 添加智能边界处理，防止产生黑色区域
                temp_channel = original_shading[:, :, channel] + enhanced_changes
                
                # 智能边界处理：避免简单clipping导致的黑色区域
                temp_channel = self._apply_intelligent_boundary_processing(
                    temp_channel, original_shading[:, :, channel], combined_anomaly_mask
                )
                
                modified_shading[:, :, channel] = temp_channel
            
            # 边界平滑：只在异常区域边界进行轻微平滑，避免突变
            modified_shading = self._apply_localized_smoothing(
                modified_shading, original_shading, combined_anomaly_mask
            )
            
        else:
            # === 传统全局方法（仅作为备选） ===
            print("  使用传统全局光照重建方法")
            for channel in range(3):
                channel_weight = [1.0, 0.9, 0.8][channel]
                channel_changes = intensity_changes * channel_weight
                modified_shading[:, :, channel] = (
                    original_shading[:, :, channel] + channel_changes
                )
        
        # 确保像素值在有效范围[0,1]
        modified_shading = np.clip(modified_shading, 0, 1)
        
        # 计算并报告变化统计
        if patch_masks is not None:
            self._report_local_perturbation_stats(
                original_shading, modified_shading, patch_masks
            )
        
        return modified_shading
    
    def _apply_nonlinear_enhancement(
        self, 
        channel_changes: np.ndarray, 
        anomaly_mask: np.ndarray
    ) -> np.ndarray:
        """
        对异常区域应用安全的非线性增强，提高视觉对比度
        使用温和的增强策略避免产生黑色区域
        
        Args:
            channel_changes: 单通道光照变化 [H, W]
            anomaly_mask: 异常区域掩码 [H, W]
            
        Returns:
            增强后的光照变化 [H, W]
        """
        enhanced_changes = channel_changes.copy()
        
        if np.sum(anomaly_mask) > 0:
            # 获取异常区域的变化值
            anomaly_changes = enhanced_changes[anomaly_mask]
            
            # 安全的非线性增强策略
            sign_mask = np.sign(anomaly_changes)  # 保持方向
            abs_changes = np.abs(anomaly_changes)
            
            # 温和的多阶段增强策略
            enhanced_abs = np.zeros_like(abs_changes)
            
            # 第一阶段：温和的指数增强
            base_enhanced = np.power(abs_changes + 1e-8, 0.6) * 0.08  # 更温和的指数变换
            
            # 第二阶段：S型曲线增强
            sigmoid_enhanced = 1.0 / (1.0 + np.exp(-8 * (abs_changes - 0.005))) * 0.12  # 更温和的sigmoid
            
            # 第三阶段：线性增强（限制最大放大倍数）
            linear_enhanced = abs_changes * 8.0  # 温和的线性放大
            
            # 第四阶段：安全的最小值设置
            forced_minimum = np.full_like(abs_changes, 0.03)  # 更小的强制最小变化
            
            # 取所有增强策略的最大值，但限制增强的最大值
            enhanced_abs = np.maximum.reduce([
                base_enhanced,
                sigmoid_enhanced, 
                linear_enhanced,
                forced_minimum
            ])
            
            # 关键：限制增强的最大值以避免过度增强
            enhanced_abs = np.minimum(enhanced_abs, 0.25)  # 防止过度增强
            
            # 恢复方向并应用增强
            enhanced_anomaly_changes = sign_mask * enhanced_abs
            
            # 安全范围限制：确保最终结果在合理范围内
            min_safe_change = -0.08
            max_safe_change = 0.3
            enhanced_anomaly_changes = np.clip(enhanced_anomaly_changes, min_safe_change, max_safe_change)
            
            # 将增强后的值放回原位置
            enhanced_changes[anomaly_mask] = enhanced_anomaly_changes
            
            # 温和的区域性增强：在中心区域应用适度的变化
            from scipy.ndimage import binary_erosion
            core_mask = binary_erosion(anomaly_mask, iterations=1)
            if np.sum(core_mask) > 0:
                enhanced_changes[core_mask] *= 1.2  # 中心区域温和增强
            
            print(f"    安全增强统计: 原始平均={np.mean(np.abs(anomaly_changes)):.6f}, 增强后平均={np.mean(np.abs(enhanced_anomaly_changes)):.6f}, 放大倍数={np.mean(np.abs(enhanced_anomaly_changes)) / max(np.mean(np.abs(anomaly_changes)), 1e-8):.1f}x")
        
        return enhanced_changes
    
    def _apply_intelligent_boundary_processing(
        self,
        modified_channel: np.ndarray,
        original_channel: np.ndarray, 
        anomaly_mask: np.ndarray
    ) -> np.ndarray:
        """
        智能边界处理，替代简单clipping避免产生黑色区域
        
        Args:
            modified_channel: 修改后的单通道数据
            original_channel: 原始单通道数据
            anomaly_mask: 异常区域掩码
            
        Returns:
            处理后的单通道数据
        """
        result_channel = modified_channel.copy()
        
        # 只在异常区域应用智能处理
        if np.sum(anomaly_mask) > 0:
            anomaly_pixels = result_channel[anomaly_mask]
            original_pixels = original_channel[anomaly_mask]
            
            # 检测超出范围的像素
            over_bright = anomaly_pixels > 1.0
            over_dark = anomaly_pixels < 0.0
            
            if np.sum(over_bright) > 0 or np.sum(over_dark) > 0:
            # 智能边界处理: 过亮和过暗像素
                
                # 对于过亮的像素，使用温和压缩而非直接截断
                if np.sum(over_bright) > 0:
                    # 使用指数衰减函数压缩过亮像素
                    excess = anomaly_pixels[over_bright] - 1.0
                    compressed_excess = 1.0 - np.exp(-excess)  # 指数衰减
                    anomaly_pixels[over_bright] = 1.0 - compressed_excess * 0.2  # 保持在[0.8, 1.0]范围
                
                # 对于过暗的像素，使用温和提升而非直接截断
                if np.sum(over_dark) > 0:
                    # 使用指数提升函数处理过暗像素
                    deficit = -anomaly_pixels[over_dark]  # 负值的绝对值
                    compressed_deficit = 1.0 - np.exp(-deficit)  # 指数衰减
                    anomaly_pixels[over_dark] = compressed_deficit * 0.2  # 保持在[0, 0.2]范围
                
                # 更新结果
                result_channel[anomaly_mask] = anomaly_pixels
            
            # 最终安全检查：确保所有值都在[0,1]范围内
            result_channel = np.clip(result_channel, 0.0, 1.0)
        
        return result_channel
    
    def _apply_minimal_boundary_smoothing(
        self, 
        modified_shading: np.ndarray, 
        patch_masks: List[np.ndarray],
        boundary_width: int = 2
    ) -> np.ndarray:
        """
        在异常区域边界应用最小化平滑
        
        Args:
            modified_shading: 修改后的阴影图
            patch_masks: 异常区域掩码列表
            boundary_width: 边界平滑宽度
            
        Returns:
            平滑后的阴影图
        """
        from scipy.ndimage import binary_dilation
        
        # 创建综合异常掩码
        H, W = modified_shading.shape[:2]
        combined_mask = np.zeros((H, W), dtype=bool)
        for mask in patch_masks:
            combined_mask |= mask
        
        if np.sum(combined_mask) == 0:
            return modified_shading
        
        # 创建边界区域：异常区域向外扩展的边界
        dilated_mask = binary_dilation(combined_mask, iterations=boundary_width)
        boundary_mask = dilated_mask & (~combined_mask)
        
        if np.sum(boundary_mask) > 0:
            smoothed_shading = modified_shading.copy()
            
            # 只在边界区域应用极轻微的平滑
            for channel in range(3):
                channel_data = modified_shading[:, :, channel]
                smoothed_channel = gaussian_filter(channel_data, sigma=0.5)  # 极小的平滑半径
                
                # 只在边界区域使用平滑结果
                smoothed_shading[boundary_mask, channel] = smoothed_channel[boundary_mask]
            
        # 边界平滑
            return smoothed_shading
        
        return modified_shading
    
    def _apply_localized_smoothing(
        self, 
        modified_shading: np.ndarray, 
        original_shading: np.ndarray,
        anomaly_mask: np.ndarray,
        boundary_width: int = 3
    ) -> np.ndarray:
        """
        在异常区域边界应用局部平滑，避免突变
        保持非异常区域完全不变
        
        Args:
            modified_shading: 修改后的阴影图
            original_shading: 原始阴影图 
            anomaly_mask: 异常区域掩码
            boundary_width: 边界平滑宽度
            
        Returns:
            平滑后的阴影图
        """
        from scipy.ndimage import binary_dilation, binary_erosion
        
        # 创建边界区域：异常区域的外边界
        dilated_mask = binary_dilation(anomaly_mask, iterations=boundary_width)
        eroded_mask = binary_erosion(anomaly_mask, iterations=1)
        boundary_mask = dilated_mask & (~eroded_mask)
        
        if np.sum(boundary_mask) > 0:
            # 只在边界区域应用轻微平滑
            smoothed_shading = modified_shading.copy()
            
            for channel in range(3):
                # 边界区域的轻微高斯平滑
                channel_data = modified_shading[:, :, channel]
                smoothed_channel = gaussian_filter(channel_data, sigma=1.0)
                
                # 只在边界区域使用平滑结果
                smoothed_shading[boundary_mask, channel] = smoothed_channel[boundary_mask]
            
            return smoothed_shading
        
        return modified_shading
    
    def _report_local_perturbation_stats(
        self, 
        original_shading: np.ndarray,
        modified_shading: np.ndarray, 
        patch_masks: List[np.ndarray]
    ):
        """
        报告局部扰动统计信息
        
        Args:
            original_shading: 原始阴影图
            modified_shading: 修改后阴影图
            patch_masks: 异常区域掩码列表
        """
        # 计算全局变化
        total_diff = np.abs(modified_shading - original_shading)
        total_changed_pixels = np.sum(total_diff.sum(axis=2) > 1e-6)
        
        # 计算异常区域变化
        combined_mask = np.zeros(original_shading.shape[:2], dtype=bool)
        for mask in patch_masks:
            combined_mask |= mask
            
        anomaly_pixels = np.sum(combined_mask)
        max_change_in_anomaly = np.max(total_diff[combined_mask]) if anomaly_pixels > 0 else 0
        mean_change_in_anomaly = np.mean(total_diff[combined_mask]) if anomaly_pixels > 0 else 0
        
        # 计算非异常区域变化（应该为0或极小）
        non_anomaly_pixels = np.sum(~combined_mask)
        max_change_in_normal = np.max(total_diff[~combined_mask]) if non_anomaly_pixels > 0 else 0
        
        # === 增量式扰动统计 ===
    
    def _resize_shading_image(self, shading_image: np.ndarray, target_size: Tuple[int, int]) -> np.ndarray:
        """
        Resize shading image to target size
        
        Args:
            shading_image: Input shading image [H, W, 3] or [H, W]
            target_size: Target size (H, W)
            
        Returns:
            Resized shading image [target_H, target_W, 3]
        """
        import cv2
        
        target_H, target_W = target_size
        
        # Handle different input shapes
        if len(shading_image.shape) == 2:
            # Grayscale image
            resized = cv2.resize(shading_image, (target_W, target_H), interpolation=cv2.INTER_LINEAR)
            resized = np.stack([resized] * 3, axis=2)  # Convert to 3-channel
        elif len(shading_image.shape) == 3:
            if shading_image.shape[2] == 1:
                # Single channel image
                resized = cv2.resize(shading_image[:, :, 0], (target_W, target_H), interpolation=cv2.INTER_LINEAR)
                resized = np.stack([resized] * 3, axis=2)
            elif shading_image.shape[2] == 3:
                # RGB image
                resized = cv2.resize(shading_image, (target_W, target_H), interpolation=cv2.INTER_LINEAR)
            else:
                raise ValueError(f"Unsupported shading image shape: {shading_image.shape}")
        else:
            raise ValueError(f"Unsupported shading image shape: {shading_image.shape}")
        
        # Ensure values are in [0, 1] range
        if resized.max() > 1.0:
            resized = resized / 255.0
        
        return resized
    
    def _create_synthetic_shading(self, H: int, W: int) -> np.ndarray:
        """
        Create synthetic shading image for testing
        
        Args:
            H: Image height
            W: Image width
            
        Returns:
            Synthetic shading image [H, W, 3]
        """
        # Create smooth gradient-like shading
        y_coords, x_coords = np.ogrid[:H, :W]
        center_x, center_y = W // 2, H // 2
        
        # Distance from center
        distance = np.sqrt((x_coords - center_x)**2 + (y_coords - center_y)**2)
        max_distance = np.sqrt(center_x**2 + center_y**2)
        
        # Create shading pattern
        shading = 0.3 + 0.7 * (1 - distance / max_distance)
        shading = np.clip(shading, 0, 1)
        
        # Convert to 3-channel
        shading_3d = np.stack([shading] * 3, axis=2)
        
        return shading_3d
    
    def apply_gaussian_smoothing(
        self,
        shading_image: np.ndarray,
        patch_masks: List[np.ndarray] = None,
        adaptive_sigma: bool = True
    ) -> np.ndarray:
        """
        Apply Gaussian smoothing to 2D shading image
        
        Args:
            shading_image: Shading image [H, W, 3]
            patch_masks: Optional patch masks for adaptive smoothing
            adaptive_sigma: Whether to use adaptive smoothing strength
            
        Returns:
            Smoothed shading image [H, W, 3]
        """
        smoothed_image = shading_image.copy()
        
        if patch_masks is not None and adaptive_sigma:
            # Apply different smoothing to deformed and non-deformed regions
            combined_mask = np.zeros(shading_image.shape[:2], dtype=bool)
            for mask in patch_masks:
                combined_mask |= mask
            
            # Stronger smoothing in deformed areas
            deformed_sigma = self.gaussian_blur_sigma * 1.5
            normal_sigma = self.gaussian_blur_sigma * 0.5
            
            for channel in range(3):
                # Smooth entire image with normal sigma
                smoothed_channel = gaussian_filter(shading_image[:, :, channel], sigma=normal_sigma)
                
                # Apply stronger smoothing to deformed areas
                strong_smoothed = gaussian_filter(shading_image[:, :, channel], sigma=deformed_sigma)
                smoothed_channel[combined_mask] = strong_smoothed[combined_mask]
                
                smoothed_image[:, :, channel] = smoothed_channel
        else:
            # Uniform smoothing
            for channel in range(3):
                smoothed_image[:, :, channel] = gaussian_filter(
                    shading_image[:, :, channel], 
                    sigma=self.gaussian_blur_sigma
                )
        
        return smoothed_image
    
    def _apply_minimal_boundary_smoothing(
        self, 
        modified_shading: np.ndarray, 
        patch_masks: List[np.ndarray],
        sigma: float = 0.5
    ) -> np.ndarray:
        """
        对增量式方法生成的结果进行最小化边界平滑
        避免过度平滑导致异常信号丢失
        
        Args:
            modified_shading: 已经应用增量式扰动的阴影图
            patch_masks: 异常区域掩码列表
            sigma: 最小平滑参数
            
        Returns:
            最终平滑结果
        """
        from scipy.ndimage import binary_dilation
        
        # 创建异常区域边界
        combined_mask = np.zeros(modified_shading.shape[:2], dtype=bool)
        for mask in patch_masks:
            combined_mask |= mask
            
        # 扩展区域用于边界平滑
        boundary_mask = binary_dilation(combined_mask, iterations=2) & (~combined_mask)
        
        if np.sum(boundary_mask) == 0:
            return modified_shading
            
        smoothed_result = modified_shading.copy()
        
        # 仅在边界区域应用极小平滑
        for channel in range(3):
            channel_data = modified_shading[:, :, channel]
            smoothed_channel = gaussian_filter(channel_data, sigma=sigma)
            
            # 只更新边界区域
            smoothed_result[boundary_mask, channel] = smoothed_channel[boundary_mask]
            
        # 对边界像素应用了最小平滑
        return smoothed_result
    
    def generate_shading_derivatives(
        self,
        original_pointcloud: np.ndarray,
        deformed_pointcloud: np.ndarray,
        patch_masks: List[np.ndarray],
        original_shading: Optional[np.ndarray] = None,
        original_normals: Optional[np.ndarray] = None
    ) -> Dict:
        """
        Complete pipeline for generating 2D shading derivatives from 3D deformations
        
        Args:
            original_pointcloud: Original point cloud [H, W, 3]
            deformed_pointcloud: Deformed point cloud [H, W, 3]
            patch_masks: List of deformed patch masks
            original_shading: Optional original shading image [H, W, 3]
            original_normals: Optional pre-computed original normals [H, W, 3]
            
        Returns:
            Dictionary containing:
            - modified_shading: Final modified shading image [H, W, 3]
            - intensity_changes: Lighting intensity changes [H, W]
            - deformed_normals: Recalculated normals [H, W, 3]
            - original_normals: Original normals [H, W, 3]
        """
        # Generating 2D shading derivatives
        
        # Step 1: Recalculate normals after deformation
        # Step 1: Recalculating normals after deformation
        deformed_normals = self.recalculate_normals_after_deformation(
            deformed_pointcloud, 
            patch_masks
        )
        
        # Step 2: Get original normals for comparison
        if original_normals is None:
        # Step 2: Estimating original normals
            original_normals = self.recalculate_normals_after_deformation(original_pointcloud)
        
        # Step 3: Calculate lighting intensity changes
        # Step 3: Calculating lighting intensity changes
        intensity_changes = self.calculate_lighting_intensity_changes(
            original_normals,
            deformed_normals,
            patch_masks
        )
        
        # Step 4: Map 3D changes to 2D shading using incremental perturbation
        # Step 4: Applying incremental lighting perturbation to original shading
        modified_shading = self.map_3d_to_2d_lighting(
            intensity_changes,
            original_shading,
            patch_masks=patch_masks,  # 启用增量式扰动
            preserve_global_consistency=True  # 保持全局光照一致性
        )
        
        # Step 5: Apply minimal additional smoothing (since localized smoothing already applied)
        # Step 5: Applying minimal boundary smoothing
        # 由于增量式方法已经包含了局部平滑，这里只做轻微的最终处理
        if patch_masks is not None:
            # 仅在异常区域边界进行最小化平滑
            smoothed_shading = self._apply_minimal_boundary_smoothing(modified_shading, patch_masks)
        else:
            # 如果没有patch信息，使用传统平滑
            smoothed_shading = self.apply_gaussian_smoothing(modified_shading, patch_masks)
        
        # Calculate statistics
        total_change = np.sum(np.abs(intensity_changes))
        max_change = np.max(np.abs(intensity_changes))
        mean_change = np.mean(np.abs(intensity_changes[intensity_changes != 0])) if np.any(intensity_changes != 0) else 0
        
        # Shading derivative generation complete
        
        return {
            'modified_shading': smoothed_shading,
            'intensity_changes': intensity_changes,
            'deformed_normals': deformed_normals,
            'original_normals': original_normals,
            'shading_statistics': {
                'total_change': total_change,
                'max_change': max_change,
                'mean_change': mean_change,
                'num_changed_pixels': np.sum(intensity_changes != 0)
            }
        }
    
    def visualize_shading_derivatives(
        self,
        results: Dict,
        original_shading: Optional[np.ndarray] = None,
        save_path: Optional[str] = None
    ):
        """
        Visualize shading derivative results
        
        Args:
            results: Results from generate_shading_derivatives
            original_shading: Optional original shading for comparison
            save_path: Optional save path for visualization
        """
        import matplotlib.pyplot as plt
        
        modified_shading = results['modified_shading']
        intensity_changes = results['intensity_changes']
        deformed_normals = results['deformed_normals']
        original_normals = results['original_normals']
        
        # Create visualization
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # Original shading
        if original_shading is not None:
            axes[0, 0].imshow(original_shading)
            axes[0, 0].set_title('Original Shading')
        else:
            axes[0, 0].imshow(np.mean(modified_shading, axis=2), cmap='gray')
            axes[0, 0].set_title('Reference Shading')
        axes[0, 0].axis('off')
        
        # Modified shading
        axes[0, 1].imshow(modified_shading)
        axes[0, 1].set_title('Modified Shading')
        axes[0, 1].axis('off')
        
        # Intensity changes
        im2 = axes[0, 2].imshow(intensity_changes, cmap='RdBu_r', vmin=-0.5, vmax=0.5)
        axes[0, 2].set_title('Intensity Changes')
        axes[0, 2].axis('off')
        plt.colorbar(im2, ax=axes[0, 2], fraction=0.046, pad=0.04)
        
        # Original normals (color-coded)
        normal_vis_orig = (original_normals + 1) / 2  # Normalize to [0,1]
        axes[1, 0].imshow(normal_vis_orig)
        axes[1, 0].set_title('Original Normals')
        axes[1, 0].axis('off')
        
        # Deformed normals (color-coded)
        normal_vis_def = (deformed_normals + 1) / 2  # Normalize to [0,1]
        axes[1, 1].imshow(normal_vis_def)
        axes[1, 1].set_title('Deformed Normals')
        axes[1, 1].axis('off')
        
        # Statistics
        stats = results['shading_statistics']
        stats_text = f"Total change: {stats['total_change']:.4f}\n"
        stats_text += f"Max change: {stats['max_change']:.4f}\n"
        stats_text += f"Mean change: {stats['mean_change']:.4f}\n"
        stats_text += f"Changed pixels: {stats['num_changed_pixels']}"
        
        axes[1, 2].text(0.1, 0.5, stats_text, transform=axes[1, 2].transAxes,
                       fontsize=12, verticalalignment='center', fontfamily='monospace')
        axes[1, 2].set_title('Shading Statistics')
        axes[1, 2].axis('off')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Shading derivative visualization saved to: {save_path}")
        
        plt.show()


def test_shading_derivative_transformer():
    """
    Test the ShadingDerivativeTransformer
    """
    from patch_selector import LocalPatchSelector
    from normal_guided_displacer import NormalGuidedDisplacer
    
    # Create test data
    H, W = 224, 224
    organized_pc = np.random.rand(H, W, 3) * 2 - 1
    
    # Create realistic object shape
    center_x, center_y = W // 2, H // 2
    y_coords, x_coords = np.ogrid[:H, :W]
    distance = np.sqrt((x_coords - center_x)**2 + (y_coords - center_y)**2)
    max_distance = min(W, H) * 0.4
    
    foreground_mask = distance <= max_distance
    organized_pc[~foreground_mask] = 0
    organized_pc[:, :, 2] = np.where(foreground_mask, 
                                   0.5 + 0.3 * np.cos(distance / max_distance * np.pi),
                                   0)
    
    # Generate patches and deformations
    patch_selector = LocalPatchSelector({'min_patch_size': 100, 'max_patch_size': 300})
    patches = patch_selector.select_random_patches(organized_pc, num_patches=2)
    
    if not patches:
        print("No patches generated for testing")
        return
    
    displacer = NormalGuidedDisplacer({'displacement_strength_range': [0.02, 0.08]})
    deformed_pc, _ = displacer.apply_multiple_deformations(organized_pc, patches)
    
    # Extract patch masks
    patch_masks = [patch['patch_mask'] for patch in patches]
    
    # Initialize transformer
    transformer_config = {
        'light_direction': [0.5, 0.3, 0.8],
        'ambient_light': 0.2,
        'diffuse_strength': 0.8,
        'gaussian_blur_sigma': 1.5,
        'shading_intensity_multiplier': 2.5
    }
    
    transformer = ShadingDerivativeTransformer(transformer_config)
    
    # Generate shading derivatives
    results = transformer.generate_shading_derivatives(
        organized_pc,
        deformed_pc,
        patch_masks
    )
    
    print("Shading derivative transformation complete!")
    print(f"Generated {results['shading_statistics']['num_changed_pixels']} changed pixels")
    
    # Visualize results
    transformer.visualize_shading_derivatives(results)
    
    return results


if __name__ == "__main__":
    test_shading_derivative_transformer()