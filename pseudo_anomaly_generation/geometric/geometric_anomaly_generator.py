"""
Geometric Anomaly Generator
Main integration class for geometric anomaly generation workflow
Combines patch selection, normal-guided displacement, and shading derivative transformation

Author: IA-CRF Project
Date: 2025-08-27
"""

import numpy as np
import cv2
from typing import Tuple, List, Optional, Dict, Union
from pathlib import Path
import json
from datetime import datetime
import open3d as o3d

from .patch_selector import LocalPatchSelector
from .normal_guided_displacer import NormalGuidedDisplacer
from .shading_derivative_transformer import ShadingDerivativeTransformer


def convert_numpy_types(obj):
    """
    Convert NumPy types to native Python types for JSON serialization
    
    Args:
        obj: Object to convert
        
    Returns:
        Converted object
    """
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(convert_numpy_types(item) for item in obj)
    else:
        return obj


def save_organized_pointcloud_as_pcd(
    organized_pointcloud: np.ndarray,
    save_path: str,
    normals: Optional[np.ndarray] = None,
    colors: Optional[np.ndarray] = None
) -> bool:
    """
    Save organized point cloud as .pcd file
    
    Args:
        organized_pointcloud: Organized point cloud [H, W, 3]
        save_path: Path to save the .pcd file
        normals: Optional normal vectors [H, W, 3]
        colors: Optional colors [H, W, 3] in range [0, 1]
        
    Returns:
        True if saved successfully, False otherwise
    """
    try:
        H, W, _ = organized_pointcloud.shape
        
        # Convert organized point cloud to unorganized
        points = organized_pointcloud.reshape(-1, 3)
        
        # Filter out invalid points (zeros or very small values)
        valid_mask = (np.abs(points).sum(axis=1) > 1e-6)
        valid_points = points[valid_mask]
        
        if len(valid_points) == 0:
            print(f"Warning: No valid points to save in {save_path}")
            return False
        
        # Create Open3D point cloud
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(valid_points)
        
        # Add normals if provided
        if normals is not None:
            normals_flat = normals.reshape(-1, 3)
            valid_normals = normals_flat[valid_mask]
            # Ensure normals are unit vectors
            norms = np.linalg.norm(valid_normals, axis=1, keepdims=True)
            valid_norms_mask = norms.flatten() > 1e-6
            if np.any(valid_norms_mask):
                valid_normals[valid_norms_mask] = valid_normals[valid_norms_mask] / norms[valid_norms_mask]
                pcd.normals = o3d.utility.Vector3dVector(valid_normals)
        
        # Add colors if provided
        if colors is not None:
            colors_flat = colors.reshape(-1, 3)
            valid_colors = colors_flat[valid_mask]
            # Ensure colors are in [0, 1] range
            valid_colors = np.clip(valid_colors, 0, 1)
            pcd.colors = o3d.utility.Vector3dVector(valid_colors)
        
        # Save as .pcd file
        success = o3d.io.write_point_cloud(str(save_path), pcd)
        
        if success:
            print(f"Point cloud saved to: {save_path} ({len(valid_points)} points)")
        else:
            print(f"Failed to save point cloud to: {save_path}")
        
        return success
        
    except Exception as e:
        print(f"Error saving point cloud to {save_path}: {e}")
        return False


class GeometricAnomalyGenerator:
    """
    Geometric Anomaly Generator
    Complete pipeline for generating geometric anomalies with physically consistent 2D shading derivatives
    """
    
    def __init__(self, config: Dict = None):
        """
        Initialize geometric anomaly generator
        
        Args:
            config: Configuration parameter dictionary
        """
        self.config = config or {}
        
        # Initialize submodules
        patch_config = self.config.get('patch_selection', {})
        displacement_config = self.config.get('displacement', {})
        shading_config = self.config.get('shading_transformation', {})
        
        self.patch_selector = LocalPatchSelector(patch_config)
        self.displacer = NormalGuidedDisplacer(displacement_config)
        self.shading_transformer = ShadingDerivativeTransformer(shading_config)
        
        # Generation parameters
        self.num_patches_range = self.config.get('num_patches_range', [1, 3])
        self.patch_coverage_ratio = self.config.get('patch_coverage_ratio', 0.15)
        self.anomaly_probability = self.config.get('anomaly_probability', 1.0)
        
        # print(f"GeometricAnomalyGenerator initialized with {self.num_patches_range} patches range")
    
    def load_geometric_features(self, features_path: str) -> np.ndarray:
        """
        Load pre-computed 7D geometric features
        
        Args:
            features_path: Path to .npy file containing geometric features
            
        Returns:
            Geometric features [H*W, 7] where format is [x,y,z,nx,ny,nz,curvature]
        """
        if not Path(features_path).exists():
            raise FileNotFoundError(f"Geometric features file not found: {features_path}")
        
        features = np.load(features_path)
        
        if features.shape[1] != 7:
            raise ValueError(f"Expected 7D features, got {features.shape[1]}D")
        
        print(f"Loaded geometric features from {features_path}, shape: {features.shape}")
        return features
    
    def features_to_organized_pointcloud(
        self, 
        geometric_features: np.ndarray, 
        target_size: Tuple[int, int] = (224, 224)
    ) -> np.ndarray:
        """
        Convert geometric features to organized point cloud
        
        Args:
            geometric_features: Geometric features [H*W, 7]
            target_size: Target organized point cloud size (H, W)
            
        Returns:
            Organized point cloud [H, W, 3]
        """
        H, W = target_size
        expected_points = H * W
        
        if geometric_features.shape[0] != expected_points:
            raise ValueError(f"Feature count {geometric_features.shape[0]} doesn't match target size {expected_points}")
        
        # Extract XYZ coordinates (first 3 dimensions)
        xyz_coords = geometric_features[:, :3]
        organized_pc = xyz_coords.reshape(H, W, 3)
        
        return organized_pc
    
    def create_depth_map_from_pointcloud(self, organized_pointcloud: np.ndarray) -> np.ndarray:
        """
        Create depth map from organized point cloud
        
        Args:
            organized_pointcloud: Organized point cloud [H, W, 3]
            
        Returns:
            Depth map [H, W]
        """
        return organized_pointcloud[:, :, 2].copy()
    
    def generate_single_geometric_anomaly(
        self,
        geometric_features: np.ndarray,
        original_shading: Optional[np.ndarray] = None,
        custom_params: Optional[Dict] = None
    ) -> Dict:
        """
        Generate a single geometric anomaly
        
        Args:
            geometric_features: Pre-computed geometric features [H*W, 7]
            original_shading: Optional original shading image [H, W, 3]
            custom_params: Optional custom parameters for generation
            
        Returns:
            Dictionary containing anomaly generation results
        """
        # Determine if anomaly should be generated
        if np.random.random() > self.anomaly_probability:
            # Return original data without anomaly
            H_W_total = geometric_features.shape[0]
            H = W = int(np.sqrt(H_W_total))
            organized_pc = self.features_to_organized_pointcloud(geometric_features, (H, W))
            
            return {
                'has_anomaly': np.array([0.0]),
                'original_pointcloud': organized_pc,
                'deformed_pointcloud': organized_pc,
                'original_shading': original_shading,
                'modified_shading': original_shading,
                'patches_info': [],
                'deformation_infos': [],
                'shading_results': None,
                'anomaly_type': 'none'
            }
        
        # Parse custom parameters
        params = custom_params or {}
        num_patches = params.get('num_patches', np.random.randint(*self.num_patches_range))
        patch_coverage_ratio = params.get('patch_coverage_ratio', self.patch_coverage_ratio)
        
        # Generating geometric anomaly
        
        # Step 1: Convert features to organized point cloud
        H_W_total = geometric_features.shape[0]
        H = W = int(np.sqrt(H_W_total))
        organized_pc = self.features_to_organized_pointcloud(geometric_features, (H, W))
        depth_map = self.create_depth_map_from_pointcloud(organized_pc)
        
        # Step 2: Select patches for deformation
        # Step 1: Selecting patches for deformation
        patches_info = self.patch_selector.select_random_patches(
            organized_pc,
            depth_map=depth_map,
            num_patches=num_patches,
            patch_coverage_ratio=patch_coverage_ratio
        )
        
        if not patches_info:
            # Warning: No patches selected
            return {
                'has_anomaly': np.array([0.0]),
                'original_pointcloud': organized_pc,
                'deformed_pointcloud': organized_pc,
                'original_shading': original_shading,
                'modified_shading': original_shading,
                'patches_info': [],
                'deformation_infos': [],
                'shading_results': None,
                'anomaly_type': 'failed_patch_selection'
            }
        
        # Step 3: Apply geometric deformations
        # Step 2: Applying geometric deformations
        deformed_pc, deformation_infos = self.displacer.apply_multiple_deformations(
            organized_pc,
            patches_info,
            geometric_features
        )
        
        # Step 4: Generate 2D shading derivatives using incremental transformation
        # Step 3: Generating 2D shading derivatives using incremental method
        
        # Import the new incremental transformer
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
        from incremental_shading_transformer import IncrementalShadingTransformer
        
        # Create incremental transformer with configuration
        shading_config = self.config.get('shading_transformation', {})
        light_direction = shading_config.get('light_direction', [0.3, 0.3, 0.9])
        ambient_light = shading_config.get('ambient_light', 0.2)
        diffuse_strength = shading_config.get('diffuse_strength', 0.8)
        
        incremental_transformer = IncrementalShadingTransformer(
            light_direction=light_direction,
            ambient_light=ambient_light,
            diffuse_strength=diffuse_strength
        )
        
        # Extract patch indices from patches_info
        patch_indices = []
        for patch_info in patches_info:
            if 'patch_mask' in patch_info:
                mask = patch_info['patch_mask']
                # Convert boolean mask to list of (i, j) indices
                indices = np.where(mask)
                for i, j in zip(indices[0], indices[1]):
                    patch_indices.append((int(i), int(j)))
        
        print(f"  Extracted {len(patch_indices)} affected pixel indices")
        
        # Extract original normals from features if available
        original_normals = None
        if geometric_features.shape[1] >= 6:
            original_normals = geometric_features[:, 3:6].reshape(H, W, 3)
            print(f"  Using precomputed normals from geometric features")
        
        # Apply incremental shading transformation
        try:
            shading_transformation_result = incremental_transformer.apply_incremental_shading_transformation(
                clean_shading_map=original_shading,
                original_pcd=organized_pc,
                anomalous_pcd=deformed_pc,
                patch_indices=patch_indices,
                original_normals=original_normals
            )
            
            modified_shading = shading_transformation_result['anomalous_shading_map']
            shading_stats = shading_transformation_result['transformation_stats']
            
            # Incremental shading transformation successful
            
        except Exception as e:
            # Warning: Incremental shading transformation failed
            modified_shading = original_shading
            shading_stats = {
                'total_change': 0,
                'max_change': 0,
                'mean_change': 0,
                'total_changed_pixels': 0
            }
        
        # Compile results with incremental shading transformation
        result = {
            'has_anomaly': np.array([1.0]),
            'original_pointcloud': organized_pc,
            'deformed_pointcloud': deformed_pc,
            'original_shading': original_shading,
            'modified_shading': modified_shading,
            'patches_info': patches_info,
            'deformation_infos': deformation_infos,
            'shading_results': {
                'modified_shading': modified_shading,
                'shading_statistics': shading_stats,
                'transformation_method': 'incremental'
            },
            'anomaly_type': 'geometric_deformation',
            'generation_params': {
                'num_patches': len(patches_info),
                'patch_coverage_ratio': patch_coverage_ratio,
                'total_displaced_points': sum([info['num_displaced_points'] for info in deformation_infos]),
                'max_displacement': max([info['max_displacement'] for info in deformation_infos]) if deformation_infos else 0,
                'shading_change_magnitude': shading_stats['total_change'],
                'affected_pixels': len(patch_indices)
            }
        }
        
        # Geometric anomaly generation complete
        
        return result
    
    def generate_batch_geometric_anomalies(
        self,
        features_batch: List[np.ndarray],
        shading_batch: Optional[List[np.ndarray]] = None,
        custom_params_batch: Optional[List[Dict]] = None
    ) -> List[Dict]:
        """
        Generate geometric anomalies for a batch of data
        
        Args:
            features_batch: List of geometric features [H*W, 7]
            shading_batch: Optional list of original shading images
            custom_params_batch: Optional list of custom parameters for each sample
            
        Returns:
            List of anomaly generation results
        """
        batch_size = len(features_batch)
        print(f"Generating geometric anomalies for batch of {batch_size} samples...")
        
        # Prepare inputs
        if shading_batch is None:
            shading_batch = [None] * batch_size
        if custom_params_batch is None:
            custom_params_batch = [None] * batch_size
        
        results = []
        for i, (features, shading, params) in enumerate(zip(features_batch, shading_batch, custom_params_batch)):
            print(f"\nProcessing sample {i+1}/{batch_size}...")
            
            try:
                result = self.generate_single_geometric_anomaly(features, shading, params)
                result['sample_index'] = i
                results.append(result)
                
            except Exception as e:
                print(f"Error processing sample {i+1}: {e}")
                # Create fallback result
                H_W_total = features.shape[0]
                H = W = int(np.sqrt(H_W_total))
                organized_pc = self.features_to_organized_pointcloud(features, (H, W))
                
                fallback_result = {
                    'has_anomaly': np.array([0.0]),
                    'original_pointcloud': organized_pc,
                    'deformed_pointcloud': organized_pc,
                    'original_shading': shading,
                    'modified_shading': shading,
                    'patches_info': [],
                    'deformation_infos': [],
                    'shading_results': None,
                    'anomaly_type': 'generation_error',
                    'sample_index': i,
                    'error_message': str(e)
                }
                results.append(fallback_result)
        
        # Calculate batch statistics
        successful_anomalies = sum([1 for r in results if r['has_anomaly'][0] > 0])
        print(f"\nBatch generation complete:")
        print(f"  Total samples: {batch_size}")
        print(f"  Successful anomalies: {successful_anomalies}")
        print(f"  Success rate: {successful_anomalies/batch_size*100:.1f}%")
        
        return results
    
    def save_anomaly_results(
        self,
        results: Dict,
        save_dir: str,
        sample_name: str,
        save_pointclouds: bool = True,
        save_shading: bool = True,
        save_metadata: bool = True,
        save_pcd_files: bool = True
    ):
        """
        Save anomaly generation results to disk
        
        Args:
            results: Results from generate_single_geometric_anomaly
            save_dir: Save directory
            sample_name: Sample name for file naming
            save_pointclouds: Whether to save point cloud data as .npy files
            save_shading: Whether to save shading images
            save_metadata: Whether to save metadata
            save_pcd_files: Whether to save point clouds as .pcd files
        """
        save_path = Path(save_dir)
        save_path.mkdir(parents=True, exist_ok=True)
        
        # Save original point cloud as .npy (always save for reference)
        if save_pointclouds:
            original_pc_path = save_path / f"{sample_name}_original_pointcloud.npy"
            np.save(original_pc_path, results['original_pointcloud'])
            
            # Save deformed point cloud as .npy only if anomaly exists
            if results['has_anomaly'][0] > 0:
                deformed_pc_path = save_path / f"{sample_name}_deformed_pointcloud.npy"
                np.save(deformed_pc_path, results['deformed_pointcloud'])
        
        # Save point clouds as .pcd files
        if save_pcd_files:
            # Save original point cloud as .pcd
            original_pcd_path = save_path / f"{sample_name}_original.pcd"
            
            # Extract original normals from shading results if available
            original_normals = None
            if results.get('shading_results') and 'original_normals' in results['shading_results']:
                original_normals = results['shading_results']['original_normals']
            
            success_orig = save_organized_pointcloud_as_pcd(
                results['original_pointcloud'],
                str(original_pcd_path),
                normals=original_normals
            )
            
            # Save deformed point cloud as .pcd only if anomaly exists
            if results['has_anomaly'][0] > 0:
                deformed_pcd_path = save_path / f"{sample_name}_anomaly.pcd"
                
                # Extract deformed normals from shading results if available
                deformed_normals = None
                if results.get('shading_results') and 'deformed_normals' in results['shading_results']:
                    deformed_normals = results['shading_results']['deformed_normals']
                
                # Create colors to highlight the anomaly regions
                anomaly_colors = self._create_anomaly_colors(
                    results['original_pointcloud'],
                    results['patches_info']
                )
                
                success_def = save_organized_pointcloud_as_pcd(
                    results['deformed_pointcloud'],
                    str(deformed_pcd_path),
                    normals=deformed_normals,
                    colors=anomaly_colors
                )
                
                if success_orig and success_def:
                    print(f"Point cloud files (.pcd) saved successfully")
            else:
                if success_orig:
                    print(f"Original point cloud file (.pcd) saved successfully")
        
        # Save modified shading
        if save_shading and results['modified_shading'] is not None:
            shading_path = save_path / f"{sample_name}_modified_shading.png"
            shading_image = (results['modified_shading'] * 255).astype(np.uint8)
            if len(shading_image.shape) == 3:
                shading_image = cv2.cvtColor(shading_image, cv2.COLOR_RGB2BGR)
            cv2.imwrite(str(shading_path), shading_image)
        
        # Save metadata
        if save_metadata:
            metadata = {
                'has_anomaly': bool(results['has_anomaly'][0] > 0),
                'anomaly_type': results['anomaly_type'],
                'generation_timestamp': datetime.now().isoformat(),
                'patches_info': [
                    {
                        'seed_point': patch['seed_point'],
                        'patch_size': int(patch['patch_size'])
                    } for patch in results['patches_info']
                ],
                'generation_params': results.get('generation_params', {}),
                'shading_statistics': results['shading_results']['shading_statistics'] if results['shading_results'] else None
            }
            
            # Convert NumPy types to native Python types for JSON serialization
            metadata = convert_numpy_types(metadata)
            
            metadata_path = save_path / f"{sample_name}_metadata.json"
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2)
        
        print(f"Anomaly results saved to: {save_path}")
    
    def _create_anomaly_colors(
        self,
        organized_pointcloud: np.ndarray,
        patches_info: List[Dict]
    ) -> np.ndarray:
        """
        Create colors to highlight anomaly regions in point cloud
        
        Args:
            organized_pointcloud: Organized point cloud [H, W, 3]
            patches_info: List of patch information dictionaries
            
        Returns:
            Colors array [H, W, 3] in range [0, 1]
        """
        H, W, _ = organized_pointcloud.shape
        
        # Initialize with default color (light gray)
        colors = np.full((H, W, 3), 0.7, dtype=np.float32)
        
        # Define anomaly colors (bright colors to highlight anomalies)
        anomaly_colors = [
            [1.0, 0.0, 0.0],  # Red
            [0.0, 1.0, 0.0],  # Green
            [0.0, 0.0, 1.0],  # Blue
            [1.0, 1.0, 0.0],  # Yellow
            [1.0, 0.0, 1.0],  # Magenta
            [0.0, 1.0, 1.0],  # Cyan
        ]
        
        # Color each patch with a different color
        for i, patch_info in enumerate(patches_info):
            if 'patch_mask' in patch_info:
                patch_mask = patch_info['patch_mask']
                color_idx = i % len(anomaly_colors)
                color = anomaly_colors[color_idx]
                
                # Apply color to the masked region
                for c in range(3):
                    colors[:, :, c][patch_mask > 0] = color[c]
        
        return colors
    
    def visualize_complete_pipeline(
        self,
        results: Dict,
        save_path: Optional[str] = None
    ):
        """
        Visualize complete geometric anomaly generation pipeline
        
        Args:
            results: Results from generate_single_geometric_anomaly
            save_path: Optional save path for visualization
        """
        import matplotlib.pyplot as plt
        
        if not results['has_anomaly'][0]:
            print("No anomaly to visualize")
            return
        
        fig, axes = plt.subplots(3, 3, figsize=(18, 18))
        
        # Row 1: Original data
        # Original depth
        orig_depth = results['original_pointcloud'][:, :, 2]
        axes[0, 0].imshow(orig_depth, cmap='viridis')
        axes[0, 0].set_title('Original Depth Map')
        axes[0, 0].axis('off')
        
        # Original shading
        if results['original_shading'] is not None:
            axes[0, 1].imshow(results['original_shading'])
            axes[0, 1].set_title('Original Shading')
        else:
            axes[0, 1].text(0.5, 0.5, 'No Original\nShading', ha='center', va='center', 
                           transform=axes[0, 1].transAxes, fontsize=14)
            axes[0, 1].set_title('Original Shading (N/A)')
        axes[0, 1].axis('off')
        
        # Selected patches
        combined_mask = np.zeros_like(orig_depth)
        for i, patch in enumerate(results['patches_info']):
            combined_mask += (i + 1) * patch['patch_mask']
        axes[0, 2].imshow(orig_depth, cmap='viridis', alpha=0.7)
        axes[0, 2].imshow(combined_mask, cmap='tab10', alpha=0.5)
        axes[0, 2].set_title(f'Selected Patches ({len(results["patches_info"])})')
        axes[0, 2].axis('off')
        
        # Row 2: Deformation results
        # Deformed depth
        def_depth = results['deformed_pointcloud'][:, :, 2]
        axes[1, 0].imshow(def_depth, cmap='viridis')
        axes[1, 0].set_title('Deformed Depth Map')
        axes[1, 0].axis('off')
        
        # Displacement magnitude
        displacement = results['deformed_pointcloud'] - results['original_pointcloud']
        displacement_mag = np.linalg.norm(displacement, axis=2)
        im1 = axes[1, 1].imshow(displacement_mag, cmap='hot')
        axes[1, 1].set_title('Displacement Magnitude')
        axes[1, 1].axis('off')
        plt.colorbar(im1, ax=axes[1, 1], fraction=0.046, pad=0.04)
        
        # Deformation statistics
        if results['deformation_infos']:
            displacements = [info['mean_displacement'] for info in results['deformation_infos']]
            x = range(len(displacements))
            axes[1, 2].bar(x, displacements, alpha=0.7)
            axes[1, 2].set_title('Mean Displacement per Patch')
            axes[1, 2].set_xlabel('Patch Index')
            axes[1, 2].set_ylabel('Displacement')
        else:
            axes[1, 2].text(0.5, 0.5, 'No Deformation\nStatistics', ha='center', va='center',
                           transform=axes[1, 2].transAxes, fontsize=14)
            axes[1, 2].set_title('Deformation Statistics')
        
        # Row 3: Shading results
        # Modified shading
        if results['modified_shading'] is not None:
            axes[2, 0].imshow(results['modified_shading'])
            axes[2, 0].set_title('Modified Shading')
        else:
            axes[2, 0].text(0.5, 0.5, 'No Modified\nShading', ha='center', va='center',
                           transform=axes[2, 0].transAxes, fontsize=14)
            axes[2, 0].set_title('Modified Shading (N/A)')
        axes[2, 0].axis('off')
        
        # Intensity changes
        if results['shading_results'] is not None:
            intensity_changes = results['shading_results']['intensity_changes']
            im2 = axes[2, 1].imshow(intensity_changes, cmap='RdBu_r', vmin=-0.5, vmax=0.5)
            axes[2, 1].set_title('Lighting Intensity Changes')
            axes[2, 1].axis('off')
            plt.colorbar(im2, ax=axes[2, 1], fraction=0.046, pad=0.04)
        else:
            axes[2, 1].text(0.5, 0.5, 'No Intensity\nChanges', ha='center', va='center',
                           transform=axes[2, 1].transAxes, fontsize=14)
            axes[2, 1].set_title('Intensity Changes (N/A)')
        
        # Summary statistics
        stats_text = f"Anomaly Type: {results['anomaly_type']}\n"
        if 'generation_params' in results:
            params = results['generation_params']
            stats_text += f"Patches: {params['num_patches']}\n"
            stats_text += f"Displaced Points: {params['total_displaced_points']}\n"
            stats_text += f"Max Displacement: {params['max_displacement']:.4f}\n"
            stats_text += f"Shading Change: {params['shading_change_magnitude']:.4f}"
        
        axes[2, 2].text(0.1, 0.5, stats_text, transform=axes[2, 2].transAxes,
                       fontsize=11, verticalalignment='center', fontfamily='monospace')
        axes[2, 2].set_title('Generation Summary')
        axes[2, 2].axis('off')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Complete pipeline visualization saved to: {save_path}")
        
        plt.show()


def test_geometric_anomaly_generator():
    """
    Test the complete GeometricAnomalyGenerator
    """
    # Create test geometric features (7D: x,y,z,nx,ny,nz,curvature)
    H, W = 224, 224
    
    # Create organized point cloud
    center_x, center_y = W // 2, H // 2
    y_coords, x_coords = np.ogrid[:H, :W]
    distance = np.sqrt((x_coords - center_x)**2 + (y_coords - center_y)**2)
    max_distance = min(W, H) * 0.4
    
    # Create test features
    features = np.zeros((H*W, 7))
    
    # XYZ coordinates
    for i in range(H):
        for j in range(W):
            idx = i * W + j
            if distance[i, j] <= max_distance:
                features[idx, 0] = (j - center_x) / W * 2  # X
                features[idx, 1] = (i - center_y) / H * 2  # Y
                features[idx, 2] = 0.5 + 0.3 * np.cos(distance[i, j] / max_distance * np.pi)  # Z
                
                # Simple normals (pointing up with slight variation)
                features[idx, 3] = 0.1 * (np.random.random() - 0.5)  # NX
                features[idx, 4] = 0.1 * (np.random.random() - 0.5)  # NY
                features[idx, 5] = 1.0  # NZ
                
                # Curvature
                features[idx, 6] = np.random.random() * 0.5  # Curvature
    
    # Initialize generator
    config = {
        'patch_selection': {
            'min_patch_size': 100,
            'max_patch_size': 400,
            'expansion_radius': 0.03
        },
        'displacement': {
            'displacement_strength_range': [0.02, 0.08],
            'center_weight': 1.0,
            'edge_weight': 0.2
        },
        'shading_transformation': {
            'light_direction': [0.3, 0.3, 0.9],
            'ambient_light': 0.2,
            'diffuse_strength': 0.8,
            'gaussian_blur_sigma': 1.5
        },
        'num_patches_range': [1, 2],
        'patch_coverage_ratio': 0.2,
        'anomaly_probability': 1.0
    }
    
    generator = GeometricAnomalyGenerator(config)
    
    # Generate anomaly
    results = generator.generate_single_geometric_anomaly(features)
    
    print(f"Test completed! Generated anomaly: {results['has_anomaly'][0] > 0}")
    
    # Visualize results
    generator.visualize_complete_pipeline(results)
    
    # Save results
    generator.save_anomaly_results(results, "./test_geometric_anomaly_output", "test_sample")
    
    return results


if __name__ == "__main__":
    test_geometric_anomaly_generator()