"""
材质异常生成配置文件
基于EasyNet方法的Albedo纹理材质异常生成参数配置

Author: IA-CRF Project
Date: 2025-08-26
"""

# 基础配置
base_config = {
    # 图像尺寸设置
    "image_size": [224, 224],  # [height, width]
    
    # 支持的图像格式
    "supported_formats": [".jpg", ".jpeg", ".png", ".bmp"],
    
    # 随机种子（None为随机）
    "random_seed": None,
    
    # 异常生成概率
    "anomaly_probability": 0.5,
}

# DTD数据集配置
dtd_config = {
    # DTD数据集路径（需要用户设置）
    "dtd_dataset_path": "/path/to/dtd/dataset",
    
    # DTD纹理类别（可选，为空时自动检测）
    "dtd_categories": [
        "banded", "blotchy", "braided", "bubbly", "bumpy",
        "chequered", "cobwebbed", "cracked", "crosshatched", "crystalline",
        "dotted", "fibrous", "flecked", "freckled", "frilly",
        "gauzy", "grid", "grooved", "honeycombed", "interlaced",
        "knitted", "lacelike", "lined", "marbled", "matted",
        "meshed", "paisley", "perforated", "pitted", "pleated",
        "polka-dotted", "porous", "potholed", "scaly", "smeared",
        "spiralled", "sprinkled", "stained", "stratified", "striped",
        "studded", "swirly", "veined", "waffled", "woven",
        "wrinkled", "zigzagged"
    ],
    
    # 特定类别权重（可选）
    "category_weights": None,  # 如：{"cracked": 2.0, "stained": 1.5}
}

# Perlin噪声配置
perlin_config = {
    # Perlin尺度参数
    "min_perlin_scale": 0,      # 最小尺度指数
    "perlin_scale": 6,          # 最大尺度指数
    
    # 二值化阈值
    "threshold": 0.5,
    
    # 深度有效性阈值
    "depth_threshold": 0.001,
    
    # 旋转角度范围
    "rotation_range": [-90, 90],  # [min_angle, max_angle]
    
    # 分形噪声参数（可选，暂未实现）
    # "use_fractal": False,
    # "fractal_octaves": 3,
    # "fractal_persistence": 0.5,
}

# 纹理增强配置
texture_augmentation_config = {
    # 是否启用纹理增强
    "enable_augmentation": True,
    
    # 同时应用的增强数量
    "num_augmentations": 3,
    
    # 增强参数
    "augmentation_params": {
        "gamma_contrast": {
            "gamma_range": [0.5, 2.0],
            "per_channel": True
        },
        "brightness": {
            "mul_range": [0.8, 1.2],
            "add_range": [-30, 30]
        },
        "sharpness": {
            "enable": True
        },
        "hue_saturation": {
            "hue_range": [-50, 50],
            "saturation_range": [-50, 50],
            "per_channel": True
        },
        "solarize": {
            "probability": 0.5,
            "threshold_range": [32, 128]
        },
        "posterize": {
            "enable": True
        },
        "invert": {
            "enable": True
        },
        "autocontrast": {
            "enable": True
        },
        "equalize": {
            "enable": True
        },
        "rotation": {
            "angle_range": [-45, 45]
        }
    },
    
    # 增强器权重（可选）
    "augmenter_weights": None,  # 如：{"gamma_contrast": 2.0, "rotation": 0.5}
}

# Beta混合配置
beta_mixing_config = {
    # Beta参数范围
    "beta_range": [0.0, 0.8],
    
    # Beta分布参数（可选，用于非均匀分布）
    "use_beta_distribution": False,
    "beta_alpha": 2.0,
    "beta_beta": 5.0,
}

# 异常区域配置
anomaly_region_config = {
    # 最小异常区域面积占比
    "min_anomaly_ratio": 0.01,   # 1%
    
    # 最大异常区域面积占比
    "max_anomaly_ratio": 0.3,    # 30%
    
    # 形态学操作（用于优化异常形状）
    "morphology": {
        "enable": False,
        "operation": "opening",   # "opening", "closing", "gradient"
        "kernel_size": 3,
        "iterations": 1
    },
    
    # 高斯模糊（用于软化边缘）
    "gaussian_blur": {
        "enable": False,
        "kernel_size": 5,
        "sigma": 1.0
    }
}

# 输出配置
output_config = {
    # 输出格式
    "output_format": "png",
    
    # 保存组件
    "save_components": {
        "augmented_albedo": True,
        "anomaly_mask": True,
        "original_albedo": False,
        "texture_source": False,
        "perlin_noise": False,
        "metadata": True
    },
    
    # 图像质量
    "image_quality": 95,  # 仅对JPEG格式有效
    
    # 压缩级别
    "compression_level": 6,  # 仅对PNG格式有效
}

# 验证配置
validation_config = {
    # 是否启用验证
    "enable_validation": True,
    
    # 验证阈值
    "validation_thresholds": {
        "min_texture_variance": 0.01,  # 纹理变化最小阈值
        "max_depth_coverage": 0.95,    # 深度覆盖最大阈值
        "min_perlin_coverage": 0.001   # Perlin噪声最小覆盖
    },
    
    # 重试次数
    "max_retries": 3,
}

# 性能配置
performance_config = {
    # 批处理大小
    "batch_size": 8,
    
    # 并行处理
    "num_workers": 4,
    
    # 内存优化
    "memory_optimization": {
        "enable": True,
        "clear_cache_interval": 100,  # 每处理100个样本清理缓存
    },
    
    # 预加载设置
    "preload": {
        "texture_images": False,  # 是否预加载纹理图像
        "max_preload_size": 1000  # 最大预加载图像数量
    }
}

# 调试配置
debug_config = {
    # 调试模式
    "debug_mode": False,
    
    # 详细输出
    "verbose": True,
    
    # 保存中间结果
    "save_intermediate": False,
    
    # 统计信息
    "collect_statistics": True,
    
    # 可视化
    "visualization": {
        "enable": False,
        "save_visualization": False,
        "visualization_interval": 10
    }
}

# MVTec 3D-AD特定配置
mvtec3d_specific_config = {
    # 类别特定参数
    "category_specific_params": {
        "bagel": {
            "beta_range": [0.2, 0.8],
            "perlin_scale": 5,
            "anomaly_probability": 0.6
        },
        "cable_gland": {
            "beta_range": [0.1, 0.7],
            "perlin_scale": 6,
            "anomaly_probability": 0.5
        },
        "carrot": {
            "beta_range": [0.3, 0.8],
            "perlin_scale": 4,
            "anomaly_probability": 0.7
        },
        "cookie": {
            "beta_range": [0.2, 0.7],
            "perlin_scale": 5,
            "anomaly_probability": 0.6
        },
        "dowel": {
            "beta_range": [0.1, 0.6],
            "perlin_scale": 6,
            "anomaly_probability": 0.5
        },
        "foam": {
            "beta_range": [0.4, 0.8],
            "perlin_scale": 3,
            "anomaly_probability": 0.8
        },
        "peach": {
            "beta_range": [0.3, 0.7],
            "perlin_scale": 4,
            "anomaly_probability": 0.7
        },
        "potato": {
            "beta_range": [0.2, 0.8],
            "perlin_scale": 5,
            "anomaly_probability": 0.6
        },
        "rope": {
            "beta_range": [0.1, 0.6],
            "perlin_scale": 6,
            "anomaly_probability": 0.5
        },
        "tire": {
            "beta_range": [0.2, 0.7],
            "perlin_scale": 5,
            "anomaly_probability": 0.6
        }
    },
    
    # 推荐的DTD类别映射
    "recommended_texture_mapping": {
        "bagel": ["bumpy", "porous", "cracked", "stained"],
        "cable_gland": ["lined", "grooved", "striped", "ribbed"],
        "carrot": ["bumpy", "freckled", "pitted", "blotchy"],
        "cookie": ["cracked", "bumpy", "freckled", "dotted"],
        "dowel": ["lined", "grooved", "striped", "scratched"],
        "foam": ["porous", "bubbly", "honeycombe", "spongy"],
        "peach": ["bumpy", "freckled", "blotchy", "stained"],
        "potato": ["bumpy", "pitted", "blotchy", "spotted"],
        "rope": ["braided", "fibrous", "woven", "twisted"],
        "tire": ["grooved", "ribbed", "honeycombe", "textured"]
    }
}

# 完整配置字典
MATERIAL_ANOMALY_CONFIG = {
    "base": base_config,
    "dtd": dtd_config,
    "perlin": perlin_config,
    "texture_augmentation": texture_augmentation_config,
    "beta_mixing": beta_mixing_config,
    "anomaly_region": anomaly_region_config,
    "output": output_config,
    "validation": validation_config,
    "performance": performance_config,
    "debug": debug_config,
    "mvtec3d_specific": mvtec3d_specific_config
}

# 预设配置
PRESET_CONFIGS = {
    "easynet_default": {
        # EasyNet默认参数
        "base": {
            "image_size": [224, 224],
            "anomaly_probability": 0.5
        },
        "perlin": {
            "min_perlin_scale": 0,
            "perlin_scale": 6,
            "threshold": 0.5,
            "depth_threshold": 0.001,
            "rotation_range": [-90, 90]
        },
        "beta_mixing": {
            "beta_range": [0.0, 0.8]
        },
        "texture_augmentation": {
            "enable_augmentation": True,
            "num_augmentations": 3
        }
    },
    
    "high_quality": {
        # 高质量异常生成
        "perlin": {
            "use_fractal": True,
            "fractal_octaves": 4,
            "fractal_persistence": 0.6
        },
        "anomaly_region": {
            "gaussian_blur": {
                "enable": True,
                "kernel_size": 3,
                "sigma": 0.8
            }
        },
        "texture_augmentation": {
            "num_augmentations": 5
        }
    },
    
    "fast_generation": {
        # 快速生成模式
        "perlin": {
            "use_fractal": False
        },
        "texture_augmentation": {
            "num_augmentations": 1
        },
        "performance": {
            "batch_size": 16,
            "preload": {
                "texture_images": True,
                "max_preload_size": 500
            }
        }
    },
    
    "debug_mode": {
        # 调试模式
        "debug": {
            "debug_mode": True,
            "verbose": True,
            "save_intermediate": True,
            "visualization": {
                "enable": True,
                "save_visualization": True
            }
        }
    }
}


def get_config(preset: str = "easynet_default") -> dict:
    """
    获取配置
    
    Args:
        preset: 预设配置名称
        
    Returns:
        配置字典
    """
    import copy
    
    # 基础配置
    config = copy.deepcopy(MATERIAL_ANOMALY_CONFIG)
    
    # 应用预设配置
    if preset in PRESET_CONFIGS:
        preset_config = PRESET_CONFIGS[preset]
        for section, params in preset_config.items():
            if section in config:
                config[section].update(params)
    
    return config


def validate_config(config: dict) -> bool:
    """
    验证配置有效性
    
    Args:
        config: 配置字典
        
    Returns:
        是否有效
    """
    try:
        # 检查必需的配置项
        required_sections = ["base", "perlin", "beta_mixing"]
        for section in required_sections:
            if section not in config:
                print(f"Missing required section: {section}")
                return False
        
        # 检查图像尺寸
        image_size = config["base"]["image_size"]
        if len(image_size) != 2 or any(s <= 0 for s in image_size):
            print("Invalid image_size")
            return False
        
        # 检查Beta范围
        beta_range = config["beta_mixing"]["beta_range"]
        if len(beta_range) != 2 or beta_range[0] >= beta_range[1]:
            print("Invalid beta_range")
            return False
        
        # 检查Perlin参数
        perlin_cfg = config["perlin"]
        if perlin_cfg["min_perlin_scale"] >= perlin_cfg["perlin_scale"]:
            print("Invalid perlin scale range")
            return False
        
        return True
        
    except Exception as e:
        print(f"Config validation error: {e}")
        return False


if __name__ == "__main__":
    # 测试配置
    print("Testing material anomaly generation config...")
    
    # 获取默认配置
    config = get_config("easynet_default")
    print(f"Config sections: {list(config.keys())}")
    
    # 验证配置
    is_valid = validate_config(config)
    print(f"Config validation: {'PASSED' if is_valid else 'FAILED'}")
    
    # 测试其他预设
    for preset_name in PRESET_CONFIGS.keys():
        preset_config = get_config(preset_name)
        is_valid = validate_config(preset_config)
        print(f"Preset '{preset_name}' validation: {'PASSED' if is_valid else 'FAILED'}")