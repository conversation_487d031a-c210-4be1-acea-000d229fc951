"""
Geometric Anomaly Generation Configuration
Configuration parameters for geometric anomaly generation workflow

Author: IA-CRF Project
Date: 2025-08-27
"""

# Base configuration
base_config = {
    # Image/Point cloud size settings
    "image_size": [224, 224],  # [height, width]
    
    # Random seed (None for random)
    "random_seed": None,
    
    # Anomaly generation probability
    "anomaly_probability": 1.0,
    
    # Number of patches per sample
    "num_patches_range": [1, 3],  # [min, max] patches
    
    # Patch coverage ratio (relative to foreground area)
    "patch_coverage_ratio": 0.15,
}

# Patch selection configuration
patch_selection_config = {
    # Patch size constraints
    "min_patch_size": 50,        # Minimum patch size (number of points)
    "max_patch_size": 500,       # Maximum patch size (number of points)
    
    # KNN expansion parameters
    "expansion_radius": 0.02,    # Expansion radius for KNN search
    "max_iterations": 20,        # Maximum expansion iterations
    
    # Boundary smoothing
    "smoothing_sigma": 2.0,      # Gaussian smoothing sigma for patch boundaries
    
    # Depth threshold
    "depth_threshold": 0.001,    # Depth validity threshold
}

# Displacement configuration
displacement_config = {
    # Displacement strength - 降低位移强度使异常更平缓
    "displacement_strength_range": [0.001, 0.008],  # [min, max] displacement strength - 大幅降低
    
    # Weight distribution (center strong, edge weak) - 降低中心权重差异
    "center_weight": 0.6,        # Displacement weight at patch center - 从1.0降低到0.6
    "edge_weight": 0.3,          # Displacement weight at patch edges - 从0.1提高到0.3
    
    # Normal estimation
    "normal_estimation_radius": 0.02,  # Radius for normal estimation
    
    # Direction randomization
    "displacement_direction_noise": 0.1,  # Noise level for displacement direction
    
    # Minimum points for processing
    "min_displacement_points": 10,
}

# Shading transformation configuration
shading_transformation_config = {
    # Lighting parameters
    "light_direction": [0.0, 0.0, 1.0],  # Light direction vector [x, y, z]
    "ambient_light": 0.3,                # Ambient light intensity [0, 1]
    "diffuse_strength": 0.7,             # Diffuse lighting strength [0, 1]
    
    # Normal estimation
    "normal_estimation_radius": 0.02,    # Radius for normal estimation
    
    # Smoothing parameters
    "gaussian_blur_sigma": 1.0,          # Gaussian blur sigma for smoothing
    
    # Shading change amplification
    "shading_intensity_multiplier": 2.0, # Multiplier for shading changes in deformed regions
}

# Complete configuration dictionary
GEOMETRIC_ANOMALY_CONFIG = {
    "base": base_config,
    "patch_selection": patch_selection_config,
    "displacement": displacement_config,
    "shading_transformation": shading_transformation_config
}

# Preset configurations for different scenarios
PRESET_CONFIGS = {
    "subtle_deformation": {
        # Subtle geometric deformations - 进一步优化微小变形参数
        "base": {
            "num_patches_range": [1, 2],
            "patch_coverage_ratio": 0.08  # 进一步降低覆盖率
        },
        "displacement": {
            "displacement_strength_range": [0.0005, 0.003],  # 极小的位移强度
            "displacement_direction_noise": 0.02,            # 极小的方向噪声
            "center_weight": 0.4,                           # 更平缓的中心权重
            "edge_weight": 0.35                             # 更平缓的边缘权重
        },
        "shading_transformation": {
            "shading_intensity_multiplier": 1.2,  # 更小的阴影变化
            "gaussian_blur_sigma": 1.8            # 更多平滑
        }
    },
    
    "moderate_deformation": {
        # Moderate geometric deformations (default) - 调整为更平缓的参数
        "base": {
            "num_patches_range": [1, 2],  # 减少补丁数量
            "patch_coverage_ratio": 0.12   # 降低覆盖率
        },
        "displacement": {
            "displacement_strength_range": [0.001, 0.006],  # 大幅降低位移强度
            "displacement_direction_noise": 0.05,           # 降低方向噪声
            "center_weight": 0.5,                          # 降低中心权重
            "edge_weight": 0.4                             # 提高边缘权重
        },
        "shading_transformation": {
            "shading_intensity_multiplier": 1.5,  # 降低阴影变化放大倍数
            "gaussian_blur_sigma": 1.2            # 增加平滑
        }
    },
    
    "strong_deformation": {
        # Strong geometric deformations
        "base": {
            "num_patches_range": [2, 4],
            "patch_coverage_ratio": 0.25
        },
        "displacement": {
            "displacement_strength_range": [0.01, 0.05],
            "displacement_direction_noise": 0.2
        },
        "shading_transformation": {
            "shading_intensity_multiplier": 3.0,
            "gaussian_blur_sigma": 0.8
        }
    },
    
    "crack_like_defect": {
        # Crack-like linear defects
        "patch_selection": {
            "min_patch_size": 20,
            "max_patch_size": 200,
            "expansion_radius": 0.015,
            "smoothing_sigma": 1.0
        },
        "displacement": {
            "displacement_strength_range": [0.008, 0.03],
            "center_weight": 1.2,
            "edge_weight": 0.3
        }
    },
    
    "dent_like_defect": {
        # Dent-like defects
        "patch_selection": {
            "min_patch_size": 100,
            "max_patch_size": 600,
            "expansion_radius": 0.025,
            "smoothing_sigma": 2.5
        },
        "displacement": {
            "displacement_strength_range": [0.01, 0.04],
            "center_weight": 1.5,
            "edge_weight": 0.1
        },
        "shading_transformation": {
            "shading_intensity_multiplier": 2.5
        }
    },
    
    "surface_roughness": {
        # Surface roughness simulation
        "base": {
            "num_patches_range": [3, 6],
            "patch_coverage_ratio": 0.3
        },
        "patch_selection": {
            "min_patch_size": 30,
            "max_patch_size": 150,
            "expansion_radius": 0.02
        },
        "displacement": {
            "displacement_strength_range": [0.003, 0.015],
            "displacement_direction_noise": 0.3
        }
    }
}

# MVTec 3D-AD category-specific configurations
mvtec3d_specific_config = {
    "category_specific_params": {
        "bagel": {
            "preset": "dent_like_defect",
            "patch_coverage_ratio": 0.12,
            "displacement_strength_range": [0.006, 0.025]
        },
        "cable_gland": {
            "preset": "crack_like_defect",
            "patch_coverage_ratio": 0.08,
            "displacement_strength_range": [0.004, 0.018]
        },
        "carrot": {
            "preset": "dent_like_defect",
            "patch_coverage_ratio": 0.15,
            "displacement_strength_range": [0.008, 0.03]
        },
        "cookie": {
            "preset": "crack_like_defect",
            "patch_coverage_ratio": 0.1,
            "displacement_strength_range": [0.005, 0.02]
        },
        "dowel": {
            "preset": "crack_like_defect",
            "patch_coverage_ratio": 0.08,
            "displacement_strength_range": [0.004, 0.015]
        },
        "foam": {
            "preset": "surface_roughness",
            "patch_coverage_ratio": 0.2,
            "displacement_strength_range": [0.003, 0.012]
        },
        "peach": {
            "preset": "dent_like_defect",
            "patch_coverage_ratio": 0.15,
            "displacement_strength_range": [0.007, 0.025]
        },
        "potato": {
            "preset": "dent_like_defect",
            "patch_coverage_ratio": 0.18,
            "displacement_strength_range": [0.008, 0.03]
        },
        "rope": {
            "preset": "surface_roughness",
            "patch_coverage_ratio": 0.12,
            "displacement_strength_range": [0.005, 0.02]
        },
        "tire": {
            "preset": "crack_like_defect",
            "patch_coverage_ratio": 0.1,
            "displacement_strength_range": [0.006, 0.025]
        }
    }
}

# Output configuration
output_config = {
    # Output format
    "output_format": "png",
    
    # Save components
    "save_components": {
        "deformed_pointcloud": True,
        "modified_shading": True,
        "original_pointcloud": False,
        "displacement_map": True,
        "intensity_changes": True,
        "metadata": True
    },
    
    # Image quality
    "image_quality": 95,  # Only for JPEG format
    
    # Compression level
    "compression_level": 6,  # Only for PNG format
}

# Validation configuration
validation_config = {
    # Enable validation
    "enable_validation": True,
    
    # Validation thresholds
    "validation_thresholds": {
        "min_displacement_magnitude": 0.001,  # Minimum displacement to be considered valid
        "max_displacement_magnitude": 0.1,    # Maximum allowed displacement
        "min_shading_change": 0.01,           # Minimum shading change magnitude
        "min_patch_size_ratio": 0.005         # Minimum patch size relative to image
    },
    
    # Retry attempts
    "max_retries": 3,
}

# Performance configuration
performance_config = {
    # Batch processing
    "batch_size": 8,
    
    # Parallel processing
    "num_workers": 4,
    
    # Memory optimization
    "memory_optimization": {
        "use_efficient_algorithms": True,
        "clear_intermediate_results": True
    }
}

# Debug configuration
debug_config = {
    # Debug mode
    "debug_mode": False,
    
    # Verbose output
    "verbose": True,
    
    # Save intermediate results
    "save_intermediate": False,
    
    # Visualization
    "visualization": {
        "enable": True,
        "save_visualization": False,
        "show_plots": False
    }
}

# Complete configuration dictionary
COMPLETE_CONFIG = {
    "base": base_config,
    "patch_selection": patch_selection_config,
    "displacement": displacement_config,
    "shading_transformation": shading_transformation_config,
    "output": output_config,
    "validation": validation_config,
    "performance": performance_config,
    "debug": debug_config,
    "mvtec3d_specific": mvtec3d_specific_config
}


def get_config(preset: str = "moderate_deformation") -> dict:
    """
    Get configuration for geometric anomaly generation
    
    Args:
        preset: Preset configuration name
        
    Returns:
        Configuration dictionary
    """
    import copy
    
    # Start with base configuration
    config = copy.deepcopy(COMPLETE_CONFIG)
    
    # Apply preset configuration
    if preset in PRESET_CONFIGS:
        preset_config = PRESET_CONFIGS[preset]
        for section, params in preset_config.items():
            if section in config:
                config[section].update(params)
    
    return config


def get_category_config(category: str, base_preset: str = "moderate_deformation") -> dict:
    """
    Get category-specific configuration
    
    Args:
        category: MVTec 3D-AD category name
        base_preset: Base preset to start with
        
    Returns:
        Category-specific configuration dictionary
    """
    import copy
    
    # Get base configuration
    config = get_config(base_preset)
    
    # Apply category-specific settings
    if category in mvtec3d_specific_config["category_specific_params"]:
        category_params = mvtec3d_specific_config["category_specific_params"][category]
        
        # Apply category preset if specified
        if "preset" in category_params:
            category_preset = category_params["preset"]
            if category_preset in PRESET_CONFIGS:
                preset_config = PRESET_CONFIGS[category_preset]
                for section, params in preset_config.items():
                    if section in config:
                        config[section].update(params)
        
        # Apply specific category parameters
        for key, value in category_params.items():
            if key != "preset":
                # Find appropriate section for the parameter
                if key in config["base"]:
                    config["base"][key] = value
                elif key in config["displacement"]:
                    config["displacement"][key] = value
                elif key in config["patch_selection"]:
                    config["patch_selection"][key] = value
                elif key in config["shading_transformation"]:
                    config["shading_transformation"][key] = value
    
    return config


def validate_config(config: dict) -> bool:
    """
    Validate configuration parameters
    
    Args:
        config: Configuration dictionary
        
    Returns:
        True if configuration is valid
    """
    try:
        # Check required sections
        required_sections = ["base", "patch_selection", "displacement", "shading_transformation"]
        for section in required_sections:
            if section not in config:
                print(f"Missing required section: {section}")
                return False
        
        # Check image size
        image_size = config["base"]["image_size"]
        if len(image_size) != 2 or any(s <= 0 for s in image_size):
            print("Invalid image_size")
            return False
        
        # Check displacement strength range
        disp_range = config["displacement"]["displacement_strength_range"]
        if len(disp_range) != 2 or disp_range[0] >= disp_range[1] or disp_range[0] < 0:
            print("Invalid displacement_strength_range")
            return False
        
        # Check patch size range
        patch_config = config["patch_selection"]
        if patch_config["min_patch_size"] >= patch_config["max_patch_size"]:
            print("Invalid patch size range")
            return False
        
        # Check probability
        prob = config["base"]["anomaly_probability"]
        if prob < 0 or prob > 1:
            print("Invalid anomaly_probability")
            return False
        
        return True
        
    except Exception as e:
        print(f"Config validation error: {e}")
        return False


if __name__ == "__main__":
    # Test configurations
    print("Testing geometric anomaly generation configurations...")
    
    # Test default configuration
    config = get_config("moderate_deformation")
    print(f"Default config sections: {list(config.keys())}")
    
    # Validate configuration
    is_valid = validate_config(config)
    print(f"Default config validation: {'PASSED' if is_valid else 'FAILED'}")
    
    # Test category-specific configurations
    categories = ["bagel", "cable_gland", "carrot", "cookie", "dowel"]
    for category in categories:
        cat_config = get_category_config(category)
        is_valid = validate_config(cat_config)
        print(f"Category '{category}' config validation: {'PASSED' if is_valid else 'FAILED'}")
    
    # Test all presets
    for preset_name in PRESET_CONFIGS.keys():
        preset_config = get_config(preset_name)
        is_valid = validate_config(preset_config)
        print(f"Preset '{preset_name}' validation: {'PASSED' if is_valid else 'FAILED'}")