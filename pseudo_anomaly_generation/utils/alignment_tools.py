"""
2D-3D对齐映射工具类
用于处理RGB图像坐标与3D点云坐标之间的映射和对齐

Author: IA-CRF Project
Date: 2025-08-26
"""

import numpy as np
import cv2
from typing import Tuple, Optional, Dict, List, Union
from pathlib import Path
import tifffile as tiff


class Camera3DProjection:
    """
    相机3D投影工具
    处理3D点云到2D图像的投影变换
    """
    
    def __init__(
        self,
        intrinsic_matrix: Optional[np.ndarray] = None,
        image_size: Tuple[int, int] = (224, 224)
    ):
        """
        初始化相机投影工具
        
        Args:
            intrinsic_matrix: 相机内参矩阵 [3x3]
            image_size: 图像尺寸 (height, width)
        """
        self.image_size = image_size
        
        if intrinsic_matrix is not None:
            self.intrinsic_matrix = intrinsic_matrix
        else:
            # 默认内参矩阵（假设FOV=60度）
            self.intrinsic_matrix = self._create_default_intrinsic()
    
    def _create_default_intrinsic(self) -> np.ndarray:
        """
        创建默认相机内参矩阵
        
        Returns:
            3x3内参矩阵
        """
        h, w = self.image_size
        
        # 假设FOV=60度
        focal_length = w / (2 * np.tan(np.radians(30)))
        
        intrinsic = np.array([
            [focal_length, 0, w / 2],
            [0, focal_length, h / 2],
            [0, 0, 1]
        ], dtype=np.float32)
        
        return intrinsic
    
    def project_3d_to_2d(
        self,
        points_3d: np.ndarray,
        filter_valid: bool = True
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        将3D点投影到2D图像平面
        
        Args:
            points_3d: 3D点坐标 [N, 3] 或 [H, W, 3]
            filter_valid: 是否过滤无效点
            
        Returns:
            (points_2d, valid_mask): 2D点坐标和有效性掩码
        """
        original_shape = points_3d.shape
        
        # 将输入reshape为[N, 3]
        if len(original_shape) == 3:
            points_3d = points_3d.reshape(-1, 3)
        
        # 过滤深度为0的点
        if filter_valid:
            valid_mask = points_3d[:, 2] > 0
            valid_points = points_3d[valid_mask]
        else:
            valid_mask = np.ones(len(points_3d), dtype=bool)
            valid_points = points_3d
        
        if len(valid_points) == 0:
            # 没有有效点
            if len(original_shape) == 3:
                points_2d = np.zeros((original_shape[0], original_shape[1], 2))
                valid_mask = np.zeros((original_shape[0], original_shape[1]), dtype=bool)
            else:
                points_2d = np.zeros((len(points_3d), 2))
                valid_mask = np.zeros(len(points_3d), dtype=bool)
            return points_2d, valid_mask
        
        # 投影到图像平面
        points_2d_homogeneous = self.intrinsic_matrix @ valid_points.T
        points_2d_normalized = points_2d_homogeneous[:2] / points_2d_homogeneous[2]
        points_2d_valid = points_2d_normalized.T
        
        # 创建完整的2D点数组
        points_2d_full = np.zeros((len(points_3d), 2))
        if filter_valid:
            points_2d_full[valid_mask] = points_2d_valid
        else:
            points_2d_full = points_2d_valid
        
        # 恢复原始形状
        if len(original_shape) == 3:
            points_2d_full = points_2d_full.reshape(original_shape[0], original_shape[1], 2)
            valid_mask = valid_mask.reshape(original_shape[0], original_shape[1])
        
        return points_2d_full, valid_mask
    
    def unproject_2d_to_3d(
        self,
        points_2d: np.ndarray,
        depth_map: np.ndarray
    ) -> np.ndarray:
        """
        将2D点反投影到3D空间
        
        Args:
            points_2d: 2D点坐标 [N, 2]
            depth_map: 深度图 [H, W]
            
        Returns:
            3D点坐标 [N, 3]
        """
        # 获取深度值
        u = points_2d[:, 0].astype(int)
        v = points_2d[:, 1].astype(int)
        
        # 确保坐标在图像范围内
        h, w = depth_map.shape
        u = np.clip(u, 0, w - 1)
        v = np.clip(v, 0, h - 1)
        
        depths = depth_map[v, u]
        
        # 计算3D坐标
        intrinsic_inv = np.linalg.inv(self.intrinsic_matrix)
        
        # 齐次坐标
        points_2d_homogeneous = np.column_stack([
            points_2d, np.ones(len(points_2d))
        ]).T
        
        # 反投影
        points_3d_normalized = intrinsic_inv @ points_2d_homogeneous
        points_3d = points_3d_normalized.T * depths.reshape(-1, 1)
        
        return points_3d


class Depth2DTo3DMapper:
    """
    深度图2D到3D映射工具
    处理深度图像和RGB图像之间的像素级对应关系
    """
    
    def __init__(self, image_size: Tuple[int, int] = (256, 256)):
        """
        初始化映射工具
        
        Args:
            image_size: 图像尺寸 (height, width)
        """
        self.image_size = image_size
        self.projection_tool = Camera3DProjection(image_size=image_size)
    
    def create_coordinate_grids(self) -> Tuple[np.ndarray, np.ndarray]:
        """
        创建坐标网格
        
        Returns:
            (u_grid, v_grid): u和v坐标网格
        """
        h, w = self.image_size
        u_coords = np.arange(w)
        v_coords = np.arange(h)
        u_grid, v_grid = np.meshgrid(u_coords, v_coords)
        
        return u_grid, v_grid
    
    def depth_to_pointcloud(
        self,
        depth_map: np.ndarray,
        rgb_image: Optional[np.ndarray] = None
    ) -> Tuple[np.ndarray, Optional[np.ndarray]]:
        """
        将深度图转换为点云
        
        Args:
            depth_map: 深度图 [H, W] 或 [H, W, 1]
            rgb_image: RGB图像 [H, W, 3]（可选）
            
        Returns:
            (pointcloud, colors): 点云坐标和颜色信息
        """
        if len(depth_map.shape) == 3:
            depth_map = depth_map[:, :, 0]
        
        h, w = depth_map.shape
        
        # 创建坐标网格
        u_grid, v_grid = self.create_coordinate_grids()
        
        # 将2D坐标转换为3D
        points_2d = np.stack([u_grid.flatten(), v_grid.flatten()], axis=1)
        points_3d = self.projection_tool.unproject_2d_to_3d(points_2d, depth_map)
        
        # 重新整形为[H, W, 3]
        pointcloud = points_3d.reshape(h, w, 3)
        
        # 处理颜色信息
        colors = None
        if rgb_image is not None:
            if rgb_image.shape[:2] == (h, w):
                colors = rgb_image
            else:
                colors = cv2.resize(rgb_image, (w, h))
        
        return pointcloud, colors
    
    def pointcloud_to_depth(
        self,
        pointcloud: np.ndarray,
        target_size: Optional[Tuple[int, int]] = None
    ) -> np.ndarray:
        """
        将点云转换为深度图
        
        Args:
            pointcloud: 点云 [H, W, 3] 或 [N, 3]
            target_size: 目标图像尺寸
            
        Returns:
            深度图 [H, W]
        """
        if target_size is None:
            target_size = self.image_size
        
        if len(pointcloud.shape) == 3:
            # 组织点云格式
            depth_map = pointcloud[:, :, 2]
            if depth_map.shape != target_size:
                depth_map = cv2.resize(depth_map, (target_size[1], target_size[0]))
        else:
            # 散点格式
            points_2d, valid_mask = self.projection_tool.project_3d_to_2d(pointcloud)
            
            # 创建深度图
            depth_map = np.zeros(target_size)
            
            valid_points_2d = points_2d[valid_mask].astype(int)
            valid_depths = pointcloud[valid_mask, 2]
            
            # 过滤在图像范围内的点
            h, w = target_size
            in_bounds = (
                (valid_points_2d[:, 0] >= 0) & (valid_points_2d[:, 0] < w) &
                (valid_points_2d[:, 1] >= 0) & (valid_points_2d[:, 1] < h)
            )
            
            valid_points_2d = valid_points_2d[in_bounds]
            valid_depths = valid_depths[in_bounds]
            
            # 填充深度图
            depth_map[valid_points_2d[:, 1], valid_points_2d[:, 0]] = valid_depths
        
        return depth_map
    
    def align_rgb_depth(
        self,
        rgb_image: np.ndarray,
        depth_map: np.ndarray,
        method: str = "resize"
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        对齐RGB图像和深度图
        
        Args:
            rgb_image: RGB图像
            depth_map: 深度图
            method: 对齐方法 ("resize", "crop", "pad")
            
        Returns:
            (aligned_rgb, aligned_depth): 对齐后的RGB图像和深度图
        """
        rgb_h, rgb_w = rgb_image.shape[:2]
        
        if len(depth_map.shape) == 3:
            depth_h, depth_w = depth_map.shape[:2]
        else:
            depth_h, depth_w = depth_map.shape
        
        target_h, target_w = self.image_size
        
        if method == "resize":
            # 简单缩放
            aligned_rgb = cv2.resize(rgb_image, (target_w, target_h))
            aligned_depth = cv2.resize(depth_map, (target_w, target_h))
            
        elif method == "crop":
            # 中心裁剪
            rgb_crop = self._center_crop(rgb_image, (target_h, target_w))
            depth_crop = self._center_crop(depth_map, (target_h, target_w))
            
            aligned_rgb = cv2.resize(rgb_crop, (target_w, target_h))
            aligned_depth = cv2.resize(depth_crop, (target_w, target_h))
            
        elif method == "pad":
            # 填充到目标尺寸
            aligned_rgb = self._pad_to_size(rgb_image, (target_h, target_w))
            aligned_depth = self._pad_to_size(depth_map, (target_h, target_w))
        
        else:
            raise ValueError(f"Unknown alignment method: {method}")
        
        return aligned_rgb, aligned_depth
    
    def _center_crop(self, image: np.ndarray, target_size: Tuple[int, int]) -> np.ndarray:
        """
        中心裁剪图像
        
        Args:
            image: 输入图像
            target_size: 目标尺寸 (height, width)
            
        Returns:
            裁剪后的图像
        """
        h, w = image.shape[:2]
        target_h, target_w = target_size
        
        start_h = max(0, (h - target_h) // 2)
        start_w = max(0, (w - target_w) // 2)
        
        end_h = min(h, start_h + target_h)
        end_w = min(w, start_w + target_w)
        
        if len(image.shape) == 3:
            return image[start_h:end_h, start_w:end_w, :]
        else:
            return image[start_h:end_h, start_w:end_w]
    
    def _pad_to_size(self, image: np.ndarray, target_size: Tuple[int, int]) -> np.ndarray:
        """
        填充图像到目标尺寸
        
        Args:
            image: 输入图像
            target_size: 目标尺寸 (height, width)
            
        Returns:
            填充后的图像
        """
        h, w = image.shape[:2]
        target_h, target_w = target_size
        
        pad_h = max(0, target_h - h)
        pad_w = max(0, target_w - w)
        
        pad_top = pad_h // 2
        pad_bottom = pad_h - pad_top
        pad_left = pad_w // 2
        pad_right = pad_w - pad_left
        
        if len(image.shape) == 3:
            padded = np.pad(
                image,
                ((pad_top, pad_bottom), (pad_left, pad_right), (0, 0)),
                mode='constant'
            )
        else:
            padded = np.pad(
                image,
                ((pad_top, pad_bottom), (pad_left, pad_right)),
                mode='constant'
            )
        
        # 如果还需要裁剪
        if padded.shape[0] > target_h or padded.shape[1] > target_w:
            padded = self._center_crop(padded, target_size)
        
        return padded


class AnomalyMaskProjector:
    """
    异常掩码投影工具
    处理2D异常掩码与3D点云异常之间的映射
    """
    
    def __init__(self, image_size: Tuple[int, int] = (224, 224)):
        """
        初始化投影工具
        
        Args:
            image_size: 图像尺寸
        """
        self.image_size = image_size
        self.mapper = Depth2DTo3DMapper(image_size)
    
    def project_2d_mask_to_3d(
        self,
        mask_2d: np.ndarray,
        depth_map: np.ndarray,
        threshold: float = 0.5
    ) -> np.ndarray:
        """
        将2D异常掩码投影到3D点云
        
        Args:
            mask_2d: 2D异常掩码 [H, W]
            depth_map: 深度图 [H, W]
            threshold: 掩码二值化阈值
            
        Returns:
            3D异常掩码 [H, W]（对应点云的每个点）
        """
        # 二值化掩码
        binary_mask = (mask_2d > threshold).astype(np.float32)
        
        # 应用深度约束
        if len(depth_map.shape) == 3:
            depth_single = depth_map[:, :, 0]
        else:
            depth_single = depth_map
        
        valid_depth = (depth_single > 0).astype(np.float32)
        
        # 3D掩码 = 2D掩码 * 深度有效性
        mask_3d = binary_mask * valid_depth
        
        return mask_3d
    
    def project_3d_mask_to_2d(
        self,
        mask_3d: np.ndarray,
        pointcloud: np.ndarray
    ) -> np.ndarray:
        """
        将3D异常掩码投影到2D图像
        
        Args:
            mask_3d: 3D异常掩码
            pointcloud: 对应的点云
            
        Returns:
            2D异常掩码 [H, W]
        """
        # 如果点云是组织格式，直接使用掩码
        if len(pointcloud.shape) == 3 and pointcloud.shape[:2] == mask_3d.shape[:2]:
            return mask_3d
        
        # 如果是散点格式，需要投影
        if len(pointcloud.shape) == 2:
            points_2d, valid_mask = self.mapper.projection_tool.project_3d_to_2d(pointcloud)
            
            # 创建2D掩码
            mask_2d = np.zeros(self.image_size)
            
            valid_points = points_2d[valid_mask].astype(int)
            valid_masks = mask_3d[valid_mask]
            
            # 过滤在图像范围内的点
            h, w = self.image_size
            in_bounds = (
                (valid_points[:, 0] >= 0) & (valid_points[:, 0] < w) &
                (valid_points[:, 1] >= 0) & (valid_points[:, 1] < h)
            )
            
            valid_points = valid_points[in_bounds]
            valid_masks = valid_masks[in_bounds]
            
            # 填充2D掩码
            mask_2d[valid_points[:, 1], valid_points[:, 0]] = valid_masks
            
            return mask_2d
        
        raise ValueError("Invalid pointcloud or mask format")
    
    def align_masks_with_depth(
        self,
        rgb_mask: np.ndarray,
        depth_mask: np.ndarray,
        depth_map: np.ndarray
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        基于深度信息对齐RGB和深度掩码
        
        Args:
            rgb_mask: RGB掩码
            depth_mask: 深度掩码
            depth_map: 深度图
            
        Returns:
            (aligned_rgb_mask, aligned_depth_mask): 对齐后的掩码
        """
        # 确保尺寸一致
        target_size = self.image_size
        
        if rgb_mask.shape != target_size:
            rgb_mask = cv2.resize(rgb_mask, (target_size[1], target_size[0]))
        
        if depth_mask.shape != target_size:
            depth_mask = cv2.resize(depth_mask, (target_size[1], target_size[0]))
        
        if depth_map.shape[:2] != target_size:
            if len(depth_map.shape) == 3:
                depth_map = cv2.resize(depth_map, (target_size[1], target_size[0]))
            else:
                depth_map = cv2.resize(depth_map, (target_size[1], target_size[0]))
        
        # 创建深度有效性掩码
        if len(depth_map.shape) == 3:
            depth_valid = (depth_map[:, :, 0] > 0).astype(np.float32)
        else:
            depth_valid = (depth_map > 0).astype(np.float32)
        
        # 对齐掩码：只在有效深度区域保留异常
        aligned_rgb_mask = rgb_mask * depth_valid
        aligned_depth_mask = depth_mask * depth_valid
        
        return aligned_rgb_mask, aligned_depth_mask


# 便捷函数
def load_mvtec3d_data(
    rgb_path: str,
    tiff_path: str,
    target_size: Tuple[int, int] = (224, 224)
) -> Tuple[np.ndarray, np.ndarray]:
    """
    加载MVTec 3D-AD数据
    
    Args:
        rgb_path: RGB图像路径
        tiff_path: TIFF深度图路径
        target_size: 目标尺寸
        
    Returns:
        (rgb_image, depth_map): RGB图像和深度图
    """
    # 加载RGB图像
    rgb_image = cv2.imread(rgb_path, cv2.IMREAD_COLOR)
    rgb_image = cv2.cvtColor(rgb_image, cv2.COLOR_BGR2RGB)
    rgb_image = cv2.resize(rgb_image, (target_size[1], target_size[0]))
    rgb_image = rgb_image.astype(np.float32) / 255.0
    
    # 加载深度图
    depth_data = tiff.imread(tiff_path)
    if len(depth_data.shape) == 3:
        depth_map = depth_data[:, :, 2]  # Z通道
    else:
        depth_map = depth_data
    
    # 归一化深度图
    depth_map = depth_map.astype(np.float32)
    if depth_map.max() > 1.0:
        depth_map = (depth_map - depth_map.min()) / (depth_map.max() - depth_map.min())
    
    depth_map = cv2.resize(depth_map, (target_size[1], target_size[0]))
    
    return rgb_image, depth_map


def create_depth_constrained_mask(
    perlin_mask: np.ndarray,
    depth_map: np.ndarray,
    depth_threshold: float = 0.001
) -> np.ndarray:
    """
    创建深度约束的异常掩码
    
    Args:
        perlin_mask: Perlin噪声掩码
        depth_map: 深度图
        depth_threshold: 深度阈值
        
    Returns:
        深度约束的异常掩码
    """
    if len(depth_map.shape) == 3:
        depth_single = depth_map[:, :, 0]
    else:
        depth_single = depth_map
    
    depth_valid = (depth_single > depth_threshold).astype(np.float32)
    constrained_mask = perlin_mask * depth_valid
    
    return constrained_mask


if __name__ == "__main__":
    # 测试代码
    print("Testing 2D-3D alignment tools...")
    
    # 创建测试数据
    image_size = (224, 224)
    
    # 测试相机投影
    projection = Camera3DProjection(image_size=image_size)
    
    # 创建测试3D点
    test_points_3d = np.random.rand(100, 3) * [10, 10, 5]  # 随机3D点
    test_points_3d[:, 2] += 1  # 确保深度为正
    
    points_2d, valid_mask = projection.project_3d_to_2d(test_points_3d)
    print(f"Projected {np.sum(valid_mask)}/{len(test_points_3d)} points to 2D")
    
    # 测试深度映射
    mapper = Depth2DTo3DMapper(image_size=image_size)
    
    # 创建测试深度图
    test_depth = np.random.rand(*image_size) * 5 + 1
    test_rgb = np.random.rand(*image_size, 3)
    
    pointcloud, colors = mapper.depth_to_pointcloud(test_depth, test_rgb)
    print(f"Created pointcloud with shape: {pointcloud.shape}")
    
    # 测试掩码投影
    projector = AnomalyMaskProjector(image_size=image_size)
    
    test_mask_2d = np.random.rand(*image_size) > 0.7  # 随机掩码
    mask_3d = projector.project_2d_mask_to_3d(test_mask_2d, test_depth)
    print(f"Created 3D mask with {np.sum(mask_3d)} anomaly points")
    
    print("All tests passed!")