"""
异常类型枚举和数据结构定义
为IA-CRF项目的伪异常生成系统提供统一的数据结构和类型定义

Author: IA-CRF Project
Date: 2025-08-26
"""

from enum import Enum, auto
from dataclasses import dataclass, field
from typing import Dict, List, Tuple, Optional, Union, Any
import numpy as np
from pathlib import Path


class AnomalyType(Enum):
    """异常类型枚举"""
    GEOMETRIC = "geometric"          # 几何异常（形状变形）
    MATERIAL = "material"            # 材质异常（纹理、颜色）
    STRUCTURAL = "structural"        # 结构异常（缺失、断裂）
    SURFACE = "surface"             # 表面异常（划痕、污渍）
    HYBRID = "hybrid"               # 混合异常


class GeometricAnomalySubType(Enum):
    """几何异常子类型"""
    DENT = "dent"                   # 凹陷
    BULGE = "bulge"                 # 凸起
    CRACK = "crack"                 # 裂纹
    HOLE = "hole"                   # 孔洞
    DEFORMATION = "deformation"     # 变形
    MISSING_PART = "missing_part"   # 缺失部分


class MaterialAnomalySubType(Enum):
    """材质异常子类型"""
    COLOR_VARIATION = "color_variation"     # 颜色变化
    TEXTURE_CHANGE = "texture_change"       # 纹理改变
    STAIN = "stain"                        # 污渍
    DISCOLORATION = "discoloration"        # 变色
    ALBEDO_ANOMALY = "albedo_anomaly"      # Albedo异常
    SHADING_ANOMALY = "shading_anomaly"    # 阴影异常


class SeverityLevel(Enum):
    """异常严重程度"""
    MINIMAL = 1      # 轻微
    MILD = 2         # 中等
    MODERATE = 3     # 明显
    SEVERE = 4       # 严重
    CRITICAL = 5     # 极严重


class GenerationMethod(Enum):
    """生成方法"""
    PERLIN_NOISE = "perlin_noise"           # Perlin噪声
    TEXTURE_BLENDING = "texture_blending"   # 纹理混合
    GEOMETRIC_TRANSFORM = "geometric_transform"  # 几何变换
    PHYSICS_SIMULATION = "physics_simulation"    # 物理模拟
    NEURAL_GENERATION = "neural_generation"      # 神经网络生成
    HYBRID_METHOD = "hybrid_method"              # 混合方法


@dataclass
class AnomalyRegion:
    """异常区域定义"""
    mask: np.ndarray                        # 异常掩码 [H, W]
    bbox: Tuple[int, int, int, int]         # 边界框 (x, y, w, h)
    center: Tuple[float, float]             # 中心点 (x, y)
    area: float                             # 面积（像素数）
    area_ratio: float                       # 面积占比
    shape_descriptors: Dict[str, float] = field(default_factory=dict)  # 形状描述符
    
    def __post_init__(self):
        """后初始化处理"""
        if self.area == 0:
            self.area = np.sum(self.mask)
        
        if self.area_ratio == 0:
            total_pixels = self.mask.shape[0] * self.mask.shape[1]
            self.area_ratio = self.area / total_pixels


@dataclass
class AnomalyParameters:
    """异常生成参数"""
    # 基础参数
    anomaly_type: AnomalyType
    sub_type: Optional[Union[GeometricAnomalySubType, MaterialAnomalySubType]] = None
    severity: SeverityLevel = SeverityLevel.MODERATE
    generation_method: GenerationMethod = GenerationMethod.PERLIN_NOISE
    
    # 空间参数
    position: Optional[Tuple[float, float]] = None  # 异常位置 (相对坐标 0-1)
    size: Optional[Tuple[float, float]] = None      # 异常尺寸 (相对尺寸 0-1)
    orientation: float = 0.0                        # 方向角度（度）
    
    # 生成参数
    intensity: float = 0.5                          # 异常强度 [0, 1]
    randomness: float = 0.3                         # 随机性程度 [0, 1]
    blend_factor: float = 0.5                       # 混合因子 [0, 1]
    
    # 特定参数
    specific_params: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'anomaly_type': self.anomaly_type.value,
            'sub_type': self.sub_type.value if self.sub_type else None,
            'severity': self.severity.value,
            'generation_method': self.generation_method.value,
            'position': self.position,
            'size': self.size,
            'orientation': self.orientation,
            'intensity': self.intensity,
            'randomness': self.randomness,
            'blend_factor': self.blend_factor,
            'specific_params': self.specific_params
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AnomalyParameters':
        """从字典创建"""
        return cls(
            anomaly_type=AnomalyType(data['anomaly_type']),
            sub_type=GeometricAnomalySubType(data['sub_type']) if data.get('sub_type') and data['anomaly_type'] == 'geometric' 
                     else MaterialAnomalySubType(data['sub_type']) if data.get('sub_type') else None,
            severity=SeverityLevel(data.get('severity', 3)),
            generation_method=GenerationMethod(data.get('generation_method', 'perlin_noise')),
            position=data.get('position'),
            size=data.get('size'),
            orientation=data.get('orientation', 0.0),
            intensity=data.get('intensity', 0.5),
            randomness=data.get('randomness', 0.3),
            blend_factor=data.get('blend_factor', 0.5),
            specific_params=data.get('specific_params', {})
        )


@dataclass
class AnomalyGenerationResult:
    """异常生成结果"""
    # 基础信息
    success: bool = True
    error_message: Optional[str] = None
    generation_time: float = 0.0
    
    # 输入数据
    original_data: Dict[str, np.ndarray] = field(default_factory=dict)
    parameters: Optional[AnomalyParameters] = None
    
    # 输出数据
    anomalous_data: Dict[str, np.ndarray] = field(default_factory=dict)
    anomaly_regions: List[AnomalyRegion] = field(default_factory=list)
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    # 统计信息
    statistics: Dict[str, float] = field(default_factory=dict)
    
    def add_statistic(self, key: str, value: float):
        """添加统计信息"""
        self.statistics[key] = value
    
    def get_anomaly_coverage(self) -> float:
        """获取异常覆盖率"""
        if not self.anomaly_regions:
            return 0.0
        
        total_area = sum(region.area for region in self.anomaly_regions)
        if 'original_mask_shape' in self.metadata:
            h, w = self.metadata['original_mask_shape']
            total_pixels = h * w
            return total_area / total_pixels
        
        return 0.0
    
    def get_anomaly_count(self) -> int:
        """获取异常区域数量"""
        return len(self.anomaly_regions)


@dataclass
class DatasetSample:
    """数据集样本"""
    # 标识信息
    sample_id: str
    category: str
    split: str  # 'train', 'test', 'val'
    
    # 数据路径
    rgb_path: Optional[str] = None
    depth_path: Optional[str] = None
    pointcloud_path: Optional[str] = None
    albedo_path: Optional[str] = None
    shading_path: Optional[str] = None
    mask_path: Optional[str] = None
    
    # 数据数组（如果已加载）
    rgb_data: Optional[np.ndarray] = None
    depth_data: Optional[np.ndarray] = None
    pointcloud_data: Optional[np.ndarray] = None
    albedo_data: Optional[np.ndarray] = None
    shading_data: Optional[np.ndarray] = None
    mask_data: Optional[np.ndarray] = None
    
    # 标签信息
    is_anomalous: bool = False
    anomaly_type: Optional[AnomalyType] = None
    anomaly_regions: List[AnomalyRegion] = field(default_factory=list)
    
    # 元数据
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def load_data(self, data_type: str) -> Optional[np.ndarray]:
        """加载指定类型的数据"""
        path_attr = f"{data_type}_path"
        data_attr = f"{data_type}_data"
        
        if hasattr(self, data_attr) and getattr(self, data_attr) is not None:
            return getattr(self, data_attr)
        
        if hasattr(self, path_attr):
            path = getattr(self, path_attr)
            if path and Path(path).exists():
                if data_type in ['rgb', 'albedo', 'shading', 'mask']:
                    import cv2
                    data = cv2.imread(path, cv2.IMREAD_COLOR if data_type != 'mask' else cv2.IMREAD_GRAYSCALE)
                    if data_type != 'mask':
                        data = cv2.cvtColor(data, cv2.COLOR_BGR2RGB)
                elif data_type == 'depth':
                    import tifffile as tiff
                    data = tiff.imread(path)
                elif data_type == 'pointcloud':
                    data = np.load(path)  # 假设是.npy格式
                else:
                    return None
                
                setattr(self, data_attr, data)
                return data
        
        return None
    
    def get_data_shape(self, data_type: str) -> Optional[Tuple[int, ...]]:
        """获取数据形状"""
        data = self.load_data(data_type)
        return data.shape if data is not None else None


@dataclass
class GenerationConfig:
    """生成配置"""
    # 基础配置
    output_dir: str
    dataset_name: str = "synthetic_anomalies"
    image_size: Tuple[int, int] = (224, 224)
    
    # 异常配置
    anomaly_types: List[AnomalyType] = field(default_factory=lambda: [AnomalyType.MATERIAL])
    anomaly_probability: float = 0.5
    max_anomalies_per_sample: int = 3
    
    # 质量控制
    min_anomaly_size: float = 0.01  # 最小异常尺寸（相对）
    max_anomaly_size: float = 0.3   # 最大异常尺寸（相对）
    quality_threshold: float = 0.7   # 质量阈值
    
    # 生成控制
    random_seed: Optional[int] = None
    num_workers: int = 4
    batch_size: int = 8
    
    # 输出控制
    save_intermediate: bool = False
    save_visualization: bool = False
    save_metadata: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'output_dir': self.output_dir,
            'dataset_name': self.dataset_name,
            'image_size': self.image_size,
            'anomaly_types': [at.value for at in self.anomaly_types],
            'anomaly_probability': self.anomaly_probability,
            'max_anomalies_per_sample': self.max_anomalies_per_sample,
            'min_anomaly_size': self.min_anomaly_size,
            'max_anomaly_size': self.max_anomaly_size,
            'quality_threshold': self.quality_threshold,
            'random_seed': self.random_seed,
            'num_workers': self.num_workers,
            'batch_size': self.batch_size,
            'save_intermediate': self.save_intermediate,
            'save_visualization': self.save_visualization,
            'save_metadata': self.save_metadata
        }


# MVTec 3D-AD特定定义
class MVTec3DCategory(Enum):
    """MVTec 3D-AD数据集类别"""
    BAGEL = "bagel"
    CABLE_GLAND = "cable_gland"
    CARROT = "carrot"
    COOKIE = "cookie"
    DOWEL = "dowel"
    FOAM = "foam"
    PEACH = "peach"
    POTATO = "potato"
    ROPE = "rope"
    TIRE = "tire"


# DTD纹理类别定义
class DTDTextureCategory(Enum):
    """DTD纹理数据集类别"""
    BANDED = "banded"
    BLOTCHY = "blotchy"
    BRAIDED = "braided"
    BUBBLY = "bubbly"
    BUMPY = "bumpy"
    CHEQUERED = "chequered"
    COBWEBBED = "cobwebbed"
    CRACKED = "cracked"
    CROSSHATCHED = "crosshatched"
    CRYSTALLINE = "crystalline"
    DOTTED = "dotted"
    FIBROUS = "fibrous"
    FLECKED = "flecked"
    FRECKLED = "freckled"
    FRILLY = "frilly"
    GAUZY = "gauzy"
    GRID = "grid"
    GROOVED = "grooved"
    HONEYCOMBED = "honeycombed"
    INTERLACED = "interlaced"
    KNITTED = "knitted"
    LACELIKE = "lacelike"
    LINED = "lined"
    MARBLED = "marbled"
    MATTED = "matted"
    MESHED = "meshed"
    PAISLEY = "paisley"
    PERFORATED = "perforated"
    PITTED = "pitted"
    PLEATED = "pleated"
    POLKA_DOTTED = "polka-dotted"
    POROUS = "porous"
    POTHOLED = "potholed"
    SCALY = "scaly"
    SMEARED = "smeared"
    SPIRALLED = "spiralled"
    SPRINKLED = "sprinkled"
    STAINED = "stained"
    STRATIFIED = "stratified"
    STRIPED = "striped"
    STUDDED = "studded"
    SWIRLY = "swirly"
    VEINED = "veined"
    WAFFLED = "waffled"
    WOVEN = "woven"
    WRINKLED = "wrinkled"
    ZIGZAGGED = "zigzagged"


# 异常类型与产品类别的推荐映射
MVTEC_ANOMALY_MAPPING = {
    MVTec3DCategory.BAGEL: {
        'common_anomalies': [MaterialAnomalySubType.STAIN, GeometricAnomalySubType.CRACK],
        'recommended_textures': [DTDTextureCategory.BUMPY, DTDTextureCategory.POROUS, DTDTextureCategory.CRACKED]
    },
    MVTec3DCategory.CABLE_GLAND: {
        'common_anomalies': [GeometricAnomalySubType.DEFORMATION, MaterialAnomalySubType.DISCOLORATION],
        'recommended_textures': [DTDTextureCategory.LINED, DTDTextureCategory.GROOVED, DTDTextureCategory.STRIPED]
    },
    MVTec3DCategory.CARROT: {
        'common_anomalies': [MaterialAnomalySubType.STAIN, GeometricAnomalySubType.DENT],
        'recommended_textures': [DTDTextureCategory.BUMPY, DTDTextureCategory.FRECKLED, DTDTextureCategory.PITTED]
    },
    MVTec3DCategory.COOKIE: {
        'common_anomalies': [GeometricAnomalySubType.CRACK, MaterialAnomalySubType.COLOR_VARIATION],
        'recommended_textures': [DTDTextureCategory.CRACKED, DTDTextureCategory.BUMPY, DTDTextureCategory.DOTTED]
    },
    MVTec3DCategory.DOWEL: {
        'common_anomalies': [GeometricAnomalySubType.CRACK, MaterialAnomalySubType.TEXTURE_CHANGE],
        'recommended_textures': [DTDTextureCategory.LINED, DTDTextureCategory.GROOVED, DTDTextureCategory.STRIPED]
    },
    MVTec3DCategory.FOAM: {
        'common_anomalies': [GeometricAnomalySubType.HOLE, MaterialAnomalySubType.TEXTURE_CHANGE],
        'recommended_textures': [DTDTextureCategory.POROUS, DTDTextureCategory.BUBBLY, DTDTextureCategory.HONEYCOMBED]
    },
    MVTec3DCategory.PEACH: {
        'common_anomalies': [MaterialAnomalySubType.STAIN, GeometricAnomalySubType.DENT],
        'recommended_textures': [DTDTextureCategory.BUMPY, DTDTextureCategory.FRECKLED, DTDTextureCategory.BLOTCHY]
    },
    MVTec3DCategory.POTATO: {
        'common_anomalies': [MaterialAnomalySubType.STAIN, GeometricAnomalySubType.DENT],
        'recommended_textures': [DTDTextureCategory.BUMPY, DTDTextureCategory.PITTED, DTDTextureCategory.BLOTCHY]
    },
    MVTec3DCategory.ROPE: {
        'common_anomalies': [GeometricAnomalySubType.DEFORMATION, MaterialAnomalySubType.TEXTURE_CHANGE],
        'recommended_textures': [DTDTextureCategory.BRAIDED, DTDTextureCategory.FIBROUS, DTDTextureCategory.WOVEN]
    },
    MVTec3DCategory.TIRE: {
        'common_anomalies': [GeometricAnomalySubType.CRACK, MaterialAnomalySubType.TEXTURE_CHANGE],
        'recommended_textures': [DTDTextureCategory.GROOVED, DTDTextureCategory.HONEYCOMBED, DTDTextureCategory.STRIPED]
    }
}


# 工具函数
def create_default_anomaly_parameters(
    anomaly_type: AnomalyType,
    category: Optional[MVTec3DCategory] = None
) -> AnomalyParameters:
    """
    创建默认异常参数
    
    Args:
        anomaly_type: 异常类型
        category: 产品类别
        
    Returns:
        默认异常参数
    """
    params = AnomalyParameters(anomaly_type=anomaly_type)
    
    if category and category in MVTEC_ANOMALY_MAPPING:
        mapping = MVTEC_ANOMALY_MAPPING[category]
        
        # 设置推荐的子类型
        if 'common_anomalies' in mapping and mapping['common_anomalies']:
            params.sub_type = mapping['common_anomalies'][0]
        
        # 设置特定参数
        if 'recommended_textures' in mapping:
            params.specific_params['recommended_textures'] = [
                tex.value for tex in mapping['recommended_textures']
            ]
    
    return params


def validate_anomaly_region(region: AnomalyRegion, min_size: float = 0.001) -> bool:
    """
    验证异常区域的有效性
    
    Args:
        region: 异常区域
        min_size: 最小尺寸阈值
        
    Returns:
        是否有效
    """
    return (
        region.area > 0 and
        region.area_ratio >= min_size and
        region.bbox[2] > 0 and  # width > 0
        region.bbox[3] > 0      # height > 0
    )


if __name__ == "__main__":
    # 测试代码
    print("Testing anomaly data structures...")
    
    # 测试异常参数
    params = create_default_anomaly_parameters(
        AnomalyType.MATERIAL, 
        MVTec3DCategory.BAGEL
    )
    print(f"Created parameters for bagel: {params.sub_type}")
    
    # 测试数据结构序列化
    params_dict = params.to_dict()
    params_restored = AnomalyParameters.from_dict(params_dict)
    print(f"Serialization test: {params.anomaly_type == params_restored.anomaly_type}")
    
    # 测试异常区域
    test_mask = np.random.rand(256, 256) > 0.8
    region = AnomalyRegion(
        mask=test_mask,
        bbox=(10, 10, 50, 50),
        center=(35, 35),
        area=0,  # 将自动计算
        area_ratio=0  # 将自动计算
    )
    print(f"Created region with area: {region.area}, ratio: {region.area_ratio:.4f}")
    
    # 测试验证
    is_valid = validate_anomaly_region(region)
    print(f"Region validation: {is_valid}")
    
    print("All tests passed!")