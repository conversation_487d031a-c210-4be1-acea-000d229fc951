"""
工具模块
伪异常生成的基础工具类集合

Author: IA-CRF Project  
Date: 2025-08-26
"""

from .perlin_noise import (
    PerlinNoiseGenerator,
    EasyNetPerlinGenerator,
    rand_perlin_2d_np,
    generate_fractal_noise_2d
)

from .alignment_tools import (
    Camera3DProjection,
    Depth2DTo3DMapper,
    AnomalyMaskProjector,
    load_mvtec3d_data,
    create_depth_constrained_mask
)

from .data_structures import (
    AnomalyType,
    GeometricAnomalySubType,
    MaterialAnomalySubType,
    SeverityLevel,
    GenerationMethod,
    AnomalyRegion,
    AnomalyParameters,
    AnomalyGenerationResult,
    DatasetSample,
    GenerationConfig,
    MVTec3DCategory,
    DTDTextureCategory,
    MVTEC_ANOMALY_MAPPING,
    create_default_anomaly_parameters,
    validate_anomaly_region
)

__all__ = [
    # Perlin噪声生成
    'PerlinNoiseGenerator',
    'EasyNetPerlinGenerator',
    'rand_perlin_2d_np', 
    'generate_fractal_noise_2d',
    
    # 2D-3D对齐工具
    'Camera3DProjection',
    'Depth2DTo3DMapper',
    'AnomalyMaskProjector',
    'load_mvtec3d_data',
    'create_depth_constrained_mask',
    
    # 数据结构和枚举
    'AnomalyType',
    'GeometricAnomalySubType',
    'MaterialAnomalySubType',
    'SeverityLevel',
    'GenerationMethod',
    'AnomalyRegion',
    'AnomalyParameters',
    'AnomalyGenerationResult',
    'DatasetSample',
    'GenerationConfig',
    'MVTec3DCategory',
    'DTDTextureCategory',
    'MVTEC_ANOMALY_MAPPING',
    'create_default_anomaly_parameters',
    'validate_anomaly_region'
]

__version__ = "1.0.0"
__author__ = "IA-CRF Project"