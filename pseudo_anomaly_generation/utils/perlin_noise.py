"""
Perlin噪声生成工具类
基于EasyNet项目的实现，用于生成异常区域掩码

Author: IA-CRF Project
Date: 2025-08-26
"""

import torch
import math
import numpy as np
from typing import Tuple, Optional, Union
import cv2


def lerp_np(x: np.ndarray, y: np.ndarray, w: np.ndarray) -> np.ndarray:
    """
    NumPy版本的线性插值
    
    Args:
        x: 起始值
        y: 结束值
        w: 插值权重
        
    Returns:
        插值结果
    """
    return (y - x) * w + x


def fade_function(t: np.ndarray) -> np.ndarray:
    """
    Perlin噪声的淡化函数
    使用6t^5 - 15t^4 + 10t^3平滑插值
    
    Args:
        t: 输入值
        
    Returns:
        淡化后的值
    """
    return 6 * t ** 5 - 15 * t ** 4 + 10 * t ** 3


class PerlinNoiseGenerator:
    """
    Perlin噪声生成器
    基于EasyNet的实现，用于生成自然的异常区域掩码
    """
    
    def __init__(self, seed: Optional[int] = None):
        """
        初始化Perlin噪声生成器
        
        Args:
            seed: 随机种子，用于可重现的结果
        """
        if seed is not None:
            np.random.seed(seed)
            torch.manual_seed(seed)
    
    def generate_2d_perlin_noise(
        self, 
        shape: Tuple[int, int], 
        resolution: Tuple[int, int]
    ) -> np.ndarray:
        """
        生成2D Perlin噪声
        基于EasyNet的rand_perlin_2d_np实现
        
        Args:
            shape: 输出噪声的形状 (height, width)
            resolution: 噪声分辨率 (res_x, res_y)
            
        Returns:
            Perlin噪声数组，值域约为[-1, 1]
        """
        delta = (resolution[0] / shape[0], resolution[1] / shape[1])
        d = (shape[0] // resolution[0], shape[1] // resolution[1])
        
        # 创建网格
        grid = np.mgrid[0:resolution[0]:delta[0], 0:resolution[1]:delta[1]].transpose(1, 2, 0) % 1
        
        # 生成随机梯度
        angles = 2 * math.pi * np.random.rand(resolution[0] + 1, resolution[1] + 1)
        gradients = np.stack((np.cos(angles), np.sin(angles)), axis=-1)
        
        # 梯度平铺函数
        def tile_grads(slice1, slice2):
            return np.repeat(
                np.repeat(
                    gradients[slice1[0]:slice1[1], slice2[0]:slice2[1]], 
                    d[0], axis=0
                ), 
                d[1], axis=1
            )
        
        # 点积函数
        def dot(grad, shift):
            shifted_grid = np.stack((
                grid[:shape[0], :shape[1], 0] + shift[0], 
                grid[:shape[0], :shape[1], 1] + shift[1]
            ), axis=-1)
            return (shifted_grid * grad[:shape[0], :shape[1]]).sum(axis=-1)
        
        # 计算四个角的点积
        n00 = dot(tile_grads([0, -1], [0, -1]), [0, 0])
        n10 = dot(tile_grads([1, None], [0, -1]), [-1, 0])
        n01 = dot(tile_grads([0, -1], [1, None]), [0, -1])
        n11 = dot(tile_grads([1, None], [1, None]), [-1, -1])
        
        # 应用淡化函数
        t = fade_function(grid[:shape[0], :shape[1]])
        
        # 双线性插值
        noise = math.sqrt(2) * lerp_np(
            lerp_np(n00, n10, t[..., 0]), 
            lerp_np(n01, n11, t[..., 0]), 
            t[..., 1]
        )
        
        return noise
    
    def generate_fractal_noise_2d(
        self,
        shape: Tuple[int, int],
        base_resolution: Tuple[int, int],
        octaves: int = 1,
        persistence: float = 0.5
    ) -> np.ndarray:
        """
        生成分形Perlin噪声（多个八度音的叠加）
        
        Args:
            shape: 输出形状
            base_resolution: 基础分辨率
            octaves: 八度音数量
            persistence: 持续性（每个八度音的振幅衰减）
            
        Returns:
            分形噪声数组
        """
        noise = np.zeros(shape)
        frequency = 1
        amplitude = 1
        max_amplitude = 0
        
        for _ in range(octaves):
            current_res = (frequency * base_resolution[0], frequency * base_resolution[1])
            noise += amplitude * self.generate_2d_perlin_noise(shape, current_res)
            max_amplitude += amplitude
            frequency *= 2
            amplitude *= persistence
        
        # 归一化到[-1, 1]
        noise /= max_amplitude
        return noise
    
    def generate_random_scale_noise(
        self,
        shape: Tuple[int, int],
        min_scale: int = 0,
        max_scale: int = 6
    ) -> np.ndarray:
        """
        生成随机尺度的Perlin噪声
        基于EasyNet的实现，随机选择分辨率
        
        Args:
            shape: 输出形状
            min_scale: 最小尺度指数
            max_scale: 最大尺度指数
            
        Returns:
            Perlin噪声数组
        """
        # 随机生成分辨率（2的幂次方）
        perlin_scalex = 2 ** np.random.randint(min_scale, max_scale + 1)
        perlin_scaley = 2 ** np.random.randint(min_scale, max_scale + 1)
        
        return self.generate_2d_perlin_noise(shape, (perlin_scalex, perlin_scaley))
    
    def create_binary_mask(
        self,
        noise: np.ndarray,
        threshold: float = 0.5,
        rotation_range: Tuple[int, int] = (-90, 90)
    ) -> np.ndarray:
        """
        从Perlin噪声创建二值掩码
        
        Args:
            noise: Perlin噪声数组
            threshold: 二值化阈值
            rotation_range: 随机旋转角度范围
            
        Returns:
            二值掩码数组 (0或1)
        """
        # 随机旋转
        if rotation_range != (0, 0):
            angle = np.random.uniform(rotation_range[0], rotation_range[1])
            center = (noise.shape[1] // 2, noise.shape[0] // 2)
            rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
            noise = cv2.warpAffine(noise, rotation_matrix, (noise.shape[1], noise.shape[0]))
        
        # 二值化
        binary_mask = np.where(noise > threshold, 1.0, 0.0)
        
        return binary_mask.astype(np.float32)
    
    def create_depth_constrained_mask(
        self,
        perlin_noise: np.ndarray,
        depth_map: np.ndarray,
        depth_threshold: float = 0.001,
        noise_threshold: float = 0.5
    ) -> np.ndarray:
        """
        创建深度约束的异常掩码
        基于EasyNet的实现，只在有效深度区域生成异常
        
        Args:
            perlin_noise: Perlin噪声
            depth_map: 深度图 (单通道或三通道)
            depth_threshold: 深度有效性阈值
            noise_threshold: 噪声二值化阈值
            
        Returns:
            深度约束的异常掩码
        """
        # 处理深度图
        if len(depth_map.shape) == 3:
            # 如果是三通道深度图，取任意一个通道
            depth_single = depth_map[:, :, 0]
        else:
            depth_single = depth_map
        
        # 创建深度掩码
        depth_mask = np.where(depth_single > depth_threshold, 1.0, 0.0)
        
        # 创建Perlin噪声掩码
        perlin_mask = np.where(perlin_noise > noise_threshold, 1.0, 0.0)
        
        # 组合掩码：只在有效深度区域应用Perlin掩码
        combined_mask = depth_mask * perlin_mask
        
        return combined_mask.astype(np.float32)


class EasyNetPerlinGenerator(PerlinNoiseGenerator):
    """
    EasyNet风格的Perlin噪声生成器
    完全复制EasyNet的生成逻辑
    """
    
    def generate_easynet_mask(
        self,
        shape: Tuple[int, int],
        depth_map: np.ndarray,
        min_perlin_scale: int = 0,
        perlin_scale: int = 6,
        threshold: float = 0.5,
        depth_threshold: float = 0.001,
        rotation_range: Tuple[int, int] = (-90, 90)
    ) -> Tuple[np.ndarray, np.ndarray]:
        """
        生成EasyNet风格的异常掩码
        严格按照EasyNet的augment_image函数实现
        
        Args:
            shape: 输出形状 (height, width)
            depth_map: 深度图
            min_perlin_scale: 最小Perlin尺度
            perlin_scale: 最大Perlin尺度
            threshold: Perlin二值化阈值
            depth_threshold: 深度有效性阈值
            rotation_range: 旋转角度范围
            
        Returns:
            (perlin_noise, anomaly_mask): Perlin噪声和最终异常掩码
        """
        # 1. 创建深度掩码
        if len(depth_map.shape) == 3:
            depth_single = depth_map[:, :, 0]
        else:
            depth_single = depth_map
        
        depth_mask = np.where(depth_single > depth_threshold, 1.0, 0.0)
        
        # 2. 生成Perlin噪声（严格按照EasyNet实现）
        perlin_scalex = 2 ** (torch.randint(min_perlin_scale, perlin_scale, (1,)).numpy()[0])
        perlin_scaley = 2 ** (torch.randint(min_perlin_scale, perlin_scale, (1,)).numpy()[0])
        
        perlin_noise = self.generate_2d_perlin_noise(shape, (perlin_scalex, perlin_scaley))
        
        # 3. 随机旋转Perlin噪声
        if rotation_range != (0, 0):
            angle = np.random.uniform(rotation_range[0], rotation_range[1])
            center = (shape[1] // 2, shape[0] // 2)
            rotation_matrix = cv2.getRotationMatrix2D(center, angle, 1.0)
            perlin_noise = cv2.warpAffine(
                perlin_noise, rotation_matrix, (shape[1], shape[0])
            )
        
        # 4. 二值化Perlin噪声
        perlin_thr = np.where(perlin_noise > threshold, 1.0, 0.0)
        
        # 5. 应用深度约束
        mask_zzz = depth_mask * perlin_thr
        
        # 6. 处理维度以匹配EasyNet格式
        perlin_noise = perlin_noise.astype(np.float32)
        if len(perlin_noise.shape) == 2:
            perlin_noise = np.expand_dims(perlin_noise, axis=2)
        
        if len(mask_zzz.shape) == 2:
            mask_zzz = np.expand_dims(mask_zzz, axis=2)
        
        return perlin_noise, mask_zzz


# 为了兼容性，提供一些便捷函数
def rand_perlin_2d_np(shape: Tuple[int, int], res: Tuple[int, int]) -> np.ndarray:
    """
    EasyNet兼容函数：生成2D Perlin噪声
    
    Args:
        shape: 输出形状
        res: 分辨率
        
    Returns:
        Perlin噪声数组
    """
    generator = PerlinNoiseGenerator()
    return generator.generate_2d_perlin_noise(shape, res)


def generate_fractal_noise_2d(
    shape: Tuple[int, int], 
    res: Tuple[int, int], 
    octaves: int = 1, 
    persistence: float = 0.5
) -> np.ndarray:
    """
    EasyNet兼容函数：生成分形噪声
    
    Args:
        shape: 输出形状
        res: 基础分辨率
        octaves: 八度音数量
        persistence: 持续性
        
    Returns:
        分形噪声数组
    """
    generator = PerlinNoiseGenerator()
    return generator.generate_fractal_noise_2d(shape, res, octaves, persistence)


if __name__ == "__main__":
    # 测试代码
    generator = EasyNetPerlinGenerator()
    
    # 生成测试噪声
    shape = (224, 224)
    noise = generator.generate_random_scale_noise(shape)
    print(f"Generated Perlin noise with shape: {noise.shape}")
    print(f"Value range: [{noise.min():.3f}, {noise.max():.3f}]")
    
    # 创建测试深度图
    depth_map = np.ones(shape) * 0.5  # 模拟有效深度
    
    # 生成EasyNet风格掩码
    perlin_noise, anomaly_mask = generator.generate_easynet_mask(shape, depth_map)
    print(f"Generated anomaly mask with shape: {anomaly_mask.shape}")
    print(f"Anomaly coverage: {np.sum(anomaly_mask) / anomaly_mask.size * 100:.2f}%")