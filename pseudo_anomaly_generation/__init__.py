"""
伪异常生成模块
基于EasyNet方法的因果一致性伪异常生成系统

Author: IA-CRF Project
Date: 2025-08-26
"""

# 材质异常生成
from .material import (
    AlbedoMaterialAnomalyGenerator,
    DTDTextureAnomalyGenerator,
    MaterialAnomalyGenerationSystem
)

# 工具模块
from .utils import (
    PerlinNoiseGenerator,
    EasyNetPerlinGenerator,
    rand_perlin_2d_np,
    generate_fractal_noise_2d,
    Camera3DProjection,
    Depth2DTo3DMapper,
    AnomalyMaskProjector,
    load_mvtec3d_data,
    create_depth_constrained_mask,
    AnomalyType,
    GeometricAnomalySubType,
    MaterialAnomalySubType,
    SeverityLevel,
    GenerationMethod,
    AnomalyRegion,
    AnomalyParameters,
    AnomalyGenerationResult,
    DatasetSample,
    GenerationConfig,
    MVTec3DCategory,
    DTDTextureCategory,
    MVTEC_ANOMALY_MAPPING,
    create_default_anomaly_parameters,
    validate_anomaly_region
)

# 配置模块
from .configs import (
    get_config,
    validate_config,
    MATERIAL_ANOMALY_CONFIG,
    PRESET_CONFIGS
)

__all__ = [
    # 材质异常生成
    'AlbedoMaterialAnomalyGenerator',
    'DTDTextureAnomalyGenerator', 
    'MaterialAnomalyGenerationSystem',
    
    # 工具模块
    'PerlinNoiseGenerator',
    'EasyNetPerlinGenerator',
    'rand_perlin_2d_np',
    'generate_fractal_noise_2d',
    'Camera3DProjection',
    'Depth2DTo3DMapper',
    'AnomalyMaskProjector',
    'load_mvtec3d_data',
    'create_depth_constrained_mask',
    
    # 数据结构和枚举
    'AnomalyType',
    'GeometricAnomalySubType',
    'MaterialAnomalySubType',
    'SeverityLevel',
    'GenerationMethod',
    'AnomalyRegion',
    'AnomalyParameters',
    'AnomalyGenerationResult',
    'DatasetSample',
    'GenerationConfig',
    'MVTec3DCategory',
    'DTDTextureCategory',
    'MVTEC_ANOMALY_MAPPING',
    'create_default_anomaly_parameters',
    'validate_anomaly_region',
    
    # 配置模块
    'get_config',
    'validate_config',
    'MATERIAL_ANOMALY_CONFIG',
    'PRESET_CONFIGS'
]

__version__ = "1.0.0"
__author__ = "IA-CRF Project"
__description__ = "Causally Consistent Pseudo Anomaly Generation Module based on EasyNet methods"