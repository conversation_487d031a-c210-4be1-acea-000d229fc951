import numpy as np

def check_npy_shape(file_path):
    """
    加载.npy文件并输出其shape
    
    参数:
        file_path (str): .npy文件的路径
    """
    try:
        # 加载.npy文件
        data = np.load(file_path)
        
        # 输出shape信息
        print(f".npy文件的shape为: {data.shape}")
        
        # 可选：输出数据类型
        print(f"数据类型为: {data.dtype}")
        
        return data.shape
        
    except FileNotFoundError:
        print(f"错误: 找不到文件 '{file_path}'")
    except Exception as e:
        print(f"加载文件时发生错误: {str(e)}")

# 使用示例
if __name__ == "__main__":
    # 替换为你的.npy文件路径
    npy_file_path = "/raid/liulinna/projects/M3DM/datasets/mvtec3d/bagel/train/good/xyz/features_224x224/000_features.npy"
    check_npy_shape(npy_file_path)
