#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU Selection and Information Tool
Provides utilities for GPU detection, selection, and monitoring
"""

import torch
import psutil
import GPUtil
from typing import Dict, List, Optional
import argparse


def get_system_info() -> Dict:
    """Get system information"""
    return {
        'cpu_count': psutil.cpu_count(),
        'memory_total': psutil.virtual_memory().total / (1024**3),  # GB
        'memory_available': psutil.virtual_memory().available / (1024**3)  # GB
    }


def get_detailed_gpu_info() -> List[Dict]:
    """Get detailed GPU information"""
    gpu_info = []
    
    if not torch.cuda.is_available():
        print("CUDA is not available on this system.")
        return gpu_info
    
    try:
        # Use GPUtil for additional information
        gpus = GPUtil.getGPUs()
        
        for i in range(torch.cuda.device_count()):
            props = torch.cuda.get_device_properties(i)
            
            # Get current memory usage
            torch.cuda.set_device(i)
            memory_allocated = torch.cuda.memory_allocated(i) / (1024**3)
            memory_reserved = torch.cuda.memory_reserved(i) / (1024**3)
            memory_total = props.total_memory / (1024**3)
            memory_free = memory_total - memory_reserved
            
            # Get GPU utilization if GPUtil is available
            gpu_util = 0
            memory_util = 0
            temperature = 0
            
            if i < len(gpus):
                gpu_util = gpus[i].load * 100
                memory_util = gpus[i].memoryUtil * 100
                temperature = gpus[i].temperature
            
            gpu_info.append({
                'id': i,
                'name': props.name,
                'compute_capability': f"{props.major}.{props.minor}",
                'memory_total': memory_total,
                'memory_allocated': memory_allocated,
                'memory_reserved': memory_reserved,
                'memory_free': memory_free,
                'multiprocessor_count': props.multi_processor_count,
                'max_threads_per_multiprocessor': props.max_threads_per_multiprocessor,
                'gpu_utilization': gpu_util,
                'memory_utilization': memory_util,
                'temperature': temperature
            })
    
    except ImportError:
        # Fallback if GPUtil is not available
        print("GPUtil not available, using basic GPU information")
        
        for i in range(torch.cuda.device_count()):
            props = torch.cuda.get_device_properties(i)
            
            torch.cuda.set_device(i)
            memory_allocated = torch.cuda.memory_allocated(i) / (1024**3)
            memory_reserved = torch.cuda.memory_reserved(i) / (1024**3)
            memory_total = props.total_memory / (1024**3)
            memory_free = memory_total - memory_reserved
            
            gpu_info.append({
                'id': i,
                'name': props.name,
                'compute_capability': f"{props.major}.{props.minor}",
                'memory_total': memory_total,
                'memory_allocated': memory_allocated,
                'memory_reserved': memory_reserved,
                'memory_free': memory_free,
                'multiprocessor_count': props.multi_processor_count,
                'max_threads_per_multiprocessor': props.max_threads_per_multiprocessor,
                'gpu_utilization': 0,
                'memory_utilization': 0,
                'temperature': 0
            })
    
    except Exception as e:
        print(f"Error getting GPU information: {e}")
    
    return gpu_info


def display_gpu_info(gpu_info: List[Dict]):
    """Display formatted GPU information"""
    if not gpu_info:
        print("No GPUs available.")
        return
    
    print(f"\n{'='*80}")
    print("GPU Information")
    print(f"{'='*80}")
    
    for gpu in gpu_info:
        print(f"\nGPU {gpu['id']}: {gpu['name']}")
        print(f"  Compute Capability: {gpu['compute_capability']}")
        print(f"  Multiprocessors: {gpu['multiprocessor_count']}")
        print(f"  Max Threads/MP: {gpu['max_threads_per_multiprocessor']}")
        
        print(f"\n  Memory:")
        print(f"    Total: {gpu['memory_total']:.1f} GB")
        print(f"    Allocated: {gpu['memory_allocated']:.2f} GB")
        print(f"    Reserved: {gpu['memory_reserved']:.2f} GB")
        print(f"    Free: {gpu['memory_free']:.1f} GB")
        
        if gpu['gpu_utilization'] > 0 or gpu['memory_utilization'] > 0:
            print(f"\n  Utilization:")
            print(f"    GPU: {gpu['gpu_utilization']:.1f}%")
            print(f"    Memory: {gpu['memory_utilization']:.1f}%")
            
        if gpu['temperature'] > 0:
            print(f"    Temperature: {gpu['temperature']}°C")


def recommend_gpu_for_training(gpu_info: List[Dict], 
                              min_memory: float = 4.0,
                              max_utilization: float = 30.0) -> Optional[int]:
    """
    Recommend best GPU for training based on criteria
    
    Args:
        gpu_info: List of GPU information
        min_memory: Minimum required free memory in GB
        max_utilization: Maximum acceptable GPU utilization %
        
    Returns:
        Recommended GPU ID or None
    """
    if not gpu_info:
        return None
    
    # Filter suitable GPUs
    suitable_gpus = []
    
    for gpu in gpu_info:
        if (gpu['memory_free'] >= min_memory and 
            gpu['gpu_utilization'] <= max_utilization):
            suitable_gpus.append(gpu)
    
    if not suitable_gpus:
        print(f"\nNo suitable GPU found with {min_memory}GB+ free memory "
              f"and <{max_utilization}% utilization.")
        return None
    
    # Score GPUs (higher is better)
    def score_gpu(gpu):
        memory_score = gpu['memory_free']
        utilization_score = (100 - gpu['gpu_utilization']) / 100
        compute_score = float(gpu['compute_capability'])
        
        return memory_score * 0.5 + utilization_score * 0.3 + compute_score * 0.2
    
    # Select best GPU
    best_gpu = max(suitable_gpus, key=score_gpu)
    
    print(f"\nRecommended GPU: {best_gpu['id']} ({best_gpu['name']})")
    print(f"  Free Memory: {best_gpu['memory_free']:.1f} GB")
    print(f"  Utilization: {best_gpu['gpu_utilization']:.1f}%")
    
    return best_gpu['id']


def monitor_gpu_usage(gpu_id: int, duration: int = 10):
    """
    Monitor GPU usage for a specified duration
    
    Args:
        gpu_id: GPU ID to monitor
        duration: Monitoring duration in seconds
    """
    import time
    
    print(f"\nMonitoring GPU {gpu_id} for {duration} seconds...")
    print("Time\t\tGPU%\tMemory%\tTemp°C\tMemory(GB)")
    print("-" * 60)
    
    try:
        for i in range(duration):
            gpu_info = get_detailed_gpu_info()
            
            if gpu_id < len(gpu_info):
                gpu = gpu_info[gpu_id]
                timestamp = time.strftime("%H:%M:%S")
                
                print(f"{timestamp}\t"
                      f"{gpu['gpu_utilization']:5.1f}\t"
                      f"{gpu['memory_utilization']:6.1f}\t"
                      f"{gpu['temperature']:5.0f}\t"
                      f"{gpu['memory_allocated']:.2f}/{gpu['memory_total']:.1f}")
            
            time.sleep(1)
            
    except KeyboardInterrupt:
        print("\nMonitoring stopped by user.")


def clear_gpu_memory(gpu_id: Optional[int] = None):
    """Clear GPU memory cache"""
    if not torch.cuda.is_available():
        print("CUDA not available.")
        return
    
    if gpu_id is not None:
        torch.cuda.set_device(gpu_id)
        torch.cuda.empty_cache()
        print(f"Cleared cache for GPU {gpu_id}")
    else:
        for i in range(torch.cuda.device_count()):
            torch.cuda.set_device(i)
            torch.cuda.empty_cache()
        print(f"Cleared cache for all {torch.cuda.device_count()} GPUs")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='GPU Selection and Information Tool')
    
    parser.add_argument('--show_info', action='store_true',
                        help='Show detailed GPU information')
    
    parser.add_argument('--recommend', action='store_true',
                        help='Recommend best GPU for training')
    
    parser.add_argument('--min_memory', type=float, default=4.0,
                        help='Minimum required GPU memory in GB')
    
    parser.add_argument('--monitor', type=int, default=None,
                        help='Monitor specific GPU (specify GPU ID)')
    
    parser.add_argument('--duration', type=int, default=10,
                        help='Monitoring duration in seconds')
    
    parser.add_argument('--clear_cache', type=int, default=None, nargs='?', const=-1,
                        help='Clear GPU memory cache (specify GPU ID or leave empty for all)')
    
    parser.add_argument('--system_info', action='store_true',
                        help='Show system information')
    
    args = parser.parse_args()
    
    # Show system info
    if args.system_info:
        sys_info = get_system_info()
        print(f"\n{'='*80}")
        print("System Information")
        print(f"{'='*80}")
        print(f"CPU Cores: {sys_info['cpu_count']}")
        print(f"RAM Total: {sys_info['memory_total']:.1f} GB")
        print(f"RAM Available: {sys_info['memory_available']:.1f} GB")
    
    # Get GPU info
    gpu_info = get_detailed_gpu_info()
    
    # Show GPU info
    if args.show_info or len(sys.argv) == 1:
        display_gpu_info(gpu_info)
    
    # Recommend GPU
    if args.recommend:
        recommend_gpu_for_training(gpu_info, min_memory=args.min_memory)
    
    # Monitor GPU
    if args.monitor is not None:
        if args.monitor < len(gpu_info):
            monitor_gpu_usage(args.monitor, args.duration)
        else:
            print(f"GPU {args.monitor} not found.")
    
    # Clear cache
    if args.clear_cache is not None:
        if args.clear_cache == -1:
            clear_gpu_memory()
        else:
            clear_gpu_memory(args.clear_cache)


if __name__ == "__main__":
    main()