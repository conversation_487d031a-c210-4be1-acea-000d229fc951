#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
IA-CRF Network Stage 2 Training Script
End-to-end multi-task cooperative training for anomaly detection

Author: IA-CRF Project
Date: 2025-08-27
"""

import os
import sys
import yaml
import time
import argparse
import numpy as np
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from tqdm import tqdm

import torch
import torch.nn as nn
from torch.utils.data import DataLoader

# TensorBoard import with fallback
try:
    from torch.utils.tensorboard import SummaryWriter
    TENSORBOARD_AVAILABLE = True
except ImportError:
    TENSORBOARD_AVAILABLE = False
    
    class SummaryWriter:
        def __init__(self, *args, **kwargs): pass
        def add_scalar(self, *args, **kwargs): pass
        def add_scalars(self, *args, **kwargs): pass
        def add_text(self, *args, **kwargs): pass
        def close(self): pass

# Add project path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from model.iacrf_network import IACRFNetwork
from model.multitask_core import MultiTaskCore
from model.multitask_losses import IACRFMultiTaskLoss
from data.iacrf_dataset import get_iacrf_dataloader


class IACRFTrainer:
    """
    IA-CRF Network Second-stage Trainer
    Handles end-to-end multi-task cooperative training
    """
    
    def __init__(self, config: Dict, category: str):
        self.config = config
        self.category = category
        self.device = self._setup_device()
        
        # Apply category-specific configurations
        self._apply_category_config()
        
        # Create directories
        self._create_directories()
        
        # Initialize data loaders
        self.train_loader, self.val_loader = self._create_data_loaders()
        
        # Validate training loader
        if self.train_loader is None:
            raise RuntimeError("Training data loader is None. Cannot proceed with training.")
        
        # Initialize network
        self.network = self._create_network()
        
        # Initialize loss function
        self.loss_fn = IACRFMultiTaskLoss(config)
        
        # Initialize multi-task core
        self.multitask_core = MultiTaskCore(self.network, config)
        
        # Initialize logging
        self.writer = self._create_tensorboard_writer()
        
        # Training state
        self.current_epoch = 0
        self.best_loss = float('inf')
        self.training_stats = []
    
    def _setup_device(self) -> torch.device:
        """Setup training device"""
        device_config = self.config['training']['device']
        
        if device_config == 'auto':
            if torch.cuda.is_available():
                device = torch.device('cuda')
            else:
                device = torch.device('cpu')
        else:
            device = torch.device(device_config)
        
        return device
    
    def _apply_category_config(self):
        """Apply category-specific configuration overrides"""
        category_config = self.config['categories'].get(self.category, {})
        
        # Apply category-specific training parameters
        if 'training' in category_config:
            for key, value in category_config['training'].items():
                if key in self.config['training']:
                    self.config['training'][key] = value
    
    def _create_directories(self):
        """Create necessary directories"""
        # Format paths with category
        self.checkpoint_dir = Path(self.config['checkpoint']['save_dir'].format(category=self.category))
        self.log_dir = Path(self.config['logging']['tensorboard_dir'].format(category=self.category))
        
        if 'visualization_dir' in self.config.get('validation', {}):
            self.viz_dir = Path(self.config['validation']['visualization_dir'].format(category=self.category))
            self.viz_dir.mkdir(parents=True, exist_ok=True)
        
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        self.log_dir.mkdir(parents=True, exist_ok=True)
    
    def _create_data_loaders(self) -> Tuple[DataLoader, Optional[DataLoader]]:
        """Create training and validation data loaders with integrated pseudo anomaly generation"""
        try:
            # 获取纹理路径（如果启用材质异常生成）
            texture_path = None
            if (self.config['pseudo_anomaly']['enable'] and 
                self.config['pseudo_anomaly']['material'].get('texture_path')):
                texture_path = self.config['pseudo_anomaly']['material']['texture_path']
                if texture_path and not os.path.exists(texture_path):
                    print(f"Warning: Texture path does not exist: {texture_path}")
                    texture_path = None
            
            # Training data loader with integrated pseudo anomaly generation
            train_loader = get_iacrf_dataloader(
                split='train',
                class_name=self.category,
                dataset_path=self.config['dataset']['dataset_path'],
                config=self.config,
                batch_size=self.config['training']['batch_size'],
                shuffle=True,
                num_workers=self.config['training']['num_workers'],
                enable_pseudo_anomaly=self.config['pseudo_anomaly']['enable'],
                anomaly_source_path=texture_path
            )
            
            if train_loader is None:
                raise RuntimeError("Failed to create training data loader")
            
            print(f"Training loader created successfully: {len(train_loader.dataset)} samples")
            
            # Validation data loader (optional, without pseudo anomaly generation)
            val_loader = None
            if self.config['validation']['frequency'] > 0:
                try:
                    val_loader = get_iacrf_dataloader(
                        split='test',
                        class_name=self.category,
                        dataset_path=self.config['dataset']['dataset_path'],
                        config=self.config,
                        batch_size=1,  # Use batch_size=1 for validation
                        shuffle=False,
                        num_workers=self.config['training']['num_workers'],
                        enable_pseudo_anomaly=False,  # Disable for validation
                        anomaly_source_path=None
                    )
                    if val_loader:
                        print(f"Validation loader created successfully: {len(val_loader.dataset)} samples")
                except Exception as e:
                    print(f"Warning: Could not create validation loader: {e}")
                    val_loader = None
            
            return train_loader, val_loader
            
        except Exception as e:
            print(f"Error creating data loaders: {e}")
            print(f"Please check:")
            print(f"  1. Dataset path exists: {self.config['dataset']['dataset_path']}")
            print(f"  2. Category '{self.category}' exists in dataset")
            print(f"  3. Required data files (RGB, XYZ) are present")
            print(f"  4. Permissions to access the dataset directory")
            raise RuntimeError(f"Failed to create data loaders: {e}")
    

    
    def _create_network(self) -> IACRFNetwork:
        """Create IA-CRF Network"""
        network = IACRFNetwork(self.config).to(self.device)
        
        # Load pre-trained VAE checkpoint
        vae_checkpoint_path = self.config['checkpoint']['vae_checkpoint_path'].format(category=self.category)
        network.load_vae_checkpoint(vae_checkpoint_path)
        
        return network
    
    def _create_tensorboard_writer(self):
        """Create TensorBoard writer"""
        if TENSORBOARD_AVAILABLE:
            return SummaryWriter(str(self.log_dir))
        else:
            return SummaryWriter()
    
    def _prepare_training_data(self, batch: Dict) -> Dict:
        """
        准备训练数据，使用数据集集成的伪异常生成结果
        
        Args:
            batch: 来自数据加载器的批次数据，已包含伪异常生成结果
            
        Returns:
            Dict: 格式化的训练数据
        """
        # 数据集已经生成了伪异常，直接使用
        # batch包含: augmented_albedo, augmented_shading, augmented_enhanced_pointcloud
        # 以及原始数据: albedo, shading, enhanced_pointcloud
        
        return {
            'corrupted_albedo': batch['augmented_albedo'],
            'corrupted_shading': batch['augmented_shading'], 
            'corrupted_pointcloud': batch['augmented_enhanced_pointcloud'],
            'clean_albedo': batch['albedo'],
            'clean_shading': batch['shading'],
            'clean_pointcloud': batch['enhanced_pointcloud'],
            # 保留异常信息用于可选的损失计算
            'geometric_anomaly_mask': batch.get('geometric_anomaly_mask', None),
            'material_anomaly_mask': batch.get('material_anomaly_mask', None),
            'has_geometric_anomaly': batch.get('has_geometric_anomaly', None),
            'has_material_anomaly': batch.get('has_material_anomaly', None),
            'anomaly_type': batch.get('anomaly_type', 'none')
        }
    
    def train_epoch(self, epoch: int) -> Dict:
        """Train one epoch"""
        self.network.train()
        epoch_metrics = {
            'total_loss': 0,
            'num_batches': 0,
            'num_samples': 0
        }
        
        # Progress bar
        pbar = tqdm(self.train_loader, desc=f"Epoch {epoch}")
        
        for batch_idx, batch in enumerate(pbar):
            try:
                # Move data to device
                for key in batch:
                    if isinstance(batch[key], torch.Tensor):
                        batch[key] = batch[key].to(self.device)
                
                # 准备训练数据（数据集已集成伪异常生成）
                training_data = self._prepare_training_data(batch)
                
                # Forward pass
                predictions = self.network(training_data)
                
                # Compute losses
                losses, loss_names, loss_details = self.loss_fn(predictions, training_data)
                
                # Multi-task training step
                step_info = self.multitask_core.training_step(epoch, losses, loss_names)
                
                # Update metrics
                epoch_metrics['total_loss'] += step_info['total_loss']
                epoch_metrics['num_batches'] += 1
                epoch_metrics['num_samples'] += batch['albedo'].size(0)
                
                # Update progress bar
                pbar.set_postfix({
                    'Loss': f"{step_info['total_loss']:.4f}",
                    'LR': f"{step_info['learning_rate']:.2e}",
                    'GradNorm': f"{step_info['gradient_norm']:.3f}"
                })
                
                # TensorBoard logging
                if batch_idx % self.config['logging']['log_frequency'] == 0:
                    global_step = epoch * len(self.train_loader) + batch_idx
                    
                    # Log training metrics to TensorBoard
                    self.writer.add_scalar('Loss/Total', step_info['total_loss'], global_step)
                    self.writer.add_scalar('Learning_Rate', step_info['learning_rate'], global_step)
                    self.writer.add_scalar('Gradient_Norm', step_info['gradient_norm'], global_step)
                    
                    # Log individual loss components
                    for loss_name in loss_names:
                        if loss_name in step_info:
                            self.writer.add_scalar(f'Loss/{loss_name}', step_info[loss_name], global_step)
                    
                    # Log epoch progress
                    progress = (batch_idx + 1) / len(self.train_loader)
                    self.writer.add_scalar('Progress/Epoch', progress, global_step)
                    self.writer.add_scalar('Progress/Global_Epoch', epoch + progress, global_step)
                
            except Exception as e:
                print(f"\n❗ 训练错误在epoch {epoch}, batch {batch_idx}: {str(e)}")
                import traceback
                traceback.print_exc()
                # Log errors to TensorBoard instead of printing
                if hasattr(self, 'writer'):
                    self.writer.add_text('Errors/Training', f'Epoch {epoch}, Batch {batch_idx}: {str(e)}', epoch)
                continue
        
        # Compute epoch averages
        if epoch_metrics['num_batches'] > 0:
            epoch_metrics['total_loss'] /= epoch_metrics['num_batches']
        
        return epoch_metrics
    
    def validate(self, epoch: int) -> Dict:
        """Validate the model"""
        if self.val_loader is None:
            return {}
        
        self.network.eval()
        val_metrics = {
            'total_loss': 0,
            'num_batches': 0
        }
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(self.val_loader):
                try:
                    # Move data to device
                    for key in batch:
                        if isinstance(batch[key], torch.Tensor):
                            batch[key] = batch[key].to(self.device)
                    
                    # Use original data as both input and target for validation
                    validation_data = {
                        'corrupted_albedo': batch['albedo'],
                        'corrupted_shading': batch['shading'],
                        'corrupted_pointcloud': batch['enhanced_pointcloud'],
                        'clean_albedo': batch['albedo'],
                        'clean_shading': batch['shading'],
                        'clean_pointcloud': batch['enhanced_pointcloud']
                    }
                    
                    # Forward pass
                    predictions = self.network(validation_data)
                    
                    # Compute losses
                    losses, loss_names, loss_details = self.loss_fn(predictions, validation_data)
                    
                    # Compute total loss
                    total_loss = sum(losses).item()
                    val_metrics['total_loss'] += total_loss
                    val_metrics['num_batches'] += 1
                    
                except Exception as e:
                    # Log validation errors to TensorBoard
                    if hasattr(self, 'writer'):
                        self.writer.add_text('Errors/Validation', f'Epoch {epoch}, Batch {batch_idx}: {str(e)}', epoch)
                    continue
        
        # Compute averages
        if val_metrics['num_batches'] > 0:
            val_metrics['total_loss'] /= val_metrics['num_batches']
            
            # Log validation metrics
            self.writer.add_scalar('Validation/Total_Loss', val_metrics['total_loss'], epoch)
        
        return val_metrics
    
    def save_checkpoint(self, epoch: int, is_best: bool = False):
        """Save training checkpoint"""
        checkpoint_path = self.checkpoint_dir / f"checkpoint_epoch_{epoch}.pth"
        best_path = self.checkpoint_dir / "best_model.pth"
        
        # Save regular checkpoint
        self.multitask_core.save_checkpoint(
            epoch=epoch,
            save_path=str(checkpoint_path),
            additional_info={
                'category': self.category,
                'best_loss': self.best_loss,
                'training_stats': self.training_stats
            }
        )
        
        # Save best model
        if is_best:
            self.multitask_core.save_checkpoint(
                epoch=epoch,
                save_path=str(best_path),
                additional_info={
                    'category': self.category,
                    'best_loss': self.best_loss,
                    'training_stats': self.training_stats
                }
            )
        
        # Cleanup old checkpoints
        self._cleanup_checkpoints()
    
    def _cleanup_checkpoints(self):
        """Remove old checkpoints, keeping only the last N"""
        keep_last_n = self.config['checkpoint']['keep_last_n']
        
        checkpoints = list(self.checkpoint_dir.glob("checkpoint_epoch_*.pth"))
        checkpoints.sort(key=lambda x: int(x.stem.split('_')[-1]))
        
        if len(checkpoints) > keep_last_n:
            for checkpoint in checkpoints[:-keep_last_n]:
                checkpoint.unlink()
    
    def train(self):
        """Main training loop"""
        # Simple startup message
        print(f"Starting IA-CRF training: {self.category} | {self.config['training']['epochs']} epochs")
        
        start_time = time.time()
        
        for epoch in range(self.config['training']['epochs']):
            self.current_epoch = epoch
            
            # Training
            train_metrics = self.train_epoch(epoch)
            
            # Validation
            val_metrics = {}
            if epoch % self.config['validation']['frequency'] == 0:
                val_metrics = self.validate(epoch)
            
            # Update training stats
            epoch_stats = {
                'epoch': epoch,
                'train_loss': train_metrics.get('total_loss', 0),
                'val_loss': val_metrics.get('total_loss', 0),
                'timestamp': datetime.now().isoformat()
            }
            self.training_stats.append(epoch_stats)
            
            # Log epoch metrics to TensorBoard
            self.writer.add_scalar('Epoch/Train_Loss', train_metrics.get('total_loss', 0), epoch)
            if val_metrics:
                self.writer.add_scalar('Epoch/Val_Loss', val_metrics.get('total_loss', 0), epoch)
            
            # Check for best model
            current_loss = val_metrics.get('total_loss', train_metrics.get('total_loss', float('inf')))
            is_best = current_loss < self.best_loss
            if is_best:
                self.best_loss = current_loss
                # Log best model update to TensorBoard
                self.writer.add_scalar('Best/Loss', self.best_loss, epoch)
                self.writer.add_text('Best/Update', f'New best model at epoch {epoch}', epoch)
            
            # Save checkpoint
            if epoch % self.config['checkpoint']['save_frequency'] == 0 or is_best:
                self.save_checkpoint(epoch, is_best)
            
            # Early stopping check
            if self.multitask_core.should_early_stop():
                self.writer.add_text('Training/EarlyStopping', f'Early stopping triggered at epoch {epoch}', epoch)
                break
        
        # Training completed
        total_time = time.time() - start_time
        print(f"Training completed! Time: {total_time/3600:.1f}h | Best loss: {self.best_loss:.6f}")
        
        # Log final statistics to TensorBoard
        self.writer.add_scalar('Training/Total_Time_Hours', total_time/3600, 0)
        self.writer.add_scalar('Training/Final_Best_Loss', self.best_loss, 0)
        self.writer.add_text('Training/Summary', 
                           f'Training completed in {total_time/3600:.1f}h with best loss {self.best_loss:.6f}', 0)
        
        # Close writer
        self.writer.close()


def create_trainer_from_config(config_path: str, category: str) -> IACRFTrainer:
    """Create trainer from configuration file"""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    
    # Set category in config
    config['dataset']['category'] = category
    
    return IACRFTrainer(config, category)


def main():
    """Main training function"""
    parser = argparse.ArgumentParser(description='IA-CRF Stage 2 Training')
    parser.add_argument('--config', type=str, default='configs/iacrf_training_config.yaml',
                       help='Path to configuration file')
    parser.add_argument('--category', type=str, default="bagel",
                       help='MVTec 3D-AD category to train on')
    parser.add_argument('--resume', type=str, default=None,
                       help='Path to checkpoint to resume from')
    parser.add_argument('--verbose', action='store_true',
                       help='Enable verbose logging')
    parser.add_argument('--num_scales', type=int, default=1, choices=[1, 2, 3],
                       help='Number of scales for 3D feature extraction (1, 2, or 3)')
    parser.add_argument('--texture_path', type=str, default="/raid/liulinna/projects/EasyNet/datasets/dtd/images/",
                       help='Path to texture images directory for material anomaly generation')
    parser.add_argument('--dataset_path', type=str, default="/raid/liulinna/projects/M3DM/datasets/mvtec3d/",
                       help='Path to MVTec 3D-AD dataset directory')
    
    args = parser.parse_args()
    
    # Load configuration
    with open(args.config, 'r') as f:
        config = yaml.safe_load(f)
    
    # Override configuration with command line arguments
    if args.verbose:
        config['logging']['verbose'] = True
    
    # 设置尺度数量
    config['num_scales'] = args.num_scales
    
    # 设置数据集路径（如果提供）
    if args.dataset_path:
        config['dataset']['dataset_path'] = args.dataset_path
    
    # 设置纹理路径（如果提供）
    if args.texture_path:
        config['pseudo_anomaly']['material']['texture_path'] = args.texture_path
    
    # Create trainer
    trainer = create_trainer_from_config(args.config, args.category)
    
    # Resume from checkpoint if specified
    if args.resume:
        trainer.multitask_core.load_checkpoint(args.resume)
    
    # Start training
    trainer.train()


if __name__ == "__main__":
    main()